/*// <copyright file="TestController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

#if DEBUG

using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Mvc;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.DataManager.Users;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.SettingsModels;
using NetProGroup.Trust.Forms;
using Swashbuckle.AspNetCore.Annotations;
using TCIG.ProjectsDashboard.Domain.Shared.User;

namespace TCIG.ProjectsDashboard.API.Areas.Tools.Controllers
{
    /// <summary>
    /// Use this controller for testing.
    /// </summary>
    [ApiController]
    [Area("Tools")]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class TestController : APIControllerBase
    {
        private readonly ILogger _logger;
        private IServiceProvider _serviceProvider;
        private IWorkContext _workContext;

        /// <summary>
        /// Initializes a new instance of the <see cref="TestController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="serviceProvider">An instance of IServiceProvider.</param>
        public TestController(ILogger<TestController> logger, IWorkContext workContext, IServiceProvider serviceProvider)
            : base(logger)
        {
            _logger = logger;
            _workContext = workContext;

            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Endpoint to create some test data like jurisdictions, masterclients, companies, modules etc.
        /// </summary>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        [HttpPost("create-data")]
        [SwaggerOperation(OperationId = "Tools_CreateData")]
        public async Task<ActionResult> CreateData()
        {
            if (!_workContext.IdentityUserId.HasValue)
            {
                _workContext.IdentityUserId = UserConsts.SystemUserId;
            }

            CreateUsers();

            SetupJurisdictions();
            SetupModules();
            SetupMasterClients();

            //AssignMasterClientToJurisdiction("JURI1", "MCC-1");
            //AssignMasterClientToJurisdiction("JURI1", "MCC-2");
            //AssignMasterClientToJurisdiction("JURI2", "MCC-3");
            //AssignMasterClientToJurisdiction("JURI3", "MCC-4");

            AssignModuleToJurisdiction("JURI1", "MOD1");
            AssignModuleToJurisdiction("JURI1", "MOD2");
            AssignModuleToJurisdiction("JURI1", "MOD3");
            AssignModuleToJurisdiction("JURI2", "MOD1");
            AssignModuleToJurisdiction("JURI2", "MOD2");
            AssignModuleToJurisdiction("JURI3", "MOD2");
            AssignModuleToJurisdiction("JURI3", "MOD3");

            AssignModuleToJurisdiction("Nevis", "BODirectors");
            AssignModuleToJurisdiction("Nevis", "SimplifiedTaxReturn");

            var jurisdiction = "nevis";

            CreateCompany(jurisdiction, "A", "MCC-1");
            CreateCompany(jurisdiction, "B", "MCC-1");
            CreateCompany(jurisdiction, "C", "MCC-2");
            CreateCompany(jurisdiction, "D", "MCC-3");
            CreateCompany(jurisdiction, "E", "MCC-3");
            CreateCompany(jurisdiction, "F", "MCC-4");
            CreateCompany(jurisdiction, "G", "MCC-4");
            CreateCompany(jurisdiction, "H", "MCC-4");

            CreateCompany(jurisdiction, "Nevis 1-1", "NEV-1");
            CreateCompany(jurisdiction, "Nevis 1-2", "NEV-1");
            CreateCompany(jurisdiction, "Nevis 1-3", "NEV-1");

            CreateCompany(jurisdiction, "Nevis 2-1", "NEV-2");
            CreateCompany(jurisdiction, "Nevis 2-2", "NEV-2");
            CreateCompany(jurisdiction, "Nevis 2-3", "NEV-2");

            CreateCompany(jurisdiction, "ABCD-1", "ABCD");
            CreateCompany(jurisdiction, "ABCD-2", "ABCD");
            CreateCompany(jurisdiction, "ABCD-3", "ABCD");
            CreateCompany(jurisdiction, "ABCD-4", "ABCD");

            CreateFormTemplatesForJurisdiction("Nevis", "SimplifiedTaxReturn", 2019, 2020, 2021, 2022, 2023, 2024);

            AssignUsersToMasterClient("NEV-1");
            AssignUsersToMasterClient("NEV-2");
            AssignUsersToMasterClient("ABCD");

            CreateInvoiceSettings();

            return Ok();
        }*/

        private void CreateUsers()
        {
            CreateUser("Monica", "netprogroup.com", 10);
        }

        private void CreateUser(string name, string domain, int total)
        {
            IUsersDataManager usersManager = _serviceProvider.GetRequiredService<IUsersDataManager>();
            for (int ix = 0; ix < total; ix++)
            {
                var email = $"{name}+{ix.ToString("00")}@{domain}";

                var user = new NetProGroup.Trust.Application.Contracts.Users.CreateUserDTO
                {
                    Email = email,
                    DisplayName = name
                };
                usersManager.CreateUserAsync(user).Wait();
            }
        }

        private void SetupModules()
        {
            CreateModule("MOD1", "Module 1");
            CreateModule("MOD2", "Module 2");
            CreateModule("MOD3", "Module 3");

            CreateModule("SimplifiedTaxReturn", "Simplified Tax Return");
            CreateModule("BODirectors", "BO/Directors");
        }

        private void SetupJurisdictions()
        {
            CreateJurisdiction("JURI1", "Jurisdiction 1");
            CreateJurisdiction("JURI2", "Jurisdiction 2");
            CreateJurisdiction("JURI3", "Jurisdiction 3");
        }

        private void SetupMasterClients()
        {
            CreateMasterClient("JURI1", "MCC-1");
            CreateMasterClient("JURI1", "MCC-2");
            CreateMasterClient("JURI2", "MCC-3");
            CreateMasterClient("JURI3", "MCC-4");

            CreateMasterClient("Nevis", "ABCD");
            CreateMasterClient("Nevis", "NEV-1");
            CreateMasterClient("Nevis", "NEV-2");
            CreateMasterClient("Nevis", "ABCD");
        }

        //private void AssignMasterClientToJurisdiction(string jurisdictionCode, string masterClientCode)
        //{
        //    var masterClientRepository = _serviceProvider.GetRequiredService<IMasterClientsRepository>();
        //    var masterClient = masterClientRepository.FindFirstOrDefaultByCondition(x => x.Code == masterClientCode);

        //    var jurisdictionRepository = _serviceProvider.GetRequiredService<IJurisdictionsRepository>();
        //    var jurisdiction = jurisdictionRepository.FindFirstOrDefaultByCondition(x => x.Code == jurisdictionCode);

        //    if (masterClient.JurisdictionId != jurisdiction.Id)
        //    {
        //        masterClient.JurisdictionId = jurisdiction.Id;
        //        masterClientRepository.SaveChanges();
        //    }
        //}

        private void AssignModuleToJurisdiction(string jurisdictionCode, string moduleKey)
        {
            var moduleRepository = _serviceProvider.GetRequiredService<IModulesRepository>();
            var module = moduleRepository.FindFirstOrDefaultByCondition(x => x.Key == moduleKey);

            var jurisdictionRepository = _serviceProvider.GetRequiredService<IJurisdictionsRepository>();
            var jurisdiction = jurisdictionRepository.FindFirstOrDefaultByCondition(x => x.Code == jurisdictionCode, q => q.Include(x => x.JurisdictionModules));

            if (!jurisdiction.JurisdictionModules.Any(x => x.ModuleId == module.Id))
            {
                jurisdiction.JurisdictionModules.Add(new JurisdictionModule(jurisdiction.Id, module.Id));
                jurisdictionRepository.SaveChanges();
            }
        }

        private Guid CreateModule(string key, string name)
        {
            var repository = _serviceProvider.GetRequiredService<IModulesRepository>();

            var existing = repository.FindFirstOrDefaultByCondition(x => x.Key == key);
            if (existing == null)
            {
                var toDb = new Module(Guid.NewGuid(), key, name);
                repository.Insert(toDb, saveChanges: true);
                return toDb.Id;
            }
            else
            {
                return existing.Id;
            }
        }

        private Guid CreateJurisdiction(string code, string name)
        {
            var repository = _serviceProvider.GetRequiredService<IJurisdictionsRepository>();

            var existing = repository.FindFirstOrDefaultByCondition(x => x.Code == code);
            if (existing == null)
            {
                var toDb = new Jurisdiction(Guid.NewGuid(), code, name);
                repository.Insert(toDb, saveChanges: true);
                return toDb.Id;
            }
            else
            {
                return existing.Id;
            }
        }

        private Guid CreateMasterClient(string jurisdictionCode, string code)
        {
            var repository = _serviceProvider.GetRequiredService<IMasterClientsRepository>();

            var existing = repository.FindFirstOrDefaultByCondition(x => x.Code == code);
            if (existing == null)
            {
                var jurisdictionRepository = _serviceProvider.GetRequiredService<IJurisdictionsRepository>();
                var jurisdiction = jurisdictionRepository.FindFirstOrDefaultByCondition(x => x.Code == jurisdictionCode);

//                var toDb = new MasterClient(Guid.NewGuid(), code);
//                repository.Insert(toDb, saveChanges: true);
//                return toDb.Id;
//            }
//            else
//            {
//                return existing.Id;
//            }
//        }

//        private Guid CreateCompany(string jurisdictionCode, string key, string masterClientCode)
//        {
//            var jurisdictionsRepository = _serviceProvider.GetRequiredService<IJurisdictionsRepository>();
//            var masterClientRepository = _serviceProvider.GetRequiredService<IMasterClientsRepository>();
//            var masterClient = masterClientRepository.FindFirstOrDefaultByCondition(x => x.Code == masterClientCode);

//            var jurisdiction = jurisdictionsRepository.FindFirstOrDefaultByCondition(j => j.Code == "nevis");

//            var repository = _serviceProvider.GetRequiredService<ILegalEntitiesRepository>();

//            var code = $"Code {key}";
//            var existing = repository.FindFirstOrDefaultByCondition(x => x.Code == code);
//            if (existing == null)
//            {
//                var dataManager = _serviceProvider.GetRequiredService<ILegalEntitiesDataManager>();

//                var companyDTO = dataManager.CreateCompanyAsync(new NetProGroup.Trust.Application.Contracts.LegalEntities.Companies.CreateCompanyDTO
//                {
//                    Code = code,
//                    Name = $"Company {key}",
//                    MasterClientId = masterClient.Id,
//                    JurisdictionId = jurisdiction.Id,
//                    IsActive = true,
//                }, true).Result;

//                CreateBOs(companyDTO.Id, companyDTO.Code, companyDTO.Name);

//                return companyDTO.Id;
//            }
//            else
//            {
//                existing.JurisdictionId = jurisdiction.Id;
//                existing.IsActive = true;
//                repository.SaveChanges();

//                CreateBOs(existing.Id, existing.Code, existing.Name);

//                return existing.Id;
//            }
//        }

//        private void CreateFormTemplatesForJurisdiction(string jurisdictionCode, string moduleKey, params int[] years)
//        {
//            var moduleRepository = _serviceProvider.GetRequiredService<IModulesRepository>();
//            var module = moduleRepository.FindFirstOrDefaultByCondition(x => x.Key == moduleKey);

//            var jurisdictionRepository = _serviceProvider.GetRequiredService<IJurisdictionsRepository>();
//            var jurisdiction = jurisdictionRepository.FindFirstOrDefaultByCondition(x => x.Code == jurisdictionCode, q => q.Include(x => x.JurisdictionModules));

//            var formTemplatesRepository = _serviceProvider.GetRequiredService<IFormTemplatesRepository>();

//            var formTemplate = formTemplatesRepository.FindFirstOrDefaultByCondition(ft => ft.JurisdictionId == jurisdiction.Id && ft.ModuleId == module.Id, q => q.Include(ft => ft.FormTemplateVersions));
//            if (formTemplate == null)
//            {
//                formTemplate = new FormTemplate(Guid.NewGuid())
//                {
//                    JurisdictionId = jurisdiction.Id,
//                    ModuleId = module.Id,
//                    Key = moduleKey,
//                    Name = module.Name + " template",
//                };
//                formTemplatesRepository.Insert(formTemplate, true);
//            }

//            foreach (var year in years)
//            {
//                var formTemplateVersion = formTemplate.FormTemplateVersions.FirstOrDefault(ftv => ftv.Year == year);
//                if (formTemplateVersion == null)
//                {
//                    formTemplateVersion = new FormTemplateVersion { Name = year.ToString(), Version = year.ToString(), Year = year, StartAt = null };
//                    formTemplate.FormTemplateVersions.Add(formTemplateVersion);
//                }
//                else
//                {
//                    formTemplateVersion.Name = $"{formTemplate.Name} {year.ToString()}";
//                    formTemplateVersion.Version = year.ToString();
//                    formTemplateVersion.Year = year;
//                    formTemplateVersion.StartAt = null;
//                    formTemplateVersion.Year = year;
//                }

//                var sampleKeyValueForm = new NetProGroup.Trust.Forms.Forms.KeyValueForm();
//                sampleKeyValueForm.Id = $"{moduleKey.ToLower()}.{year}";
//                sampleKeyValueForm.Name = $"{moduleKey}.{year}";
//                sampleKeyValueForm.Version = "1";
//                sampleKeyValueForm.CreatedAt = DateTime.UtcNow;
//                sampleKeyValueForm.CreatedBy = "TestController";
//                sampleKeyValueForm.Description = $"Sample template for module {moduleKey}, year {year} (jurisdiction {jurisdiction.Name})";

//                sampleKeyValueForm.DataSet.Add("key1", "Value 1");
//                sampleKeyValueForm.DataSet.Add("key2", "Value 2");
//                sampleKeyValueForm.DataSet.Add("key3", "Value 3");

//                //if (string.IsNullOrEmpty(formTemplateVersion.DataAsJson))
//                {
//                    var bldr = new FormBuilder();
//                    bldr.Form = sampleKeyValueForm;
//                    formTemplateVersion.DataAsJson = bldr.ToJson();
//                }
//            }
//            formTemplatesRepository.Update(formTemplate, true);
//        }

//        private void AssignUsersToMasterClient(string code)
//        {
//            var repository = _serviceProvider.GetRequiredService<IMasterClientsRepository>();

//            var existing = repository.FindFirstOrDefaultByCondition(x => x.Code == code, q => q.Include(x => x.MasterClientUsers));

//            var userRepository = _serviceProvider.GetRequiredService<IUserRepository>();
//            var users = userRepository.SearchAsync().Result;

//            foreach (var user in users)
//            {
//                if (!existing.MasterClientUsers.Any(x => x.UserId == user.Id))
//                {
//                    existing.MasterClientUsers.Add(new MasterClientUser { UserId = user.Id });
//                }
//            }

//            repository.SaveChanges();
//        }

//        private void CreateInvoiceSettings()
//        {
//            var settingsManager = _serviceProvider.GetRequiredService<ISettingsManager>();
//            var jurisdictionRepository = _serviceProvider.GetRequiredService<IJurisdictionsRepository>();
//            var jurisdiction = jurisdictionRepository.FindFirstOrDefaultByCondition(x => x.Code == "nevis");

//            var feeSettings = new FeeSettingsDTO
//            {
//                STRSubmissionFee = 150,
//                STRSubmissionFeeInvoiceText = "Submission fee for year {year}"
//            };
//            settingsManager.SaveSettingsForJurisdictionAsync(feeSettings, jurisdiction.Id).Wait();

//            var numberingSettings = new InvoiceNumberingSettingsDTO
//            {
//                InitialNumber = 4000,
//                PrefixFormat = "{yy}{mm}",
//                RangeFormat = "{yy}{mm}",
//                FullInvoiceNumberFormat = "{prefix}/{number}"
//            };
//            settingsManager.SaveInvoiceNumberingSettingsAsync(jurisdiction.Id, null, numberingSettings).Wait();

//            var lastInvoiceNumberData = settingsManager.GetLastInvoiceNumberAsync(jurisdiction.Id, null).Result;

//            if (lastInvoiceNumberData == null || lastInvoiceNumberData.FullInvoiceNumber == null)
//            {
//                lastInvoiceNumberData = new InvoiceNumberData
//                {
//                    RangeIdentifier = "2405",
//                    InvoiceNumber = 4015
//                };
//                settingsManager.SetLastInvoiceNumberAsync(jurisdiction.Id, null, lastInvoiceNumberData).Wait();
//            }
//        }

//        private void CreateBOs(Guid legalEntityId, string companyNumber, string prefix)
//        {
//            var beneficialOwnersDataManager = _serviceProvider.GetRequiredService<IBeneficialOwnersDataManager>();

//            var request = new NetProGroup.Trust.DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses.SyncBeneficialOwnerRequest
//            {
//                LegalEntityId = legalEntityId
//            };

//            for (var i = 0; i < 20; i++)
//            {
//                request.BeneficialOwners.Add(SetupBO(companyNumber, prefix, i + 1));
//            };

//            beneficialOwnersDataManager.SyncBeneficialOwnersAsync(request).Wait();
//        }

//        private NetProGroup.Trust.DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses.SyncBeneficialOwner SetupBO(string companyNumber, string prefix, int sequence)
//        {
//            var result = new NetProGroup.Trust.DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses.SyncBeneficialOwner
//            {
//                CompanyNumber = companyNumber,
//                UniqueRelationId = $"BO-{prefix}-{sequence}",
//                Name = $"BO for {prefix}-{sequence}",
//                OfficerTypeName = "Alternate Director",
//                FileType = sequence > 10 ? "company" : "individual",
//                RelationType = "Director"

//            };
//            return result;
//        }
//    }
//}

//#endif