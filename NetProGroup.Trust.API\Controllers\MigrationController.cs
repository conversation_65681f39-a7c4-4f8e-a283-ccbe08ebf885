// <copyright file="MigrationController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Models.Exceptions;
using NetProGroup.Framework.Mvc;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataMigration;
using NetProGroup.Trust.DataMigration.DTO;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Shared.Jurisdictions;
using NetProGroup.Trust.Domain.Shared.Permissions;
using Swashbuckle.AspNetCore.Annotations;
using System.Text.Json;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Trust.Application.Scheduler;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.DataMigration.Logging;
using NetProGroup.Trust.DataMigration.Services.Nevis;

namespace NetProGroup.Trust.API.Controllers
{
    /// <summary>
    /// Controller for managing data migrations for the TNEV region.
    /// </summary>
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class MigrationController : TrustAPIControllerBase
    {
        /// <summary>
        /// The region code for TNEV.
        /// </summary>
        private readonly string _jurisdictionCode;

        private readonly ILogger<MigrationController> _logger;
        private readonly DataMigrationBackgroundService _dataMigrationBackgroundService;
        private readonly TrustDbContext _dbContext;
        private readonly ISecurityManager _securityManager;
        private readonly IDataMigrationsDataManager _dataMigrationsDataManager;
        private readonly CompanyMigrationService _companyMigrationService;
        private readonly ILockManager _lockManager;
        private readonly DataMigrationExcelLogService _dataMigrationExcelLogService;

        /// <summary>
        /// Initializes a new instance of the <see cref="MigrationController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="dataMigrationBackgroundService">The background service for data migrations.</param>
        /// <param name="dbContext">The database context.</param>
        /// <param name="securityManager">The security manager.</param>
        /// <param name="dataMigrationsDataManager">The repository for data migrations.</param>
        /// <param name="companyMigrationService">The service for company migration.</param>
        /// <param name="lockManager">Managare for exclusive locks.</param>
        /// <param name="dataMigrationExcelLogService">Excel logservice for the datamigration.</param>
        /// <param name="dataMigrationAppSettings">The application settings for data migration.</param>
        public MigrationController(
            ILogger<MigrationController> logger,
            DataMigrationBackgroundService dataMigrationBackgroundService,
            TrustDbContext dbContext,
            ISecurityManager securityManager,
            IDataMigrationsDataManager dataMigrationsDataManager,
            CompanyMigrationService companyMigrationService,
            ILockManager lockManager,
            DataMigrationExcelLogService dataMigrationExcelLogService,
            IOptions<DataMigrationAppSettings> dataMigrationAppSettings)
            : base(logger)
        {
            _logger = logger;
            _dataMigrationBackgroundService = dataMigrationBackgroundService;
            _dbContext = dbContext;
            _securityManager = securityManager;
            _dataMigrationsDataManager = dataMigrationsDataManager;
            _companyMigrationService = companyMigrationService;
            _lockManager = lockManager;
            _dataMigrationExcelLogService = dataMigrationExcelLogService;
            ArgumentNullException.ThrowIfNull(dataMigrationAppSettings, nameof(dataMigrationAppSettings));
            _jurisdictionCode = dataMigrationAppSettings.Value.ActiveJurisdiction;
        }

        /// <summary>
        /// Starts a data migration for the TNEV region asynchronously.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/migration/start.
        /// </remarks>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result indicating that the migration process has been initiated.</returns>
        [HttpPost("start")]
        [SwaggerOperation(OperationId = "StartMigration", Description = "Starts a data migration for the TNEV region asynchronously.")]
        [ProducesResponseType(typeof(string), StatusCodes.Status202Accepted)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        public async Task<IActionResult> StartMigration()
        {
            var result = await ProcessRequestAsync(
                validateAsync: async () =>
                {
                    ValidateWorkContextUserId();
                    var permission = _jurisdictionCode switch
                    {
                        JurisdictionCodes.Nevis => WellKnownPermissionNames.STRModule_DataMigration,
                        JurisdictionCodes.Bahamas => WellKnownPermissionNames.ESBahamasModule_DataMigration,
                        _ => throw new NotImplementedException()
                    };
                    await _securityManager.RequireManagementPermissionAsync(permission);
                },
                executeAsync: async () =>
                {
                    _logger.LogInformation($"Data migration requested for region: {_jurisdictionCode}");
                    await _dataMigrationBackgroundService.QueueMigrationAsync(_jurisdictionCode, WorkContext.IdentityUserId!.Value);
                },
                createResponseModel: () => $"Data migration process initiated for region: {_jurisdictionCode}");

            return AcceptedResult(result);
        }

        /// <summary>
        /// Gets the status of the data migration for the region, currently hardcoded to TNEV.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/migration/status.
        /// </remarks>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the migration status.</returns>
        [HttpGet("status")]
        [SwaggerOperation(OperationId = "GetMigrationStatus", Description = "Gets the status of the data migration for the TNEV region")]
        [ProducesResponseType(typeof(DataMigrationDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        public async Task<IActionResult> GetMigrationStatus()
        {
            NetProGroup.Trust.Domain.DataMigrations.DataMigration migrationRecord = null;

            var result = await ProcessRequestAsync<object>(
                validateAsync: async () =>
                {
                    await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.STRModule_DataMigration);
                },
                executeAsync: async () =>
                {
                    migrationRecord = await _dataMigrationsDataManager.GetLastDataMigrationForJurisdiction(_jurisdictionCode);
                },
                createResponseModel: () =>
                {
                    if (migrationRecord == null)
                    {
                        return null;
                    }

                    var entityProgresses = migrationRecord.EntityMigrationProgresses.Select(ep => new EntityMigrationProgressDTO()
                    {
                        EntityName = ep.EntityName,
                        SourceCount = ep.SourceCount,
                        ProcessedCount = ep.ProcessedCount,
                        SuccessCount = ep.SuccessCount,
                        FailedCount = ep.FailedCount,
                        LastUpdated = ep.LastUpdated
                    }).ToList();

                    var unprocessedRecords = string.IsNullOrEmpty(migrationRecord.UnprocessedRecords)
                        ? new List<UnprocessedRecordDTO>()
                        : JsonSerializer.Deserialize<List<UnprocessedRecordDTO>>(migrationRecord.UnprocessedRecords);

                    return new DataMigrationDTO
                    {
                        Region = migrationRecord.Jurisdiction.Code,
                        Status = (DataMigrationDTO.MigrationStatus)migrationRecord.Status,
                        InitialSyncCompleted = migrationRecord.InitialSyncCompleted,
                        MigrationCompleted = migrationRecord.MigrationCompleted,
                        LastUpdated = migrationRecord.LastUpdated,
                        Error = migrationRecord.Error,
                        UnprocessedRecords = unprocessedRecords,
                        EntityMigrationProgresses = entityProgresses,
                        StopRequested = migrationRecord.StopRequested
                    };
                });

            if (migrationRecord == null)
            {
                return NotFound(new APIExceptionModel() { exceptionMessage = $"No migration record found for region: {_jurisdictionCode}" });
            }

            return result.AsResponse();
        }

        /// <summary>
        /// Stops the data migration for the TNEV region.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/migration/stop.
        /// </remarks>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the migration stop status.</returns>
        [HttpPost("stop")]
        [SwaggerOperation(OperationId = "StopMigration", Description = "Stops the data migration for the TNEV region")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> StopMigration()
        {
            var result = await ProcessRequestAsync(
                validateAsync: async () =>
                {
                    await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.STRModule_DataMigration);
                },
                executeAsync: async () =>
                {
                    var migrationRecord = await _dataMigrationsDataManager.GetLastDataMigrationForJurisdiction(_jurisdictionCode);

                    if (migrationRecord == null)
                    {
                        throw new NotFoundException($"No migration record found for region: {_jurisdictionCode}");
                    }

                    migrationRecord.StopRequested = true;

                    // Set cancelled here, even though it may still be running, because sometimes the stopping doesn't work correctly.
                    // This way at least a new migration can be started.
                    migrationRecord.SetCancelled();
                    await _dbContext.SaveChangesAsync();

                    _logger.LogInformation($"Stop requested for data migration in region: {_jurisdictionCode}");
                },
                createResponseModel: () => $"Stop requested for data migration in region: {_jurisdictionCode}");

            return result.AsResponse();
        }

        /// <summary>
        /// Does a post-migration cleanup for the TNEV region.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     POST /api/v1/migration/cleanup.
        /// </remarks>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the migration stop status.</returns>
        [HttpPost("cleanup")]
        [SwaggerOperation(OperationId = "PostMigrationCleanup", Description = "Does a post-migration cleanup for the TNEV region.")]
        [ProducesResponseType(typeof(string), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PostMigrationCleanup()
        {
            var result = await ProcessRequestAsync<object>(
                validateAsync: async () =>
                {
                    switch (_jurisdictionCode)
                    {
                        case JurisdictionCodes.Nevis:
                            await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.STRModule_DataMigration);
                            break;
                        default:
                            throw new NotImplementedException($"Post-migration cleanup is not implemented for jurisdiction: {_jurisdictionCode}.");
                    }
                },
                executeAsync: async () =>
                {
                    var migrationRecord = await _dataMigrationsDataManager.GetLastDataMigrationForJurisdiction(_jurisdictionCode);

                    if (migrationRecord == null)
                    {
                        throw new NotFoundException($"No migration record found for region: {_jurisdictionCode}");
                    }

                    if (migrationRecord.MigrationCompleted == false)
                    {
                        throw new PreconditionFailedException($"Migration for region {_jurisdictionCode} should be completed");
                    }

                    var jobLock = await AcquireLockAsync(new Guid(ScheduledJobConsts.ViewPointSyncJobId), _lockManager, WorkContext.IdentityUserId.Value);
                    if (!jobLock.Id.HasValue)
                    {
                        throw new NoLockException("Failed to acquire a lock for ViewPointSync");
                    }

                    try
                    {
                        _logger.LogInformation("Post migration cleanup started for data migration in region: {JurisdictionCode}", _jurisdictionCode);

                        await _companyMigrationService.CleanupCompaniesAsync(migrationRecord, jobLock);

                        _logger.LogInformation("Post migration cleanup completed for data migration in region: {JurisdictionCode}", _jurisdictionCode);
                    }
                    finally
                    {
                        await _lockManager.ReleaseLockAsync(jobLock.Id.Value);
                    }
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the log workbook of the data migration for the region, currently hardcoded to TNEV.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/migration/log-workbook.
        /// </remarks>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with FileContentResult.</returns>
        [HttpGet("log-workbook")]
        [SwaggerOperation(OperationId = "GetMigrationLogWorkbook", Description = "Gets the log workbook (excel) for the migration")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportMigrationLogWorkbook()
        {
            ReportDownloadResponseDTO item = null;
            await ProcessRequestAsync<ReportDownloadResponseDTO>(
                validateAsync: async () =>
                {
                    await _securityManager.RequireManagementPermissionAsync(WellKnownPermissionNames.STRModule_DataMigration);
                },
                executeAsync: async () =>
                {
                    var migrationRecord = await _dataMigrationsDataManager.GetLastDataMigrationForJurisdiction(_jurisdictionCode);

                    if (migrationRecord == null)
                    {
                        throw new NotFoundException($"No migration record found for region: {_jurisdictionCode}");
                    }

                    item = await _dataMigrationExcelLogService.GetWorkbookForMigrationAsDownload(migrationRecord);
                });

            return new FileContentResult(item.FileContent.ToArray(), item.ContentType)
            {
                FileDownloadName = item.FileName
            };
        }

        private static IActionResult AcceptedResult<T>(ProcessingResult<T> result)
        {
            if (result.Exception != null)
            {
                return result.AsResponse();
            }

            return new AcceptedResult();
        }

        /// <summary>
        /// Acquires a lock for cleanup on post-migration.
        /// </summary>
        /// <param name="jobId">Id of the job to acqiure the lock for.</param>
        /// <param name="lockManager">The LockManager.</param>
        /// <param name="startedByUserId">ID of the user who started the migration.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private static async Task<LockDTO> AcquireLockAsync(Guid jobId, ILockManager lockManager, Guid startedByUserId)
        {
            var request = new AcquireLockRequestDTO
            {
                IdentityUserId = startedByUserId,
                EntityName = "ScheduledJob",
                EntityId = jobId,
                Session = string.Empty
            };

            return await lockManager.AcquireLockAsync(request);
        }
    }
}
