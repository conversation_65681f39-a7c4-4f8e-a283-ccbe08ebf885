﻿// <copyright file="CreateOrUpdateCurrencyDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Currencies
{
    /// <summary>
    /// Data Transfer Object for creating or updating a currency.
    /// </summary>
    public class CreateCurrencyDTO
    {
        /// <summary>
        /// Gets or sets the name of the currency.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the code of the currency (e.g., USD, EUR).
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the symbol of the currency (e.g., $, €).
        /// </summary>
        public string Symbol { get; set; }
    }
}
