﻿// <copyright file="IManagementBeneficialOwnersAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners
{
    /// <summary>
    /// Interface for the BeneficialOwners AppService for management.
    /// </summary>
    public interface IManagementBeneficialOwnersAppService : IScopedService
    {
        /// <summary>
        /// Gets the current version of a particular BeneficialOwner using id.
        /// </summary>
        /// <param name="beneficialOwnerId">The unique identifier of the beneficial owner.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the beneficial owner DTO.</returns>
        Task<BeneficialOwnerDTO> GetBeneficialOwnerAsync(Guid beneficialOwnerId);

        /// <summary>
        /// Gets both current and prior version of a particular BeneficialOwner using the id so they can be compared.
        /// </summary>
        /// <param name="beneficialOwnerId">The unique identifier of the beneficial owner.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the beneficial owner comparison DTO.</returns>
        Task<BeneficialOwnerComparisonDTO> GetBeneficialOwnerForComparisonAsync(Guid beneficialOwnerId);
    }
}
