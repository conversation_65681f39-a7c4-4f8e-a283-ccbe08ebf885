﻿// <copyright file="IShareholdersAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using X.PagedList;

namespace NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Shareholders
{
    /// <summary>
    /// Interface for the Shareholders AppService.
    /// </summary>
    public interface IShareholdersAppService : IScopedService
    {
        /// <summary>
        /// Gets a paged list with Shareholders.
        /// </summary>
        /// <param name="legalEntityId">Id of the legal entity.</param>
        /// <param name="pageNumber">The page number to retrieve.</param>
        /// <param name="pageSize">The number of items per page.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains the Shareholders as a paged list.</returns>
        Task<IPagedList<ShareholderDTO>> ListShareholdersAsync(Guid legalEntityId, int pageNumber, int pageSize);

        /// <summary>
        /// Gets the current version of a particular Shareholder using the unique relation id.
        /// </summary>
        /// <param name="uniqueRelationId">The unique relation identifier of the shareholder.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the shareholder DTO.</returns>
        Task<ShareholderDTO> GetShareholderAsync(string uniqueRelationId);

        /// <summary>
        /// Confirmation of the data for the Shareholder with the unique relation id.
        /// </summary>
        /// <param name="uniqueRelationId">The unique relation identifier of the shareholder.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the confirmed shareholder DTO.</returns>
        Task<ShareholderDTO> SetConfirmationAsync(string uniqueRelationId);

        /// <summary>
        /// Request for updating the data of the Shareholder with the unique relation id.
        /// </summary>
        /// <param name="requestUpdate">The model holding the parameters for the update request.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the updated shareholder DTO.</returns>
        Task<ShareholderDTO> RequestUpdateAsync(RequestUpdateDTO requestUpdate);

        /// <summary>
        /// Request for assistance related to Shareholders.
        /// </summary>
        /// <param name="requestAssistance">The model holding the parameters for the assistance request.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task RequestAssistanceAsync(RequestAssistanceDTO requestAssistance);

        /// <summary>
        /// Gets both current and prior version of a particular Shareholder using the unique relation id so they can be compared.
        /// </summary>
        /// <param name="uniqueRelationId">The unique relation identifier of the shareholder.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the shareholder comparison DTO.</returns>
        Task<ShareholderComparisonDTO> GetShareholderForComparisonAsync(string uniqueRelationId);
    }
}
