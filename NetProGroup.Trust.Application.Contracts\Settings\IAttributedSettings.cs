// <copyright file="IAttributedSettings.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Settings
{
    /// <summary>
    /// Base to enable generic SettingDTOs.
    /// </summary>
#pragma warning disable CA1040 // Avoid empty interfaces
    public interface IAttributedSettings
    {
    }
#pragma warning restore CA1040 // Avoid empty interfaces
}
