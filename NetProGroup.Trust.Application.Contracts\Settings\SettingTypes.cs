// <copyright file="SettingTypes.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Settings
{
    /// <summary>
    /// List of types to use for getting or setting ssettings for jurisdictio, masterclient or legal entity.
    /// </summary>
    public abstract class SettingTypes
    {
        /// <summary>
        /// Settings for the Simple Tax Return Fees.
        /// </summary>
#pragma warning disable SA1310 // Field names should not contain underscore
        public const string STR_LatePaymentFees = "str-late-payment-fees";
#pragma warning restore SA1310 // Field names should not contain underscore

        /// <summary>
        /// Settings for documents.
        /// </summary>
        public const string Documents = "documents";

        /// <summary>
        /// Settings for fees.
        /// </summary>
        public const string Fees = "fees";

        /// <summary>
        /// The key for the first submission year setting.
        /// </summary>
        public const string FirsSubmissionYear = "first-submission-year";
    }
}
