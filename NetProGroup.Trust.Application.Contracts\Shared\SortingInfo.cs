﻿// <copyright file="SortingInfo.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Shared
{
    /// <summary>
    /// Represents sorting information for data queries, including the field to sort by and the sort order.
    /// </summary>
    public class SortingInfo
    {
        /// <summary>
        /// Gets or sets the name of the field to sort on.
        /// </summary>
        public string SortBy { get; set; }

        /// <summary>
        /// Gets or sets in which order to sort (asc or desc).
        /// </summary>
        public string SortOrder { get; set; }
    }
}
