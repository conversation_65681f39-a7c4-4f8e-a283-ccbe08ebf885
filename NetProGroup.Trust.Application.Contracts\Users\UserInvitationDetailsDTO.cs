// <copyright file="UserInvitationDetailsDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Users
{
    /// <summary>
    /// Represents the invitation info for a user.
    /// </summary>
    public class UserInvitationDetailsDTO
    {
        /// <summary>
        /// Gets or sets a value indicating whether the user is invited.
        /// </summary>
        public bool IsInvited { get; set; }

        /// <summary>
        /// Gets or sets the date/time of the last invitation email.
        /// </summary>
        public DateTime? LastInvitationAt { get; set; }
    }
}
