﻿// <copyright file="JurisdictionTaxRatesAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Trust.Application.Contracts.JurisdictionTaxRates;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Jurisdictions;
using X.PagedList;

namespace NetProGroup.Trust.Application.JurisdictionTaxRates
{
    /// <summary>
    /// Implementation of <see cref="IJurisdictionTaxRatesAppService"/> for managing jurisdiction tax rates.
    /// </summary>
    public class JurisdictionTaxRatesAppService : IJurisdictionTaxRatesAppService
    {
        private readonly ILogger<JurisdictionTaxRatesAppService> _logger;
        private readonly IUserManager _userManager;
        private readonly IJurisdictionTaxRatesRepository _jurisdictionTaxRatesRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IMapper _mapper;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="JurisdictionTaxRatesAppService"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="userManager">The user manager instance.</param>
        /// <param name="jurisdictionTaxRateRepository">The repository for jurisdiction tax rates.</param>
        /// <param name="jurisdictionsRepository">The repository for jurisdiction.</param>
        /// <param name="mapper">The AutoMapper instance for mapping entities to DTOs.</param>
        /// <param name="securityManager">The security manager instance.</param>
        public JurisdictionTaxRatesAppService(
            ILogger<JurisdictionTaxRatesAppService> logger,
            IUserManager userManager,
            IJurisdictionTaxRatesRepository jurisdictionTaxRateRepository,
            IJurisdictionsRepository jurisdictionsRepository,
            IMapper mapper,
            ISecurityManager securityManager)
        {
            _logger = logger;
            _userManager = userManager;
            _jurisdictionTaxRatesRepository = jurisdictionTaxRateRepository;
            _jurisdictionsRepository = jurisdictionsRepository;
            _mapper = mapper;
            _securityManager = securityManager;
        }

        /// <summary>
        /// Retrieves tax rates for a specific jurisdiction with pagination.
        /// </summary>
        /// <param name="jurisdictionId">The ID of the jurisdiction.</param>
        /// <param name="pageNumber">The page number to retrieve. Default is the first page.</param>
        /// <param name="pageSize">The number of items per page. Default is the specified page size.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains tax rates for the jurisdiction.</returns>
        public async Task<IPagedList<JurisdictionTaxRateDTO>> GetTaxRatesByJurisdictionAsync(Guid jurisdictionId,
            int pageNumber,
            int pageSize)
        {
            await _securityManager.RequireManagementAccessToJurisdictionAsync(jurisdictionId);

            // Retrieve tax rates with pagination
            var taxRates = await _jurisdictionTaxRatesRepository
                .FindByConditionAsPagedListAsync(x => x.JurisdictionId == jurisdictionId,
                pageNumber,
                pageSize);

            // Map domain entities to DTOs
            var taxRateDTOs = _mapper.Map<IEnumerable<JurisdictionTaxRateDTO>>(taxRates);

            // Return as paged list
            return new StaticPagedList<JurisdictionTaxRateDTO>(taxRateDTOs, taxRates.GetMetaData());
        }

        /// <summary>
        /// Creates or updates a tax rate for a specific jurisdiction.
        /// </summary>
        /// <param name="model">The data transfer object containing the tax rate details.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task CreateOrUpdateTaxRateAsync(CreateOrUpdateTaxRateDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));
            await _securityManager.RequireManagementAccessToJurisdictionAsync(model.JurisdictionId);

            // Adjust StartDate to start at 00:00 hours
            var adjustedStartDate = model.StartDate.Date;

            // Check if a tax rate already exists for the given JurisdictionId and StartDate
            var jurisdictionTaxRate = await _jurisdictionTaxRatesRepository
                .FindFirstOrDefaultByConditionAsync(x => x.JurisdictionId == model.JurisdictionId
                                                      && x.StartDate.Date == adjustedStartDate);

            if (jurisdictionTaxRate != null)
            {
                // Update the existing tax rate
                jurisdictionTaxRate.TaxRate = model.TaxRate;
                jurisdictionTaxRate.StartDate = adjustedStartDate;
                await _jurisdictionTaxRatesRepository.UpdateAsync(jurisdictionTaxRate, true);
            }
            else
            {
                // Create a new tax rate
                var newTaxRate = new JurisdictionTaxRate
                {
                    JurisdictionId = model.JurisdictionId,
                    TaxRate = model.TaxRate,
                    StartDate = adjustedStartDate
                };
                await _jurisdictionTaxRatesRepository.InsertAsync(newTaxRate, true);
            }
        }

        /// <summary>
        /// Deletes a jurisdiction tax rate by its ID.
        /// </summary>
        /// <param name="jurisdictionTaxRateId">The ID of the tax rate to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public async Task DeleteTaxRateAsync(Guid jurisdictionTaxRateId)
        {
            await _securityManager.RequireManagementUserAsync();

            // Check if jurisdiction tax rate exist
            var jurisdictionTaxRate = await _jurisdictionTaxRatesRepository.CheckTaxRateByIdAsync(jurisdictionTaxRateId);

            // TODO: Deletion is only possible as long as it is not used (add check).

            // Delete tax rate
            await _jurisdictionTaxRatesRepository.DeleteAsync(jurisdictionTaxRate, true);
        }
    }
}
