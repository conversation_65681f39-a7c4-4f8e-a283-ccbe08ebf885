// <copyright file="PaymentAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using DocumentFormat.OpenXml.Office2010.Excel;
using NetProGroup.Trust.Application.Contracts.Payments;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Application.Contracts.Payments.Response;
using NetProGroup.Trust.DataManager.Payments;
using NetProGroup.Trust.DataManager.Payments.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.Payments
{
    /// <summary>
    /// Provides application services for managing payments, acting as a facade between the presentation layer and the data manager.
    /// </summary>
    public class PaymentAppService : IPaymentsAppService
    {
        /// <summary>
        /// Manages the data operations for payments and invoices.
        /// </summary>
        private readonly IPaymentDataManager _dataManager;

        private readonly ISecurityManager _securityManager;
        private readonly IMapper _mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentAppService"/> class with the specified data manager.
        /// </summary>
        /// <param name="dataManager">The data manager responsible for handling payment-related operations.</param>
        /// <param name="securityManager">The security manager for handling user authorization.</param>
        /// <param name="mapper">The mapper for mapping between data transfer objects and domain entities.</param>
        public PaymentAppService(IPaymentDataManager dataManager, ISecurityManager securityManager, IMapper mapper)
        {
            _dataManager = dataManager;
            _securityManager = securityManager;
            _mapper = mapper;
        }

        /// <summary>
        /// Retrieves a paginated list of payments based on the specified request criteria.
        /// </summary>
        /// <param name="requestDTO">The request object containing filter and pagination criteria for payments.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a paginated list of <see cref="PaymentDTO"/> objects.</returns>
        public async Task<IPagedList<PaymentDTO>> ListPaymentsAsync(PaymentsRequestDTO requestDTO)
        {
            await _securityManager.RequireClientUserAsync();

            var request = _mapper.Map<PaymentsRequest>(requestDTO);
            request.UserId = _securityManager.UserId;

            return await _dataManager.ListPaymentsAsync(request);
        }

        /// <summary>
        /// Retrieves the details of a payment asynchronously by its identifier.
        /// </summary>
        /// <param name="id">
        /// The unique identifier of the payment to retrieve.
        /// </param>
        /// <returns>
        /// A task that represents the asynchronous operation.
        /// The task result contains a <see cref="PaymentDetailsResponseDTO"/> object with the details of the specified payment.
        /// </returns>
        /// <remarks>
        /// This method retrieves payment details by calling the underlying data manager.
        /// </remarks>
        public async Task<PaymentDetailsResponseDTO> GetPaymentAsync(Guid id)
        {
            await _securityManager.RequireClientUserAsync();
            var responseDto = await _dataManager.GetPaymentAsync(id);

            await _securityManager.RequireClientAccessToCompanyAsync(responseDto.LegalEntityId);
            return responseDto;
        }

        /// <summary>
        /// Adds a new payment transaction asynchronously.
        /// </summary>
        /// <param name="createTransactionRequestDto">
        /// The request object containing payment details such as legal entity ID, currency ID, and associated invoice IDs.
        /// </param>
        /// <returns>
        /// A task that represents the asynchronous operation.
        /// The task result contains a <see cref="CreatePaymentResponseDTO"/> object with the details of the created payment.
        /// </returns>
        /// <remarks>
        /// This method uses the provided request data to create a payment transaction by calling the underlying data manager.
        /// </remarks>
        public async Task<CreatePaymentResponseDTO> AddPaymentAsync(CreatePaymentRequestDTO createTransactionRequestDto)
        {
            ArgumentNullException.ThrowIfNull(createTransactionRequestDto, nameof(createTransactionRequestDto));

            await _securityManager.RequireClientAccessToCompanyAsync(createTransactionRequestDto.LegalEntityId);

            return await _dataManager.CreatePaymentsAsync(createTransactionRequestDto);
        }

        /// <inheritdoc/>
        public async Task CancelPaymentAsync(Guid paymentId)
        {
            await _securityManager.RequireClientUserAsync();

            var payment = await _dataManager.GetPaymentAsync(paymentId);
            await _securityManager.RequireClientAccessToCompanyAsync(payment.LegalEntityId);

            await _dataManager.CancelPaymentAsync(paymentId, saveChanges: true);
        }
    }
}