// <copyright file="PaymentProcessorFacade.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Trust.Application.Contracts.Payments;
using NetProGroup.Trust.Application.Contracts.Payments.Processor;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Services;
using StartPaymentRequest = NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Input.StartPaymentRequest;

namespace NetProGroup.Trust.Application.AppServices.Payments
{
    /// <summary>
    /// A facade responsible for handling payment processing tasks.
    /// This class acts as an intermediary between the payment creation logic and the payment processing system.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="PaymentProcessorFacade"/> class.
    /// </remarks>
    /// <param name="cxPayTransactionHandler">The handler for CxPay transactions.</param>
    /// <param name="mapper">The mapper for mapping objects.</param>
    public class PaymentProcessorFacade(ICxPayTransactionHandler cxPayTransactionHandler, IMapper mapper)
        : IPaymentProcessorFacade
    {
        /// <summary>
        /// Processes a payment request and returns the result of the payment operation.
        /// </summary>
        /// <remarks>
        /// This method processes a payment based on the provided request data, including details
        /// such as payment amount, currency, and payment method. It interacts with the necessary, payment gateways or services to complete the transaction.
        /// </remarks>
        /// <param name="request">The data transfer object containing the necessary payment details to process the transaction.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a <see cref="PaymentProcessorResponse"/> object, indicating the result of the payment processing.</returns>
        /// <exception cref="NotImplementedException">Thrown when the method is not implemented.</exception>
        public async Task<CreateTransactionResponseDTO> HandleStartPaymentAsync(CreateTransactionRequestDTO request)
        {
            // For now, we're going to use CXPay as the default payment provider,
            // because it's the only provider we have integrated at the moment.
            var startPaymentRequest = mapper.Map<StartPaymentRequest>(request);
            return new CreateTransactionResponseDTO
            {
                PaymentProcessorResponse = await cxPayTransactionHandler.StartPaymentAsync(startPaymentRequest)
            };
        }

        /// <summary>
        /// Commits a payment to the provider and updates the associated submission's payment status.
        /// </summary>
        /// <param name="request">The request containing the transaction and token IDs.</param>
        /// <returns>A task representing the asynchronous operation. The task result contains the response from submitting the payment.</returns>
        public async Task<SubmitPaymentResponseDTO> HandleCommitPaymentAsync(SubmitPaymentRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var response = await cxPayTransactionHandler.CompletePaymentAsync(request.TransactionId, request.TokenId);
            return mapper.Map<SubmitPaymentResponseDTO>(response);
        }
    }
}