﻿// <copyright file="StartupService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Services.Communication;
using NetProGroup.Framework.Services.Communication.EFModels;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.Bahamas;
using NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.BVI;
using NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.Nevis;
using NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.Panama;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.MessageTemplates;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.ProductionOffices;
using NetProGroup.Trust.Domain.Shared.Roles;

namespace NetProGroup.Trust.Application.BackgroundServices
{
    /// <summary>
    /// Service to execute some stuff at (every) startup.
    /// </summary>
    public class StartupService : BackgroundService
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _serviceProvider;
        private readonly TrustOfficeOptions _trustOfficeOptions;

        private IServiceProvider _scopedServiceProvider;
        private ISettingsManager _settingsManager;
        private IJurisdictionsRepository _jurisdictionsRepository;
        private IMessageTemplateService _messageTemplateService;

        private Framework.Services.Configuration.IConfigurationManager _configurationManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="StartupService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="serviceProvider">Instance of the serviceProvider.</param>
        /// <param name="configuration">Instance of the configuration.</param>
        /// <param name="trustOfficeOptions">The trust office options configuration.</param>
        public StartupService(ILogger<StartupService> logger,
                              IServiceProvider serviceProvider,
                              IConfiguration configuration,
                              IOptions<TrustOfficeOptions> trustOfficeOptions)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _configuration = configuration;

            ArgumentNullException.ThrowIfNull(trustOfficeOptions, nameof(trustOfficeOptions));
            _trustOfficeOptions = trustOfficeOptions.Value;
        }

        /// <summary>
        /// The actual execution (only once) of the service.
        /// </summary>
        /// <param name="stoppingToken">Token for cancellation of the task.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            Framework.Services.Locks.Models.LockDTO theLock = null;
            ILockManager lockManager = null;

            try
            {
                using var scope = _serviceProvider.CreateScope();
                _scopedServiceProvider = scope.ServiceProvider;
                _settingsManager = _scopedServiceProvider.GetRequiredService<ISettingsManager>();
                _jurisdictionsRepository = _scopedServiceProvider.GetRequiredService<IJurisdictionsRepository>();
                _messageTemplateService = _scopedServiceProvider.GetRequiredService<IMessageTemplateService>();
                _configurationManager = _scopedServiceProvider.GetRequiredService<Framework.Services.Configuration.IConfigurationManager>();

                lockManager = _scopedServiceProvider.GetRequiredService<ILockManager>();
                theLock = await lockManager.AcquireLockAsync(new Framework.Services.Locks.Models.AcquireLockRequestDTO { EntityName = this.GetType().Name, EntityId = Guid.Empty, IdentityUserId = Guid.NewGuid() });
                if (theLock.Id.HasValue)
                {
                    await SetupModulesAsync();
                    await SetupRolesAsync();

                    // Email templates
                    await UpdateUserInvitationTemplate();
                    await UpdateUserInvitationReregistrationTemplate();
                    await UpdateRequestAssistanceTemplate();
                    await UpdateRequestUpdateTemplate();

                    await SetProductionOfficeEmailConfigurationAsync();

                    // Seed Nevis Data
                    var nevisSeeder = _scopedServiceProvider.GetRequiredService<INevisSeeder>();
                    await nevisSeeder.RunAsync();

                    // Seed Bahamas Data
                    var bahamasSeeder = _scopedServiceProvider.GetRequiredService<IBahamasSeeder>();
                    await bahamasSeeder.RunAsync();

                    // Seed Panama Data
                    var panamaSeeder = _scopedServiceProvider.GetRequiredService<IPanamaSeeder>();
                    await panamaSeeder.RunAsync();

                    // Seed BVI Data
                    var bviSeeder = _scopedServiceProvider.GetRequiredService<IBVISeeder>();
                    await bviSeeder.RunAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "StartupService failed");
                throw;
            }
            finally
            {
                if (lockManager != null && theLock != null && theLock.Id.HasValue)
                {
                    await lockManager.ReleaseLockAsync(theLock);
                }
            }
        }

        private async Task UpdateUserInvitationTemplate()
        {
            var model = new Framework.Services.Communication.Email.Models.UpdateMessageTemplateDTO
            {
                Id = Guid.Parse("{598C0BC9-C358-402A-A9EC-04E7F7BCA20D}"),
                SystemName = SystemNames.UserInvitationMessage,
                DisplayName = "UserInvitation Message.",
                Description = "Message for the inviation of a MasterClient User.",
                Subject = "Invitation for Private Client Portal",
                HtmlBody = Properties.Resources.UserInvitation_HtmlBody.RemoveEOL(),
                PlainBody = Properties.Resources.UserInvitation_PlainText.RemoveEOL(),
                BodyType = MessageTemplateBodyTypeEnum.Tokenized,
                IsActive = true,
                EmailAccountId = null
            };

            await _messageTemplateService.UpdateMessageTemplateAsync(model);
        }

        private async Task UpdateUserInvitationReregistrationTemplate()
        {
            var model = new Framework.Services.Communication.Email.Models.UpdateMessageTemplateDTO
            {
                Id = Guid.Parse("{E71CA423-DD1D-4476-80AA-57ED35C0270F}"),
                SystemName = SystemNames.UserInvitationReregistrationMessage,
                DisplayName = "UserInvitation Reregistration Message.",
                Description = "Message for the invitation of a MasterClient User for reregistration.",
                Subject = "Invitation for Private Client Portal",
                HtmlBody = Properties.Resources.UserInvitation_ReReg_HtmlBody.RemoveEOL(),
                PlainBody = Properties.Resources.UserInvitation_ReReg_PlainText.RemoveEOL(),
                BodyType = MessageTemplateBodyTypeEnum.Tokenized,
                IsActive = true,
                EmailAccountId = null
            };

            await _messageTemplateService.UpdateMessageTemplateAsync(model);
        }

        private async Task UpdateRequestUpdateTemplate()
        {
            var model = new Framework.Services.Communication.Email.Models.UpdateMessageTemplateDTO
            {
                Id = Guid.Parse("{A68733E2-DE6B-4F82-BB0A-F8F547BE3183}"),
                SystemName = SystemNames.RequestUpdateMessage,
                DisplayName = "RequestUpdate Message.",
                Description = "Message for notification of a 'request for update'.",
                Subject = "{subject.prefix}{company.name} - Request update for Portal",
                HtmlBody = Properties.Resources.Notification_RequestUpdate_HtmlBody.RemoveEOL(),
                PlainBody = Properties.Resources.Notification_RequestUpdate_PlainText.RemoveEOL(),
                BodyType = MessageTemplateBodyTypeEnum.Tokenized,
                IsActive = true,
                EmailAccountId = null
            };

            await _messageTemplateService.UpdateMessageTemplateAsync(model);
        }

        private async Task UpdateRequestAssistanceTemplate()
        {
            var model = new Framework.Services.Communication.Email.Models.UpdateMessageTemplateDTO
            {
                Id = Guid.Parse("{C382294B-67CB-456D-A59B-986CDDAAEAC3}"),
                SystemName = SystemNames.RequestAssistanceMessage,
                DisplayName = "RequestAssistance Message.",
                Description = "Message for notification of a 'request for assistance'.",
                Subject = "{subject.prefix}{company.name} - No {position} found in the Portal",
                HtmlBody = Properties.Resources.Notification_RequestAssistance_HtmlBody.RemoveEOL(),
                PlainBody = Properties.Resources.Notification_RequestAssistance_PlainText.RemoveEOL(),
                BodyType = MessageTemplateBodyTypeEnum.Tokenized,
                IsActive = true,
                EmailAccountId = null
            };

            await _messageTemplateService.UpdateMessageTemplateAsync(model);
        }

        private async Task SetProductionOfficeEmailConfigurationAsync()
        {
            // HongKong
            await SetJurisdictionEmailConfigurationAsync(ProductionOfficeCodes.HongKong, BuildProductionOfficeEmail("THKO"));

            // BVI
            await SetJurisdictionEmailConfigurationAsync(ProductionOfficeCodes.BVI, BuildProductionOfficeEmail("TBVI"));

            // Panama
            await SetJurisdictionEmailConfigurationAsync(ProductionOfficeCodes.Panama, BuildProductionOfficeEmail("TPAN"));

            // CYP
            await SetJurisdictionEmailConfigurationAsync(ProductionOfficeCodes.Cyprus, BuildProductionOfficeEmail("TCYP"));

            // NEVIS
            await SetJurisdictionEmailConfigurationAsync(ProductionOfficeCodes.Nevis, BuildProductionOfficeEmail("TNEV"));

            // Default
            await SetJurisdictionEmailConfigurationAsync("default", BuildProductionOfficeEmail("THKO"));
        }

        private string BuildProductionOfficeEmail(string prefix)
        {
            var domain = _trustOfficeOptions.EmailDomain;
            var suffix = _trustOfficeOptions.ProductionOfficeEmailSuffix;
            if (string.IsNullOrWhiteSpace(suffix))
            {
                suffix = "noreply";
            }

            var email = $"{suffix}@{domain}";
            if (suffix.Equals("noreply", StringComparison.OrdinalIgnoreCase))
            {
                return email;
            }
            else
            {
                return $"{prefix}{email}";
            }
        }

        private async Task SetJurisdictionEmailConfigurationAsync(string productionOfficeCode, string email)
        {
            // Set the email address for update requests if not set yet.
            var key = $"{ConfigurationConsts.NOTIFICATION_UPDATEREQUEST_RECIPIENT_PREFIX}.{productionOfficeCode}";

            if (string.IsNullOrEmpty(await _configurationManager.GetConfigurationAsync<string>(key, null)))
            {
                await _configurationManager.SetConfigurationAsync(key, email);
            }

            // Sets the email address for assistance requests if not set yet.
            key = $"{ConfigurationConsts.NOTIFICATION_ASSISTANCEREQUEST_RECIPIENT_PREFIX}.{productionOfficeCode}";

            if (string.IsNullOrEmpty(await _configurationManager.GetConfigurationAsync<string>(key, null)))
            {
                await _configurationManager.SetConfigurationAsync(key, email);
            }
        }

        /// <summary>
        /// Setup of all modules.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task SetupModulesAsync()
        {
            await CreateModuleAsync(ModuleKeyConsts.BODirectors, "BO/Directors");

            // Nevis
            await CreateModuleAsync(ModuleKeyConsts.SimplifiedTaxReturn, "Simplified Tax Return");

            // Panama
            await CreateModuleAsync(ModuleKeyConsts.BasicFinancialReportPanama, "Basic Financial Report");

            // Bahamas
            await CreateModuleAsync(ModuleKeyConsts.EconomicSubstanceBahamas, "Economic Substance");

            // BVI
            await CreateModuleAsync(ModuleKeyConsts.EconomicSubstanceBVI, "Economic Substance");
        }

        /// <summary>
        /// Creates a module if it doesn't exist yet.
        /// </summary>
        /// <param name="key">The key for the module.</param>
        /// <param name="name">The name for the module.</param>
        /// <returns>The id of the module.</returns>
        private async Task<Guid> CreateModuleAsync(string key, string name)
        {
            var repository = _scopedServiceProvider.GetRequiredService<IModulesRepository>();

            var existing = await repository.FindFirstOrDefaultByConditionAsync(x => x.Key == key);
            if (existing == null)
            {
                var toDb = new Module(Guid.NewGuid(), key, name);
                await repository.InsertAsync(toDb, saveChanges: true);
                return toDb.Id;
            }
            else
            {
                return existing.Id;
            }
        }

        /// <summary>
        /// Setup roles.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task SetupRolesAsync()
        {
            await CreateRoleAsync(WellKnownRoleIds.Client, WellKnownRoleNames.Client);
        }

        /// <summary>
        /// Creates a role if it doesn't exist yet.
        /// </summary>
        /// <param name="id">The id of the role.</param>
        /// <param name="name">The name for the role.</param>
        private async Task CreateRoleAsync(Guid id, string name)
        {
            var userManager = _scopedServiceProvider.GetRequiredService<IUserManager>();

            if (await userManager.GetRoleByIdAsync(id) == null)
            {
                await userManager.CreateRoleAsync(new Framework.Services.Identity.Models.ApplicationRoleDTO
                {
                    Id = id,
                    Name = name,
                    DisplayName = name
                });
            }
        }
    }
}
