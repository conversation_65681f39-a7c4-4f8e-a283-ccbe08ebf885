// <copyright file="IBoDirManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.DataManager.BoDir.RequestResponses;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.BoDir
{
    /// <summary>
    /// Interface for the Beneficial Owner and Director (BO/Dir) manager.
    /// </summary>
    public interface IBoDirDataManager : IScopedService
    {
        /// <summary>
        /// Searches for Beneficial Owners and Directors using the provided criteria.
        /// </summary>
        /// <param name="request">The request containing search parameters.</param>
        /// <returns>A paged list of BO/Dir entries matching the search criteria.</returns>
        Task<IPagedList<BoDirItemDTO>> SearchBoDirsAsync(SearchBoDirRequest request);
    }
}
