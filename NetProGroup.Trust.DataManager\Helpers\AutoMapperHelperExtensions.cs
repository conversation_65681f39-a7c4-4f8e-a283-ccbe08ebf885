// <copyright file="AutoMapperHelperExtensions.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Submissions;
using System.Diagnostics.CodeAnalysis;

namespace NetProGroup.Trust.DataManager.Helpers
{
    /// <summary>
    ///     Helper methods for AutoMapper.
    /// </summary>
    public static class AutoMapperHelperExtensions
    {
        /// <summary>
        /// Gets the payment reference for a submission.
        /// </summary>
        /// <param name="submission">The submission to get the payment reference for.</param>
        /// <returns>The payment reference string if available; otherwise null.</returns>
        /// <exception cref="ArgumentNullException">Thrown when submission is null.</exception>
        public static string GetPaymentReference(this Submission submission)
        {
            ArgumentNullException.ThrowIfNull(submission);

            var payment = submission.GetPaidPayment();
            if (payment == null)
            {
                return null;
            }

            return payment.Reference;
        }

        /// <summary>
        /// Gets the used payment method for the submission.
        /// </summary>
        /// <param name="submission">The submission to get the method for.</param>
        /// <returns>The payment method string if available; otherwise null.</returns>
        /// <exception cref="ArgumentNullException">Thrown when submission is null.</exception>
        public static string GetPaymentMethod(this Submission submission)
        {
            ArgumentNullException.ThrowIfNull(submission);

            var payment = submission.GetPaidPayment();
            if (payment == null)
            {
                return null;
            }

            return payment.Type switch
            {
                PaymentType.CreditCard => "CreditCard",
                PaymentType.WireTransfer => "WireTransfer",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Gets the payment for the submission that is actually paid.
        /// </summary>
        /// <param name="submission">The submission to get the payment for.</param>
        /// <returns>The found payment or null if no paid payment.</returns>
        public static Domain.Payments.Payment GetPaidPayment(this Submission submission)
        {
            var payment = GetLastPaidPayment(submission?.Invoice?.PaymentInvoices);
            return payment;
        }

        /// <summary>
        /// Gets the 'paid at' date for the submission.
        /// </summary>
        /// <param name="source">The source submission.</param>
        /// <returns>The 'paid at' date.</returns>
        public static DateTime? GetPaidAt(this Submission source)
        {
            if (source == null)
            {
                return null;
            }

            if (source.IsPaid)
            {
                var payment = GetLastPaidPayment(source.Invoice?.PaymentInvoices);
                if (payment != null)
                {
                    return payment.PaidAt;
                }

                if (source.PaidAt.HasValue)
                {
                    return source.PaidAt;
                }

                return source.SubmittedAt;
            }

            return null;
        }

        /// <summary>
        /// Gets the paid at date for the invoice.
        /// </summary>
        /// <param name="source">The source invoice.</param>
        /// <returns>The paid at date.</returns>
        public static DateTime? GetPaidAt(this Invoice source)
        {
            var payment = GetLastPaidPayment(source?.PaymentInvoices);
            return payment?.PaidAt;
        }

        /// <summary>
        /// Gets the date of receivign the payment.
        /// </summary>
        /// <param name="submission">The submission instance.</param>
        /// <returns>The date of payment.</returns>
        public static DateTime? GetPaymentReceivedAt(this Submission submission)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            return submission.IsPaid && submission.Invoice != null ?
                   submission.Invoice.PaymentInvoices.OrderByDescending(p => p.Payment.PaidAt).FirstOrDefault(p => p.Payment.PaidAt.HasValue).Payment.PaidAt :

                   // Paid but no invoice returns the submitted datetime as the paid datetime
                   (submission.IsPaid ? submission.SubmittedAt : null);
        }

        /// <summary>
        /// Gets the status.
        /// </summary>
        /// <param name="invoice">The invoice to get the payment status for.</param>
        /// <returns>The found payment status.</returns>
        public static Domain.Payments.PaymentStatus GetPaymentStatus([NotNull] this Invoice invoice)
        {
            if (invoice.Status == DomainShared.Enums.InvoiceStatus.Paid)
            {
                return Domain.Payments.PaymentStatus.Completed;
            }

            return Domain.Payments.PaymentStatus.Pending;
        }

        /// <summary>
        /// Gets the custom enum string.
        /// </summary>
        /// <param name="productionOfficeType">The ProductionOffice type.</param>
        /// <returns>The custom enum string.</returns>
        public static string GetProductionOfficeEnumString(this ProductionOfficeType? productionOfficeType)
        {
            if (productionOfficeType.HasValue)
            {
                return productionOfficeType.Value.ToString();
            }
            else
            {
                return string.Empty;
            }
        }

        private static Domain.Payments.Payment GetLastPaidPayment(ICollection<PaymentInvoice> invoicePayments)
        {
            if (invoicePayments == null)
            {
                return null;
            }

            var invoicePayment = invoicePayments.Where(ip => ip.Payment.PaidAt.HasValue).OrderByDescending(ip => ip.Payment.PaidAt).FirstOrDefault();
            return invoicePayment?.Payment;
        }
    }
}