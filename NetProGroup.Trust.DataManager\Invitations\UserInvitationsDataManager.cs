﻿// <copyright file="UserInvitationsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Trust.Application.Contracts.Communication;
using NetProGroup.Trust.DataManager.Configurations;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Users;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Scheduling;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Enums;
using Newtonsoft.Json;

namespace NetProGroup.Trust.DataManager.Invitations
{
    /// <summary>
    /// Manager for sending invitations.
    /// </summary>
    public class UserInvitationsDataManager : IUserInvitationsDataManager
    {
        private readonly ILogger _logger;
        private readonly IWorkContext _workContext;

        private readonly IUserRepository _usersRepository;
        private readonly IUsersDataManager _usersDataManager;
        private readonly ICommunicationAppService _communicationAppService;
        private readonly IMasterClientUsersRepository _masterClientUsersRepository;

        private readonly TrustOfficeOptions _trustOfficeOptions;

        private readonly Microsoft.Extensions.Configuration.IConfiguration _configuration;

        /// <summary>
        /// Initializes a new instance of the <see cref="UserInvitationsDataManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="configuration">Configuration to use.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="usersRepository">Instance of a user repository.</param>
        /// <param name="usersDataManager">Instance of a users datamanager.</param>
        /// <param name="communicationAppService">Communication AppService instance to use.</param>
        /// <param name="masterClientUsersRepository">MasterClient users repository to use.</param>
        /// <param name="trustOfficeOptions">Options for trust office override.</param>
        public UserInvitationsDataManager(ILogger<UserInvitationsDataManager> logger,
            Microsoft.Extensions.Configuration.IConfiguration configuration,
            IWorkContext workContext,
            IUserRepository usersRepository,
            IUsersDataManager usersDataManager,
            ICommunicationAppService communicationAppService,
            IMasterClientUsersRepository masterClientUsersRepository,
            IOptions<TrustOfficeOptions> trustOfficeOptions)
        {
            ArgumentNullException.ThrowIfNull(trustOfficeOptions, nameof(trustOfficeOptions));

            _logger = logger;
            _configuration = configuration;
            _workContext = workContext;
            _usersRepository = usersRepository;
            _usersDataManager = usersDataManager;
            _communicationAppService = communicationAppService;
            _masterClientUsersRepository = masterClientUsersRepository;

            _trustOfficeOptions = trustOfficeOptions.Value;
        }

        /// <inheritdoc/>
        public async Task SendInvitationToUserAsync(Guid userId, string masterClientCode = null, bool force = false, bool reregistration = false, bool saveChanges = false)
        {
            // Check if the user is already registered. Skip if so.
            var applicationUser = await _usersRepository.CheckUserByIdAsync(userId);
            if (!force && applicationUser.ObjectId != null)
            {
                _logger.LogDebug("Skip invitation for user '{DisplayName}', already registered", UserExtensions.GetDisplayName(applicationUser));
                return;
            }

            // Check if the user was already invited less than 90 days ago
            var lastInvitation = await _usersDataManager.GetAttributeValueAsync<DateTime?>(userId, UserAttributeKeys.Invitation_Last_Sent);

            if (!force && lastInvitation.HasValue && DateTime.Today.Subtract(lastInvitation.Value.Date).TotalDays < 90)
            {
                _logger.LogDebug("Skip invitation for user '{DisplayName}', already invited on '{Date}'", UserExtensions.GetDisplayName(applicationUser), lastInvitation.Value.Date.ToShortDateString());
                return;
            }

            // No code defined?
            if (string.IsNullOrWhiteSpace(masterClientCode))
            {
                // Get the first masterclient code
                var userMasterClients = await _masterClientUsersRepository.FindByConditionAsync(mcu => mcu.UserId == userId, q => q.OrderBy(mcu => mcu.MasterClient.Code).Include(mcu => mcu.MasterClient));
                var masterClientUser = userMasterClients.FirstOrDefault();
                if (masterClientUser != null)
                {
                    masterClientCode = masterClientUser.MasterClient.Code;
                }
            }

            // If the invitation is not forced we need to check if there is an active company for aany of the masterclients
            var masterClientUsersWithActiveCompanies = await _masterClientUsersRepository.FindByConditionAsync(mcu => mcu.UserId == userId && mcu.MasterClient.LegalEntities.Any(le => le.IsActive));

            if (!masterClientUsersWithActiveCompanies.Any())
            {
                _logger.LogInformation("Don't send invitation for user '{DisplayName}' as there are no active companies", UserExtensions.GetDisplayName(applicationUser));
            }
            else
            {
                var sendIt = true;

                // If not in production then check for whitelist
                if (Globals.Environment != EnvironmentType.Production)
                {
                    if (!_trustOfficeOptions.InvitationWhiteList.Contains(applicationUser.Email, StringComparer.OrdinalIgnoreCase))
                    {
                        if (!MatchesWildcardInWhiteList(applicationUser.Email))
                        {
                            sendIt = false;
                            _logger.LogInformation("Don't send invitation for user '{DisplayName}' as this email address is not whitelisted", UserExtensions.GetDisplayName(applicationUser));
                        }
                    }
                }

                if (sendIt)
                {
                    // Send the invitation
                    await _communicationAppService.SendUserInvitationAsync(userId, masterClientCode, reregistration);

                    // Save the date that we sent the invitation
                    await _usersDataManager.SetAttributeValueAsync<DateTime>(userId, UserAttributeKeys.Invitation_Last_Sent, DateTime.Today);
                }
            }

            if (saveChanges)
            {
                await _masterClientUsersRepository.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Creates an invitation for the given user.
        /// </summary>
        /// <param name="userId">The id of the user to create the invitation for.</param>
        /// <param name="force">Denotes whether the invitation is forced (no conditions).</param>
        /// <param name="reregistration">Denotes whether it is for a reregistration.</param>
        /// <returns>The created QueuedJob.</returns>
        public QueuedJob CreateInvitationToUserJob(Guid userId, bool force = false, bool reregistration = false)
        {
            var data = new QueuedJobData { UserId = userId, Force = force, Reregistration = reregistration };
            return new QueuedJob(QueuedJobKeys.Invitation, JsonConvert.SerializeObject(data));
        }

        private bool MatchesWildcardInWhiteList(string emailAddress)
        {
            if (string.IsNullOrWhiteSpace(emailAddress))
            {
                return false;
            }

            var domain = emailAddress.Split('@').Last();
            return _trustOfficeOptions.InvitationWhiteList.Contains($"*@{domain}", StringComparer.OrdinalIgnoreCase);
        }
    }
}
