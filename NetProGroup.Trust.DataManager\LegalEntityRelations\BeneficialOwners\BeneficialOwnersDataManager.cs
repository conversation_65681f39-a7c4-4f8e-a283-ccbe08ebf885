﻿// <copyright file="BeneficialOwnersDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Communication;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Helpers;
using NetProGroup.Trust.DataManager.Jurisdictions;
using NetProGroup.Trust.DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses;
using NetProGroup.Trust.DataManager.LegalEntityRelations.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository.Sync;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Defines.BODirector;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.Jurisdictions;
using System.Diagnostics;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.LegalEntityRelations
{
    /// <summary>
    /// Manager for BeneficialOwners data.
    /// </summary>
    public class BeneficialOwnersDataManager : IBeneficialOwnersDataManager
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly ISystemAuditManager _systemAuditManager;

        private readonly ILegalEntitiesRepository _legalEntityRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IMasterClientsRepository _masterClientsRepository;

        private readonly IBeneficialOwnersRepository _beneficialOwnersRepository;
        private readonly IBeneficialOwnerHistoryRepository _beneficialOwnerHistoryRepository;

        private readonly ICommunicationAppService _communicationAppService;

        private readonly ILockManager _lockManager;
        private readonly IBulkOperationProvider _bulkOperationProvider;
        private LockDTO _jobLock;

        private List<BeneficialOwner> _beneficialOwnersToInsert = new List<BeneficialOwner>();
        private List<BeneficialOwner> _beneficialOwnersToDelete = new List<BeneficialOwner>();
        private List<BeneficialOwner> _beneficialOwnersToUpdate = new List<BeneficialOwner>();
        private List<BeneficialOwnerHistory> _beneficialOwnerHistoryToInsert = new List<BeneficialOwnerHistory>();
        private List<BeneficialOwnerHistory> _beneficialOwnerHistoryToDelete = new List<BeneficialOwnerHistory>();
        private List<Domain.Sync.SyncMessage> _syncMessagesToInsert = new List<Domain.Sync.SyncMessage>();
        private List<Guid> _syncingJurisdictionIds = new List<Guid>();

        /// <summary>
        /// Initializes a new instance of the <see cref="BeneficialOwnersDataManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="mapper">Mapper instance.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="systemAuditManager">Instance of the manager for audits.</param>
        /// <param name="repository">Instance of the repository.</param>
        /// <param name="jurisdictionsRepository">Instance of the Jurisdiction repository.</param>
        /// <param name="masterClientsRepository">Instance of the MasterClient repository.</param>
        /// <param name="beneficialOwnersRepository">Instance of the BeneficialOwner repository.</param>
        /// <param name="beneficialOwnerHistoryRepository">Instance of the BeneficialOwnerHistory repository.</param>
        /// <param name="communicationAppService">AppService for communication.</param>
        /// <param name="lockManager">Instance of the manager for locks.</param>
        /// <param name="bulkOperationProvider">Provider for bulk operations.</param>
        public BeneficialOwnersDataManager(ILogger<BeneficialOwnersDataManager> logger,
                                           IMapper mapper,
                                           IWorkContext workContext,
                                           ISystemAuditManager systemAuditManager,
                                           ILegalEntitiesRepository repository,
                                           IJurisdictionsRepository jurisdictionsRepository,
                                           IMasterClientsRepository masterClientsRepository,
                                           IBeneficialOwnersRepository beneficialOwnersRepository,
                                           IBeneficialOwnerHistoryRepository beneficialOwnerHistoryRepository,
                                           ICommunicationAppService communicationAppService,
                                           ILockManager lockManager,
                                           IBulkOperationProvider bulkOperationProvider)
        {
            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _systemAuditManager = systemAuditManager;

            _legalEntityRepository = repository;
            _jurisdictionsRepository = jurisdictionsRepository;
            _masterClientsRepository = masterClientsRepository;

            _beneficialOwnersRepository = beneficialOwnersRepository;
            _beneficialOwnerHistoryRepository = beneficialOwnerHistoryRepository;

            _communicationAppService = communicationAppService;
            _lockManager = lockManager;
            _bulkOperationProvider = Check.NotNull(bulkOperationProvider, nameof(bulkOperationProvider));
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerDTO> GetBeneficialOwnerAsync(string uniqueRelationId)
        {
            var result = await FindBeneficialOwnerAsync(uniqueRelationId);

            var history = await _beneficialOwnerHistoryRepository.GetCurrentBeneficialOwnerByUniqueRelationIdAsync(result.UniqueRelationCode);
            if (history != null)
            {
                var metaData = _mapper.Map<LegalEntityRelationMetaData>(history);
                result.MetaData = metaData;
            }

            CheckMissingInformation(result);

            return result;
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerDTO> GetBeneficialOwnerAsync(Guid beneficialOwnerId)
        {
            var item = await _beneficialOwnersRepository.FindFirstOrDefaultByConditionAsync(x => x.Id == beneficialOwnerId,
                                                                                            q => q.Include(d => d.LegalEntity));

            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.BENEFICIALOWNER_NOT_FOUND.ToErrorCode(), $"BeneficialOwner '{beneficialOwnerId}' was not found");
            }

            var result = _mapper.Map<BeneficialOwnerDTO>(item);

            var history = await _beneficialOwnerHistoryRepository.GetCurrentBeneficialOwnerByUniqueRelationIdAsync(result.UniqueRelationCode);
            if (history != null)
            {
                var metaData = _mapper.Map<LegalEntityRelationMetaData>(history);
                result.MetaData = metaData;
            }

            CheckMissingInformation(result);

            return result;
        }

        /// <inheritdoc />
        public async Task<BeneficialOwnerDTO> FindBeneficialOwnerAsync(string uniqueRelationId)
        {
            uniqueRelationId = await ToUniqueRelationIdAsync(uniqueRelationId);

            var item = await _beneficialOwnersRepository.FindFirstOrDefaultByConditionAsync(x => x.ExternalUniqueId == uniqueRelationId,
                q => q.Include(d => d.LegalEntity));

            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.BENEFICIALOWNER_NOT_FOUND.ToErrorCode(), $"BeneficialOwner '{uniqueRelationId}' was not found");
            }

            var result = _mapper.Map<BeneficialOwnerDTO>(item);
            return result;
        }

        /// <inheritdoc />
        public async Task<BeneficialOwnerDTO> CheckBeneficialOwnerByIdAsync(Guid id)
        {
            var item = await _beneficialOwnersRepository.CheckBeneficialOwnerByIdAsync(id);

            var result = _mapper.Map<BeneficialOwnerDTO>(item);
            return result;
        }

        /// <inheritdoc/>
        public async Task<ListBeneficialOwnersResponse> ListBeneficialOwnersAsync(ListBeneficialOwnersRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var items = await _beneficialOwnersRepository.FindByConditionAsync(x => x.LegalEntityId == request.LegalEntityId,
                                                                               q => q.Include(d => d.LegalEntity));

            var itemsPaged = new PagedList<BeneficialOwner>(items, request.PageNumber, request.PageSize);

            var dtos = _mapper.Map<List<BeneficialOwnerDTO>>(itemsPaged.ToList());

            var dtosPaged = new StaticPagedList<BeneficialOwnerDTO>(dtos, request.PageNumber, request.PageSize, items.Count());

            // Augment with metadata
            foreach (var dto in dtosPaged)
            {
                CheckMissingInformation(dto);

                if (request.IncludeMetaData)
                {
                    var history = await _beneficialOwnerHistoryRepository.GetCurrentBeneficialOwnerByUniqueRelationIdAsync(dto.UniqueRelationCode);
                    if (history != null)
                    {
                        var metaData = _mapper.Map<LegalEntityRelationMetaData>(history);
                        dto.MetaData = metaData;
                    }
                }
            }

            var result = new ListBeneficialOwnersResponse { BeneficialOwnerItems = dtosPaged };
            return result;
        }

        /// <inheritdoc/>
        public async Task<ConfirmationResponse> ConfirmDataAsync(ConfirmationRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var uniqueRelationId = await ToUniqueRelationIdAsync(request.UniqueRelationId);

            var item = await _beneficialOwnerHistoryRepository.GetCurrentBeneficialOwnerByUniqueRelationIdAsync(uniqueRelationId);
            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.BENEFICIALOWNER_NOT_FOUND.ToErrorCode(), $"BeneficialOwner '{uniqueRelationId}' was not found");
            }

            item = Clone(item);
            item.ConfirmData(request);
            await _beneficialOwnerHistoryRepository.InsertAsync(item);

            await _systemAuditManager.AddActivityLogAsync(item, ActivityLogActivityTypes.BeneficialOwnerDataConfirmed, "Data confirmed.", $"The data for Beneficial Owner {item.Name} is confirmed.");

            await _beneficialOwnerHistoryRepository.SaveChangesAsync();

            return new ConfirmationResponse();
        }

        /// <inheritdoc/>
        public async Task<RequestUpdateResponse> RequestUpdateAsync(RequestUpdateRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var uniqueRelationId = await ToUniqueRelationIdAsync(request.UniqueRelationId);

            var item = await _beneficialOwnerHistoryRepository.GetCurrentBeneficialOwnerByUniqueRelationIdAsync(uniqueRelationId);
            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.BENEFICIALOWNER_NOT_FOUND.ToErrorCode(), $"BeneficialOwner '{uniqueRelationId}' was not found");
            }

            item = Clone(item);
            item.RequestUpdate(request);

            await _beneficialOwnerHistoryRepository.InsertAsync(item, saveChanges: false);

            await _systemAuditManager.AddActivityLogAsync(item, ActivityLogActivityTypes.BeneficialOwnerDataUpdateRequested, "Update requested.", $"An update is requested for Beneficial Owner {item.Name}.");

            var tokens = CreateTokens(item);

            var productionOffice = string.Empty;

            if (item.LegalEntity != null)
            {
                productionOffice = item.LegalEntity.ProductionOffice == null ? string.Empty : item.LegalEntity.ProductionOffice;
            }

            // Send an email
            await _communicationAppService.SendRequestForUpdateAsync(productionOffice, tokens);

            await _beneficialOwnersRepository.SaveChangesAsync();

            return new RequestUpdateResponse();
        }

        /// <inheritdoc/>
        public async Task<RequestAssistanceResponse> RequestAssistanceAsync(RequestAssistanceRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var legalEntity = await _legalEntityRepository.GetByIdAsync(request.LegalEntityId, q => q.Include(le => le.MasterClient));
            if (legalEntity == null)
            {
                throw new NetProGroup.Framework.Exceptions.NotFoundException(ApplicationErrors.COMPANY_NOT_FOUND.ToErrorCode(), "Company not found");
            }

            await _systemAuditManager.AddActivityLogAsync(legalEntity, ActivityLogActivityTypes.ShareholderAssistanceRequested, "Assistance requested.", $"Assistance is requested for BeneficialOwner of '{legalEntity.Name}'.");

            var tokens = CreateTokens(legalEntity, request);

            var productionOffice = legalEntity.ProductionOffice == null ? string.Empty : legalEntity.ProductionOffice;

            // Send an email
            await _communicationAppService.SendRequestForAssistanceAsync(productionOffice, tokens);

            await _legalEntityRepository.SaveChangesAsync();

            return new RequestAssistanceResponse();
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerComparisonDTO> GetBeneficialOwnerForComparisonAsync(string uniqueRelationId)
        {
            var result = new BeneficialOwnerComparisonDTO();

            uniqueRelationId = await ToUniqueRelationIdAsync(uniqueRelationId);

            // Get the current
            var item = await _beneficialOwnerHistoryRepository.GetCurrentBeneficialOwnerByUniqueRelationIdAsync(uniqueRelationId);

            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.BENEFICIALOWNER_NOT_FOUND.ToErrorCode(), $"BeneficialOwner '{uniqueRelationId}' was not found");
            }

            result.CurrentVersion = _mapper.Map<BeneficialOwnerDTO>(item);

            if (item.Status == LegalEntityRelationStatus.Refreshed || item.Status == LegalEntityRelationStatus.UpdateReceived)
            {
                // And get the last with one of the following states
                var statuses = new LegalEntityRelationStatus[] { LegalEntityRelationStatus.Confirmed, LegalEntityRelationStatus.PendingUpdateRequest, LegalEntityRelationStatus.Initial };

                item = await _beneficialOwnerHistoryRepository.GetLastBeneficialOwnerByUniqueRelationIdAndStatusAsync(item.ExternalUniqueId, statuses);
                result.PriorVersion = _mapper.Map<BeneficialOwnerDTO>(item);
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task<BeneficialOwnerComparisonDTO> GetBeneficialOwnerForComparisonAsync(Guid beneficialOwnerId)
        {
            var result = new BeneficialOwnerComparisonDTO();

            var itemById = await _beneficialOwnersRepository.FindFirstOrDefaultByConditionAsync(x => x.Id == beneficialOwnerId);

            if (itemById == null)
            {
                throw new NotFoundException(ApplicationErrors.DIRECTOR_NOT_FOUND.ToErrorCode(), $"BeneficialOwner '{beneficialOwnerId}' was not found");
            }

            var item = await _beneficialOwnerHistoryRepository.GetCurrentBeneficialOwnerByUniqueRelationIdAsync(itemById.ExternalUniqueId);

            result.CurrentVersion = _mapper.Map<BeneficialOwnerDTO>(item);

            if (item.Status == LegalEntityRelationStatus.Refreshed || item.Status == LegalEntityRelationStatus.UpdateReceived)
            {
                // And get the last with one of the following states
                var statuses = new LegalEntityRelationStatus[] { LegalEntityRelationStatus.Confirmed, LegalEntityRelationStatus.PendingUpdateRequest, LegalEntityRelationStatus.Initial };

                item = await _beneficialOwnerHistoryRepository.GetLastBeneficialOwnerByUniqueRelationIdAndStatusAsync(item.ExternalUniqueId, statuses);
                result.PriorVersion = _mapper.Map<BeneficialOwnerDTO>(item);
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task SyncBeneficialOwnersAsync(ICollection<SyncBeneficialOwner> beneficialOwners)
        {
            ArgumentNullException.ThrowIfNull(beneficialOwners, nameof(beneficialOwners));

            var legalEntitiesById = new Dictionary<Guid, LegalEntity>();
            var legaEntities = new Dictionary<string, LegalEntity>(StringComparer.OrdinalIgnoreCase);
            var beneficialOwnersByLegalEntity = new Dictionary<Guid, IList<SyncBeneficialOwner>>();

            foreach (var beneficialOwner in beneficialOwners)
            {
                var key = beneficialOwner.CompanyNumber;

                if (!legaEntities.TryGetValue(key, out LegalEntity legalEntity))
                {
                    legalEntity = await _legalEntityRepository.FindFirstOrDefaultByConditionAsync(le => le.Code == key);
                    if (legalEntity == null)
                    {
                        throw new ConstraintException($"LegalEntity with code '{key}' does not exist");
                    }

                    legaEntities[key] = legalEntity;
                    legalEntitiesById[legalEntity.Id] = legalEntity;
                }

                if (!beneficialOwnersByLegalEntity.TryGetValue(legalEntity.Id, out IList<SyncBeneficialOwner> value))
                {
                    value = new List<SyncBeneficialOwner>();
                    beneficialOwnersByLegalEntity.Add(legalEntity.Id, value);
                }

                value.Add(beneficialOwner);
            }

            foreach (var key in beneficialOwnersByLegalEntity.Keys)
            {
                await SyncBeneficialOwnersAsync(legalEntitiesById[key],
                                                beneficialOwnersByLegalEntity[key],
                                                new List<SyncBeneficialOwner>(),
                                                null);
            }
        }

        /// <inheritdoc/>
        public async Task SyncBeneficialOwnersAsync(SyncBeneficialOwnerRequest request, LockDTO jobLock = null, Func<DbContext, Task> beforeCommitAsync = null)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            _logger.LogInformation("Found {CountChanged} changed bo's and {CountRemoved} removed bo's", request.ChangedBeneficialOwners.Count, request.RemovedBeneficialOwners.Count);

            _jobLock = jobLock;

            ClearBulkData();

            _syncingJurisdictionIds = await GetSyncingJurisdictionIdsAsync();

            var allBeneficialOwners = await _beneficialOwnersRepository.FindAllAsync();
            var allBeneficialOwnersByUniqueCode = allBeneficialOwners.ToDictionary(e => e.ExternalUniqueId);

            var allLegalEntitiesByCode = (await _legalEntityRepository.FindAllAsync(o => o.Include(le => le.Jurisdiction))).ToDictionary(e => e.Code);

            var changedBeneficialOwnersByEntityCode = new Dictionary<string, List<SyncBeneficialOwner>>(StringComparer.OrdinalIgnoreCase);
            var removedBeneficialOwnersByEntityCode = new Dictionary<string, List<SyncBeneficialOwner>>(StringComparer.OrdinalIgnoreCase);

            // We need the EntityCode for the removed directors
            var removedExternalUniqueIds = request.RemovedBeneficialOwners.Select(rd => rd.UniqueRelationId);
            var beneficialOwnersToRemove = (await _beneficialOwnersRepository.FindByConditionAsync(d => removedExternalUniqueIds.Contains(d.ExternalUniqueId), q => q.Include(d => d.LegalEntity))).ToDictionary(d => d.ExternalUniqueId);
            foreach (var beneficialOwner in request.RemovedBeneficialOwners)
            {
                if (beneficialOwnersToRemove.TryGetValue(beneficialOwner.UniqueRelationId, out var value))
                {
                    beneficialOwner.EntityCode = value.LegalEntity.Code;
                }
            }

            // Collect all code for the entities that we need to check the BOs for
            var entityCodes = request.ChangedBeneficialOwners.Select(le => le.EntityCode).Distinct().ToList();
            entityCodes.AddRange(request.RemovedBeneficialOwners.Select(le => le.EntityCode.GetValueOrDefault("NULL")).Distinct().ToList());
            entityCodes = entityCodes.Distinct().ToList();

            foreach (var entityCode in entityCodes)
            {
                if (!allLegalEntitiesByCode.ContainsKey(entityCode))
                {
                    // ToDo: Log error?
                    // throw new BadRequestException(ApplicationErrors.COMPANY_NOT_FOUND.ToErrorCode(), $"LegalEntity with code '{entityCode}' not found");
                    _logger.LogWarning("Entity {EntityCode} not found (check status of entity in staging)", entityCode);
                }

                changedBeneficialOwnersByEntityCode[entityCode] = new List<SyncBeneficialOwner>();
                removedBeneficialOwnersByEntityCode[entityCode] = new List<SyncBeneficialOwner>();
            }

            var processedCodes = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            foreach (var bo in request.ChangedBeneficialOwners)
            {
                if (!processedCodes.Contains(bo.UniqueRelationId))
                {
                    changedBeneficialOwnersByEntityCode[bo.EntityCode].Add(bo);
                    processedCodes.Add(bo.UniqueRelationId);
                }
            }

            foreach (var bo in request.RemovedBeneficialOwners)
            {
                removedBeneficialOwnersByEntityCode[bo.EntityCode].Add(bo);
            }

            foreach (var entityCode in entityCodes)
            {
                if (allLegalEntitiesByCode.TryGetValue(entityCode, out var legalEntity))
                {
                    if (_syncingJurisdictionIds.Contains(legalEntity.JurisdictionId.Value))
                    {
                        await SyncBeneficialOwnersAsync(legalEntity,
                                                        changedBeneficialOwnersByEntityCode[entityCode],
                                                        removedBeneficialOwnersByEntityCode[entityCode],
                                                        allBeneficialOwnersByUniqueCode);
                    }
                }
                else
                {
                    // Log?
                }
            }

            // Start a transaction for 'all or nothing'
            using var transaction = await _beneficialOwnersRepository.DbContext.Database.BeginTransactionAsync();
            {
                try
                {
                    if (_beneficialOwnersRepository.DbContext.Database.IsRelational())
                    {
                        _beneficialOwnersRepository.DbContext.Database.SetCommandTimeout(300);
                    }

                    _logger.LogInformation("Start saving beneficialowner data (in a Tx)...");

                    if (_beneficialOwnersToInsert.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkInsert for {Count} beneficialowners...", _beneficialOwnersToInsert.Count);
                        await _bulkOperationProvider.BulkInsertAsync(_beneficialOwnersToInsert, _beneficialOwnersRepository.DbContext);
                        _logger.LogInformation("BulkInsert finished");
                    }
                    else
                    {
                        _logger.LogInformation("No beneficialowners to insert");
                    }

                    if (_beneficialOwnersToUpdate.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkUpdate for {Count} beneficialowners...", _beneficialOwnersToUpdate.Count);
                        await _bulkOperationProvider.BulkUpdateAsync(_beneficialOwnersToUpdate, _beneficialOwnersRepository.DbContext);
                        _logger.LogInformation("BulkUpdate finished");
                    }
                    else
                    {
                        _logger.LogInformation("No beneficialowners to update");
                    }

                    if (_beneficialOwnersToDelete.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkDelete for {Count} beneficialowners...", _beneficialOwnersToDelete.Count);
                        await _bulkOperationProvider.BulkDeleteAsync(_beneficialOwnersToDelete, _beneficialOwnersRepository.DbContext);
                        _logger.LogInformation("BulkDelete finished");
                    }
                    else
                    {
                        _logger.LogInformation("No benficialowners to delete");
                    }

                    if (_beneficialOwnerHistoryToInsert.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkInsert for {Count} beneficialowner history...", _beneficialOwnerHistoryToInsert.Count);
                        await _bulkOperationProvider.BulkInsertAsync(_beneficialOwnerHistoryToInsert, _beneficialOwnerHistoryRepository.DbContext);
                        _logger.LogInformation("BulkInsert finished");
                    }
                    else
                    {
                        _logger.LogInformation("No beneficialownerhistory to insert");
                    }

                    if (_beneficialOwnerHistoryToDelete.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkDelete for {Count} beneficialowner history...", _beneficialOwnerHistoryToDelete.Count);
                        await _bulkOperationProvider.BulkDeleteAsync(_beneficialOwnerHistoryToDelete, _beneficialOwnerHistoryRepository.DbContext);
                        _logger.LogInformation("BulkDelete finished");
                    }
                    else
                    {
                        _logger.LogInformation("No beneficialowner history to delete");
                    }

                    if (_syncMessagesToInsert.Count > 0)
                    {
                        _logger.LogInformation("Starting BulkInsert for {Count} messages...", _syncMessagesToInsert.Count);
                        await _bulkOperationProvider.BulkInsertAsync(_syncMessagesToInsert, _beneficialOwnerHistoryRepository.DbContext);
                        _logger.LogInformation("BulkInsert finished");
                    }
                    else
                    {
                        _logger.LogInformation("No messages to insert");
                    }

                    if (beforeCommitAsync != null)
                    {
                        await beforeCommitAsync(_beneficialOwnersRepository.DbContext);
                    }

                    await transaction.CommitAsync();

                    _logger.LogInformation("Done saving data");
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    _logger.LogError(ex, "Error saving data (Tx rolled back)");
                    throw;
                }
                finally
                {
                    ClearBulkData();
                }
            }
        }

        /// <summary>
        /// Synchronizes beneficial owners for a legal entity by processing changed and removed beneficial owner data.
        /// </summary>
        /// <param name="legalEntity">The legal entity to synchronize beneficial owners for.</param>
        /// <param name="changedBeneficialOwnersSync">The list of beneficial owners that have been changed or added.</param>
        /// <param name="removedBeneficialOwnersSync">The list of beneficial owners that have been removed.</param>
        /// <param name="allBeneficialOwnersByUniqueCode">Optional dictionary of existing beneficial owners indexed by unique code for performance optimization.</param>
        /// <returns>A task that represents the asynchronous synchronization operation.</returns>
        public async Task SyncBeneficialOwnersAsync(LegalEntity legalEntity,
                                                    IList<SyncBeneficialOwner> changedBeneficialOwnersSync,
                                                    IList<SyncBeneficialOwner> removedBeneficialOwnersSync,
                                                    Dictionary<string, BeneficialOwner> allBeneficialOwnersByUniqueCode)
        {
            ArgumentNullException.ThrowIfNull(legalEntity, nameof(legalEntity));
            ArgumentNullException.ThrowIfNull(changedBeneficialOwnersSync, nameof(changedBeneficialOwnersSync));
            ArgumentNullException.ThrowIfNull(removedBeneficialOwnersSync, nameof(removedBeneficialOwnersSync));

            // Get the current BeneficialOwners for the LegalEntity if not passed.
            List<BeneficialOwner> currentBeneficialOwners = null;
            if (allBeneficialOwnersByUniqueCode == null)
            {
                currentBeneficialOwners = (await _beneficialOwnersRepository.FindByConditionAsync(x => x.LegalEntityId == legalEntity.Id)).ToList();
            }

            // List for the BeneficialOwners history for the LegalEntity.
            List<BeneficialOwnerHistory> historyBeneficialOwners = null;

            var sw = new Stopwatch();
            sw.Start();

            var processedCodes = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            // Get each BeneficialOwner from the request and compare to the current if any.
            // If ExternalUniqueId not in current then add one now and add a copy to history with status 'INITIAL'.
            // If exists in current but changed then add a record to history with the status from the last history ('REFRESHED' or 'VP DATA RECEIVED', if 'INITIAL' then 'REFRESHED').
            foreach (var syncBeneficialOwner in changedBeneficialOwnersSync)
            {
                if (_jobLock != null && sw.Elapsed.TotalSeconds > 60)
                {
                    await _lockManager.RefreshLockAsync(_jobLock.Id.Value);
                    sw.Restart();
                }

                if (processedCodes.Contains(syncBeneficialOwner.UniqueRelationId))
                {
                    _logger.LogWarning("Duplicate UniqueRelationId {UniqueRelationId} (code {BOCode})", syncBeneficialOwner.UniqueRelationId, syncBeneficialOwner.Code);
                    continue;
                }

                processedCodes.Add(syncBeneficialOwner.UniqueRelationId);

                // Fix OfficerTypeCode and OfficerTypeName when missing
                if (string.IsNullOrEmpty(syncBeneficialOwner.OfficerTypeCode))
                {
                    var (defaultOfficerTypeCode, defaultOfficerTypeName) = GetDefaultOfficerType(legalEntity);
                    syncBeneficialOwner.OfficerTypeCode = defaultOfficerTypeCode;
                    if (string.IsNullOrEmpty(syncBeneficialOwner.OfficerTypeName))
                    {
                        syncBeneficialOwner.OfficerTypeName = defaultOfficerTypeName;
                    }
                }

                // Get current and last history
                BeneficialOwner currentBeneficialOwner = null;
                BeneficialOwnerHistory lastBeneficialOwnerHistory = null;
                if (allBeneficialOwnersByUniqueCode == null)
                {
                    currentBeneficialOwner = currentBeneficialOwners.Where(x => x.ExternalUniqueId.Equals(syncBeneficialOwner.UniqueRelationId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                }
                else
                {
                    allBeneficialOwnersByUniqueCode.TryGetValue(syncBeneficialOwner.UniqueRelationId, out currentBeneficialOwner);
                }

                if (currentBeneficialOwner == null)
                {
                    _logger.LogDebug("Creating new BeneficialOwner {BOCode}", syncBeneficialOwner.Code);

                    // Initial creation of BeneficialOwner and BeneficialOwnerHistory
                    currentBeneficialOwner = UpsertBeneficialOwner(legalEntity.Id, null, syncBeneficialOwner);
                    CreateBeneficialOwnerHistory(legalEntity.Id, syncBeneficialOwner, LegalEntityRelationStatus.Initial, currentBeneficialOwner);
                }
                else
                {
                    // First call?
                    if (historyBeneficialOwners == null)
                    {
                        historyBeneficialOwners = await _beneficialOwnerHistoryRepository.ListCurrentBeneficialOwnersByLegalEntityIdAsync(legalEntity.Id);
                    }

                    lastBeneficialOwnerHistory = historyBeneficialOwners.Where(x => x.ExternalUniqueId.Equals(syncBeneficialOwner.UniqueRelationId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();

                    // Should not happen but just in case...
                    bool forceUpsert = false;
                    if (lastBeneficialOwnerHistory == null)
                    {
                        lastBeneficialOwnerHistory = CreateBeneficialOwnerHistory(legalEntity.Id, syncBeneficialOwner, LegalEntityRelationStatus.Initial, currentBeneficialOwner);
                        forceUpsert = true;
                    }

                    switch (lastBeneficialOwnerHistory.Status)
                    {
                        // If last history has status 'INITIAL', 'REFRESHED' or 'VP DATA RECEIVED' and any change then create new history and update the BODirector.
                        case LegalEntityRelationStatus.Initial:
                        case LegalEntityRelationStatus.Refreshed:
                        case LegalEntityRelationStatus.UpdateReceived:
                            {
                                if (RelationChanged(lastBeneficialOwnerHistory, syncBeneficialOwner) ||
                                    MetaDataChanged(lastBeneficialOwnerHistory, syncBeneficialOwner))
                                {
                                    UpsertBeneficialOwner(legalEntity.Id, currentBeneficialOwner, syncBeneficialOwner);

                                    var status = lastBeneficialOwnerHistory.Status;
                                    if (status == LegalEntityRelationStatus.Initial)
                                    {
                                        status = LegalEntityRelationStatus.Refreshed;
                                    }

                                    CreateBeneficialOwnerHistory(legalEntity.Id, syncBeneficialOwner, status, currentBeneficialOwner);
                                }
                                else if (forceUpsert)
                                {
                                    UpsertBeneficialOwner(legalEntity.Id, currentBeneficialOwner, syncBeneficialOwner);
                                }

                                break;
                            }

                        // If last history has 'PendingUpdateRequest' or 'Confirmed' then set to 'UpdateReceived' if data changed
                        case LegalEntityRelationStatus.PendingUpdateRequest:
                        case LegalEntityRelationStatus.Confirmed:
                            {
                                if (RelationChanged(lastBeneficialOwnerHistory, syncBeneficialOwner))
                                {
                                    // BeneficialOwner data changed. Create a new history and set status to 'VP DATA RECEIVED'
                                    UpsertBeneficialOwner(legalEntity.Id, currentBeneficialOwner, syncBeneficialOwner);
                                    CreateBeneficialOwnerHistory(legalEntity.Id, syncBeneficialOwner, LegalEntityRelationStatus.UpdateReceived, currentBeneficialOwner);
                                }
                                else if (MetaDataChanged(lastBeneficialOwnerHistory, syncBeneficialOwner))
                                {
                                    // Only 'metadata' changed. Create a new history but keep the status
                                    UpsertBeneficialOwner(legalEntity.Id, currentBeneficialOwner, syncBeneficialOwner);
                                    CreateBeneficialOwnerHistory(legalEntity.Id, syncBeneficialOwner, lastBeneficialOwnerHistory.Status, currentBeneficialOwner);
                                }

                                break;
                            }
                    }
                }
            }

            // Delete the BeneficialOwners no longer in the sync, history is kept
            // Do only when the entity is in the jurisdiction that we are syncing for
            if (_syncingJurisdictionIds.Contains(legalEntity.JurisdictionId.Value))
            {
                if (allBeneficialOwnersByUniqueCode != null && removedBeneficialOwnersSync != null)
                {
                    foreach (var bo in removedBeneficialOwnersSync)
                    {
                        if (allBeneficialOwnersByUniqueCode.TryGetValue(bo.UniqueRelationId, out var beneficialOwner))
                        {
                            _beneficialOwnersToDelete.Add(beneficialOwner);

                            var history = await _beneficialOwnerHistoryRepository.FindByConditionAsync(dh => dh.ExternalUniqueId == bo.UniqueRelationId);
                            _beneficialOwnerHistoryToDelete.AddRange(history);
                        }
                    }
                }
            }
        }

        private static BeneficialOwnerHistory Clone(BeneficialOwnerHistory source)
        {
            ArgumentNullException.ThrowIfNull(source, nameof(source));

            var result = new BeneficialOwnerHistory(Guid.NewGuid());
            source.CopyProperties(result);

            result.CreatedAt = DateTime.UtcNow;
            result.UpdatedAt = DateTime.UtcNow;

            return result;
        }

        /// <summary>
        /// Returns true if any of the BeneficialOwner fields does not match.
        /// </summary>
        /// <param name="current">The current BeneficialOwnerHistory.</param>
        /// <param name="syncBeneficialOwner">The importing SyncBeneficialOwner.</param>
        /// <returns>True if at least 1 BeneficialOwnerHistory field changed.</returns>
        private static bool RelationChanged(BeneficialOwnerHistory current, SyncBeneficialOwner syncBeneficialOwner)
        {
            var isIndividual = syncBeneficialOwner.FileType.Equals("individual", StringComparison.OrdinalIgnoreCase);

            if (CompareHelper.ValueChanged(current.Code, syncBeneficialOwner.Code) ||
                CompareHelper.ValueChanged(current.Name, syncBeneficialOwner.Name) ||
                CompareHelper.ValueChanged(current.FormerName, syncBeneficialOwner.FormerName) ||
                CompareHelper.ValueChanged(current.Nationality, syncBeneficialOwner.Nationality) ||
                CompareHelper.ValueChanged(current.TIN, syncBeneficialOwner.TIN) ||
                CompareHelper.ValueChanged(current.NameOfRegulator, syncBeneficialOwner.NameOfRegulator) ||
                CompareHelper.ValueChanged(current.StockExchangeCode, syncBeneficialOwner.StockCode) ||
                CompareHelper.ValueChanged(current.StockExchangeName, syncBeneficialOwner.StockExchange) ||
                CompareHelper.ValueChanged(current.OfficerTypeCode, syncBeneficialOwner.OfficerTypeCode) ||
                CompareHelper.ValueChanged(current.OfficerTypeName, syncBeneficialOwner.OfficerTypeName) ||

                CompareHelper.ValueChanged(current.IncorporationNr, syncBeneficialOwner.BoDirIncorporationNumber) ||

                CompareHelper.ValueChanged(current.Country, syncBeneficialOwner.Country) ||

                CompareHelper.ValueChanged(current.AppointmentDate, syncBeneficialOwner.FromDate) ||
                CompareHelper.ValueChanged(current.CessationDate, syncBeneficialOwner.ToDate) ||

                CompareHelper.ValueChanged(current.ServiceAddress, syncBeneficialOwner.ServiceAddress) ||

                CompareHelper.ValueChanged(current.FileType, syncBeneficialOwner.FileType) ||
                CompareHelper.ValueChanged(current.IsIndividual, isIndividual))
            {
                return true;
            }

            if (isIndividual)
            {
                if (CompareHelper.ValueChanged(current.DateOfBirth.AsNullable(), syncBeneficialOwner.DateOfBirthOrIncorp.AsNullable()) ||
                    CompareHelper.ValueChanged(current.PlaceOfBirth, syncBeneficialOwner.PlaceOfBirthOrIncorp) ||
                    CompareHelper.ValueChanged(current.CountryOfBirth, syncBeneficialOwner.CountryOfBirthOrIncorp) ||
                    CompareHelper.ValueChanged(current.CountryOfBirthCode, syncBeneficialOwner.CountryCodeOfBirthOrIncorp) ||
                    CompareHelper.ValueChanged(current.ResidentialAddress, syncBeneficialOwner.ResidentialOrRegisteredAddress))
                {
                    return true;
                }
            }
            else
            {
                if (CompareHelper.ValueChanged(current.IncorporationDate.AsNullable(), syncBeneficialOwner.DateOfBirthOrIncorp.AsNullable()) ||
                    CompareHelper.ValueChanged(current.Address, syncBeneficialOwner.ResidentialOrRegisteredAddress) ||
                    CompareHelper.ValueChanged(current.SovereignState, syncBeneficialOwner.JurisdictionOfRegulationOrSovereignState) ||
                    CompareHelper.ValueChanged(current.JurisdictionOfRegulator, syncBeneficialOwner.JurisdictionOfRegulationOrSovereignState))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Returns true if any of the MetaData fields does not match.
        /// </summary>
        /// <param name="current">The current BeneficialOwner.</param>
        /// <param name="syncBeneficialOwner">The importing SyncBeneficialOwner.</param>
        /// <returns>True if at least 1 MetData field changed.</returns>
        private static bool MetaDataChanged(BeneficialOwnerHistory current, SyncBeneficialOwner syncBeneficialOwner)
        {
            /*if (CompareHelper.ValueChanged(current.ma, importBODirector.MasterClientCode) ||
                CompareHelper.ValueChanged(current.CompanyNumber, importBODirector.CompanyNumber) ||
                CompareHelper.ValueChanged(current.EntityCode, importBODirector.EntityCode) ||
                CompareHelper.ValueChanged(current.EntityName, importBODirector.EntityName) ||
                CompareHelper.ValueChanged(current.Code, importBODirector.Code))
            {
                return true;
            }*/

            return false;
        }

        private static void CheckMissingInformation(BeneficialOwnerDTO beneficialOwner)
        {
            switch (beneficialOwner.OfficerTypeCode.ToUpper())
            {
                case BODirectorOfficerTypeCode.VGTP01:
                    {
                        CheckMissing(beneficialOwner, beneficialOwner.Name, nameof(beneficialOwner.Name));
                        CheckMissing(beneficialOwner, beneficialOwner.DateOfBirth, nameof(beneficialOwner.DateOfBirth));
                        CheckMissing(beneficialOwner, beneficialOwner.PlaceOfBirth, nameof(beneficialOwner.PlaceOfBirth));
                        CheckMissing(beneficialOwner, beneficialOwner.Nationality, nameof(beneficialOwner.Nationality));
                        CheckMissing(beneficialOwner, beneficialOwner.ResidentialAddress, nameof(beneficialOwner.ResidentialAddress));
                        break;
                    }

                case BODirectorOfficerTypeCode.VGTP02:
                    {
                        CheckMissing(beneficialOwner, beneficialOwner.Name, nameof(beneficialOwner.Name));
                        CheckMissing(beneficialOwner, beneficialOwner.IncorporationNumber, nameof(beneficialOwner.IncorporationNumber));
                        CheckMissing(beneficialOwner, beneficialOwner.DateOfIncorporation, nameof(beneficialOwner.DateOfIncorporation));
                        CheckMissing(beneficialOwner, beneficialOwner.Address, nameof(beneficialOwner.Address));
                        CheckMissing(beneficialOwner, beneficialOwner.CountryOfFormation, nameof(beneficialOwner.CountryOfFormation));
                        break;
                    }

                case BODirectorOfficerTypeCode.VGTP03:
                    {
                        CheckMissing(beneficialOwner, beneficialOwner.Name, nameof(beneficialOwner.Name));
                        CheckMissing(beneficialOwner, beneficialOwner.IncorporationNumber, nameof(beneficialOwner.IncorporationNumber));
                        CheckMissing(beneficialOwner, beneficialOwner.DateOfIncorporation, nameof(beneficialOwner.DateOfIncorporation));
                        CheckMissing(beneficialOwner, beneficialOwner.Address, nameof(beneficialOwner.Address));
                        CheckMissing(beneficialOwner, beneficialOwner.CountryOfFormation, nameof(beneficialOwner.CountryOfFormation));
                        CheckMissing(beneficialOwner, beneficialOwner.NameOfRegulator, nameof(beneficialOwner.NameOfRegulator));
                        CheckMissing(beneficialOwner, beneficialOwner.JurisdictionOfRegulator, nameof(beneficialOwner.JurisdictionOfRegulator));
                        break;
                    }

                case BODirectorOfficerTypeCode.VGTP04:
                    {
                        CheckMissing(beneficialOwner, beneficialOwner.Name, nameof(beneficialOwner.Name));
                        CheckMissing(beneficialOwner, beneficialOwner.IncorporationNumber, nameof(beneficialOwner.IncorporationNumber));
                        CheckMissing(beneficialOwner, beneficialOwner.DateOfIncorporation, nameof(beneficialOwner.DateOfIncorporation));
                        CheckMissing(beneficialOwner, beneficialOwner.Address, nameof(beneficialOwner.Address));
                        CheckMissing(beneficialOwner, beneficialOwner.CountryOfFormation, nameof(beneficialOwner.CountryOfFormation));
                        CheckMissing(beneficialOwner, beneficialOwner.SovereignState, nameof(beneficialOwner.SovereignState));
                        break;
                    }

                case BODirectorOfficerTypeCode.VGTP05:
                    {
                        CheckMissing(beneficialOwner, beneficialOwner.Name, nameof(beneficialOwner.Name));
                        CheckMissing(beneficialOwner, beneficialOwner.IncorporationNumber, nameof(beneficialOwner.IncorporationNumber));
                        CheckMissing(beneficialOwner, beneficialOwner.DateOfIncorporation, nameof(beneficialOwner.DateOfIncorporation));
                        CheckMissing(beneficialOwner, beneficialOwner.Address, nameof(beneficialOwner.Address));
                        CheckMissing(beneficialOwner, beneficialOwner.CountryOfFormation, nameof(beneficialOwner.CountryOfFormation));
                        CheckMissing(beneficialOwner, beneficialOwner.StockCode, nameof(beneficialOwner.StockCode));
                        CheckMissing(beneficialOwner, beneficialOwner.StockExchange, nameof(beneficialOwner.StockExchange));
                        break;
                    }

                case BODirectorOfficerTypeCode.VGTP06:
                    {
                        CheckMissing(beneficialOwner, beneficialOwner.Name, nameof(beneficialOwner.Name));
                        CheckMissing(beneficialOwner, beneficialOwner.IncorporationNumber, nameof(beneficialOwner.IncorporationNumber));
                        CheckMissing(beneficialOwner, beneficialOwner.DateOfIncorporation, nameof(beneficialOwner.DateOfIncorporation));
                        CheckMissing(beneficialOwner, beneficialOwner.Address, nameof(beneficialOwner.Address));
                        CheckMissing(beneficialOwner, beneficialOwner.CountryOfFormation, nameof(beneficialOwner.CountryOfFormation));
                        break;
                    }

                case BODirectorOfficerTypeCode.KNTP01:
                    {
                        CheckMissing(beneficialOwner, beneficialOwner.Name, nameof(beneficialOwner.Name));
                        CheckMissing(beneficialOwner, beneficialOwner.DateOfBirth, nameof(beneficialOwner.DateOfBirth));
                        CheckMissing(beneficialOwner, beneficialOwner.CountryOfBirth, nameof(beneficialOwner.CountryOfBirth));
                        CheckMissing(beneficialOwner, beneficialOwner.Nationality, nameof(beneficialOwner.Nationality));
                        CheckMissing(beneficialOwner, beneficialOwner.ResidentialAddress, nameof(beneficialOwner.ResidentialAddress));
                        break;
                    }

                case BODirectorOfficerTypeCode.KNTP02:
                case BODirectorOfficerTypeCode.KNTP03:
                case BODirectorOfficerTypeCode.KNTP04:
                case BODirectorOfficerTypeCode.KNTP05:
                case BODirectorOfficerTypeCode.KNTP06:
                    {
                        CheckMissing(beneficialOwner, beneficialOwner.Name, nameof(beneficialOwner.Name));
                        CheckMissing(beneficialOwner, beneficialOwner.IncorporationNumber, nameof(beneficialOwner.IncorporationNumber));
                        CheckMissing(beneficialOwner, beneficialOwner.DateOfIncorporation, nameof(beneficialOwner.DateOfIncorporation));
                        CheckMissing(beneficialOwner, beneficialOwner.CountryOfFormation, nameof(beneficialOwner.CountryOfFormation));
                        CheckMissing(beneficialOwner, beneficialOwner.Address, nameof(beneficialOwner.Address));
                        break;
                    }
            }
        }

        private static void CheckMissing(LegalEntityRelationDTO model, string value, string fieldName)
        {
            if (string.IsNullOrEmpty(value))
            {
                if (model.MetaData == null)
                {
                    model.MetaData = new LegalEntityRelationMetaData();
                }

                model.MetaData.MissingDataFields.Add(fieldName.ToCamelCase());
            }
        }

        private static void CheckMissing(LegalEntityRelationDTO model, DateTime? value, string fieldName)
        {
            if (!value.HasValue || value.Value == DateTime.MinValue)
            {
                if (model.MetaData == null)
                {
                    model.MetaData = new LegalEntityRelationMetaData();
                }

                model.MetaData.MissingDataFields.Add(fieldName.ToCamelCase());
            }
        }

        /// <summary>
        /// Gets the default OfficerTypeCode to use, based on jurisdiction, in case the OfficerTypeCode is empty.
        /// </summary>
        /// <param name="legalEntity">The legal entity to check the jurisdiction.</param>
        /// <returns>The default OfficerTypeCode and OfficerTypeName.</returns>
        private static (string code, string name) GetDefaultOfficerType(LegalEntity legalEntity)
        {
            var defaultOfficerType = legalEntity.Jurisdiction.Code switch
            {
                JurisdictionCodes.Nevis => (BODirectorOfficerTypeCode.KNTP01, "Individual"),
                JurisdictionCodes.BritishVirginIslands => (BODirectorOfficerTypeCode.VGTP01, "BOSS UBO S6(1)(a) Beneficial Owner/S6(1)(b) Controlling Person"),

                // ToDo.
                // Preparation for when Bahamas//Panama are implemented. BODirectorOfficerTypeCode does not exist yet for these juridictions.
                // JurisdictionCodes.Bahamas => BODirectorOfficerTypeCode.BSTP01,
                // JurisdictionCodes.Panama => BODirectorOfficerTypeCode.PATP01,
                _ => (string.Empty, string.Empty)
            };
            return defaultOfficerType;
        }

        private async Task<string> ToUniqueRelationIdAsync(string value)
        {
            if (Guid.TryParse(value, out var id))
            {
                var item = await _beneficialOwnersRepository.FindFirstOrDefaultByConditionAsync(x => x.Id == id);
                if (item == null)
                {
                    throw new Framework.Exceptions.BadRequestException("Invalid id for BeneficialOwner)");
                }

                return item.ExternalUniqueId;
            }
            else
            {
                return value;
            }
        }

        private void ClearBulkData()
        {
            _beneficialOwnersToInsert.Clear();
            _beneficialOwnersToUpdate.Clear();
            _beneficialOwnersToDelete.Clear();
            _beneficialOwnerHistoryToInsert.Clear();
        }

        private Framework.Messaging.Tokens.TokenList CreateTokens(BeneficialOwnerHistory relation)
        {
            var result = new Framework.Messaging.Tokens.TokenList();

            result.Add("company.name", relation.LegalEntity.Name);
            result.Add("company.code", relation.LegalEntity.Code);

            result.Add("masterclient.code", relation.LegalEntity.MasterClient.Code);

            result.Add("masterfile.label", "BO");
            result.Add("masterfile.code", relation.ExternalUniqueId);

            result.Add("requestor", relation.UpdateRequestedByUser.GetDisplayName());

            result.Add("position", "BO");
            result.Add("request", relation.UpdateRequestType.Value.GetDisplayText());

            result.Add("comment", relation.UpdateRequestComments);

            return result;
        }

        /// <summary>
        /// Create the list of tokens for the email for an UpdateRequest.
        /// </summary>
        /// <param name="legalEntity">The entity (company) add the tokens for.</param>
        /// <param name="request">The request for assistance.</param>
        /// <returns>The created tokenlist.</returns>
        private Framework.Messaging.Tokens.TokenList CreateTokens(LegalEntity legalEntity, RequestAssistanceRequest request)
        {
            var result = new Framework.Messaging.Tokens.TokenList();

            result.Add("company.name", legalEntity.Name);
            result.Add("company.code", legalEntity.Code);

            result.Add("masterclient.code", legalEntity.MasterClient.Code);

            result.Add("requestor", _workContext.User?.DisplayName);

            result.Add("request", request.AssistanceRequestType.GetDisplayText());
            result.Add("position", "BO");

            result.Add("comment", request.AssistanceRequestComments);

            return result;
        }

        /// <summary>
        /// Creates an entry in BeneficialOwnerHistory based on the importing SyncBeneficialOwner.
        /// </summary>
        /// <param name="legalEntityId">The id of the legal entity.</param>
        /// <param name="syncBeneficialOwner">The importing SyncBeneficialOwner.</param>
        /// <param name="status">The status to give to the history.</param>
        /// <param name="beneficial">The target beneficial owner.</param>
        /// <returns>The created BeneficialOwnerHistory.</returns>
        private BeneficialOwnerHistory CreateBeneficialOwnerHistory(Guid legalEntityId, SyncBeneficialOwner syncBeneficialOwner, LegalEntityRelationStatus status,
            BeneficialOwner beneficial)
        {
            var result = new BeneficialOwnerHistory
            {
                ReceivedAt = DateTime.UtcNow,

                LegalEntityId = legalEntityId,

                ExternalUniqueId = syncBeneficialOwner.UniqueRelationId,
                Status = status,

                // BO
                Name = syncBeneficialOwner.Name == null ? string.Empty : syncBeneficialOwner.Name,
                FormerName = syncBeneficialOwner.FormerName,
                Code = syncBeneficialOwner.Code,
                CompanyNumber = syncBeneficialOwner.CompanyNumber,

                Nationality = syncBeneficialOwner.Nationality,
                ServiceAddress = syncBeneficialOwner.ServiceAddress,
                TIN = syncBeneficialOwner.TIN,
                NameOfRegulator = syncBeneficialOwner.NameOfRegulator,
                StockExchangeName = syncBeneficialOwner.StockExchange,
                StockExchangeCode = syncBeneficialOwner.StockCode,
                OfficerTypeCode = syncBeneficialOwner.OfficerTypeCode,
                OfficerTypeName = syncBeneficialOwner.OfficerTypeName,
                Country = syncBeneficialOwner.Country,
                IncorporationNr = syncBeneficialOwner.BoDirIncorporationNumber,
                AppointmentDate = syncBeneficialOwner.FromDate,
                CessationDate = syncBeneficialOwner.ToDate,
                BeneficialOwnerId = beneficial.Id == Guid.Empty ? null : beneficial.Id,

                FileType = syncBeneficialOwner.FileType,
                IsIndividual = syncBeneficialOwner.FileType.Equals("individual", StringComparison.OrdinalIgnoreCase)
            };

            if (result.IsIndividual)
            {
                result.DateOfBirth = syncBeneficialOwner.DateOfBirthOrIncorp;
                result.PlaceOfBirth = syncBeneficialOwner.PlaceOfBirthOrIncorp;
                result.CountryOfBirth = syncBeneficialOwner.CountryOfBirthOrIncorp;
                result.CountryOfBirthCode = syncBeneficialOwner.CountryCodeOfBirthOrIncorp;
                result.ResidentialAddress = syncBeneficialOwner.ResidentialOrRegisteredAddress;
            }
            else
            {
                result.IncorporationDate = syncBeneficialOwner.DateOfBirthOrIncorp;

                result.IncorporationDate = syncBeneficialOwner.DateOfBirthOrIncorp.AsNullable();
                result.Address = syncBeneficialOwner.ResidentialOrRegisteredAddress;
                result.JurisdictionOfRegulator = syncBeneficialOwner.JurisdictionOfRegulationOrSovereignState;
                result.SovereignState = syncBeneficialOwner.JurisdictionOfRegulationOrSovereignState;
            }

            _beneficialOwnerHistoryToInsert.Add(result);
            return result;
        }

        /// <summary>
        /// Updates the current BeneficialOwner with the importing data.
        /// </summary>
        /// <param name="legalEntityId">The id of the legal entity.</param>
        /// <param name="currentBeneficialOwner">The current BeneficialOwner. Can be null, then it is created.</param>
        /// <param name="syncBeneficialOwner">The importing BeneficialOwner.</param>
        /// <returns>The updated or created BODirector.</returns>
        private BeneficialOwner UpsertBeneficialOwner(Guid legalEntityId, BeneficialOwner currentBeneficialOwner, SyncBeneficialOwner syncBeneficialOwner)
        {
            bool isNew = false;
            if (currentBeneficialOwner == null)
            {
                currentBeneficialOwner = new BeneficialOwner(Guid.NewGuid());
                currentBeneficialOwner.LegalEntityId = legalEntityId;

                // This identifies the BO in VP
                currentBeneficialOwner.ExternalUniqueId = syncBeneficialOwner.UniqueRelationId;

                isNew = true;
            }

            // BeneficialOwner
            currentBeneficialOwner.Name = syncBeneficialOwner.Name == null ? string.Empty : syncBeneficialOwner.Name;
            currentBeneficialOwner.FormerName = syncBeneficialOwner.FormerName;
            currentBeneficialOwner.Code = syncBeneficialOwner.Code;
            currentBeneficialOwner.CompanyNumber = syncBeneficialOwner.CompanyNumber;

            currentBeneficialOwner.Nationality = syncBeneficialOwner.Nationality;
            currentBeneficialOwner.TIN = syncBeneficialOwner.TIN;
            currentBeneficialOwner.NameOfRegulator = syncBeneficialOwner.NameOfRegulator;
            currentBeneficialOwner.StockExchangeName = syncBeneficialOwner.StockExchange;
            currentBeneficialOwner.StockExchangeCode = syncBeneficialOwner.StockCode;
            currentBeneficialOwner.OfficerTypeCode = syncBeneficialOwner.OfficerTypeCode;
            currentBeneficialOwner.OfficerTypeName = syncBeneficialOwner.OfficerTypeName;
            currentBeneficialOwner.Country = syncBeneficialOwner.Country;
            currentBeneficialOwner.IncorporationNr = syncBeneficialOwner.BoDirIncorporationNumber;

            // currentBeneficialOwner.IncorporationDate = syncBeneficialOwner.DateOfBirthOrIncorp;

            currentBeneficialOwner.AppointmentDate = syncBeneficialOwner.FromDate;
            currentBeneficialOwner.CessationDate = syncBeneficialOwner.ToDate;

            currentBeneficialOwner.ServiceAddress = syncBeneficialOwner.ServiceAddress;

            currentBeneficialOwner.FileType = syncBeneficialOwner.FileType;
            currentBeneficialOwner.IsIndividual = syncBeneficialOwner.FileType.Equals("individual", StringComparison.OrdinalIgnoreCase);

            if (currentBeneficialOwner.IsIndividual)
            {
                currentBeneficialOwner.DateOfBirth = syncBeneficialOwner.DateOfBirthOrIncorp.AsNullable();
                currentBeneficialOwner.PlaceOfBirth = syncBeneficialOwner.PlaceOfBirthOrIncorp;
                currentBeneficialOwner.CountryOfBirth = syncBeneficialOwner.CountryOfBirthOrIncorp;
                currentBeneficialOwner.CountryOfBirthCode = syncBeneficialOwner.CountryCodeOfBirthOrIncorp;
                currentBeneficialOwner.ResidentialAddress = syncBeneficialOwner.ResidentialOrRegisteredAddress;
            }
            else
            {
                currentBeneficialOwner.IncorporationDate = syncBeneficialOwner.DateOfBirthOrIncorp.AsNullable();
                currentBeneficialOwner.Address = syncBeneficialOwner.ResidentialOrRegisteredAddress;
                currentBeneficialOwner.JurisdictionOfRegulator = syncBeneficialOwner.JurisdictionOfRegulationOrSovereignState;
                currentBeneficialOwner.SovereignState = syncBeneficialOwner.JurisdictionOfRegulationOrSovereignState;
                currentBeneficialOwner.CountryOfFormation = syncBeneficialOwner.CountryOfBirthOrIncorp;
            }

            if (isNew)
            {
                _beneficialOwnersToInsert.Add(currentBeneficialOwner);

                // _beneficialOwnersRepository.Insert(currentBeneficialOwner, saveChanges: false);
            }
            else
            {
                _beneficialOwnersToUpdate.Add(currentBeneficialOwner);
            }

            return currentBeneficialOwner;
        }

        /// <summary>
        /// Gets the list of jurisdictions that we are syncing for.
        /// </summary>
        /// <returns>Returns a list of the synced jurisdiction ID's.</returns>
        private async Task<List<Guid>> GetSyncingJurisdictionIdsAsync()
        {
            var pcpCodes = SyncHelper.JurisdictionCodes.Select(c => CodeConverter.ViewPointCodeToPCPCode(c));
            return (await _jurisdictionsRepository.FindByConditionAsync(j => pcpCodes.Contains(j.Code))).Select(j => j.Id).ToList();
        }
    }
}
