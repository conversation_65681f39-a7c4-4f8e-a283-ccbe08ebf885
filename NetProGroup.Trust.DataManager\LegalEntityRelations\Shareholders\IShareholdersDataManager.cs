﻿// <copyright file="IShareholdersDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Shareholders;
using NetProGroup.Trust.DataManager.LegalEntityRelations.RequestResponses;
using NetProGroup.Trust.DataManager.LegalEntityRelations.Shareholders.RequestResponses;

namespace NetProGroup.Trust.DataManager.LegalEntityRelations
{
    /// <summary>
    /// Interface for the datamanager for Shareholders.
    /// </summary>
    public interface IShareholdersDataManager : IScopedService
    {
        /// <summary>
        /// Gets a paged list with Shareholders.
        /// </summary>
        /// <param name="request">Request with optional parameters to search for.</param>
        /// <returns>A <see cref="Task{ListCompaniesResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<ListShareholdersResponse> ListShareholdersAsync(ListShareholdersRequest request);

        /// <summary>
        /// Gets the current version of a particular BeneficialOwner using the unique relation id.
        /// </summary>
        /// <param name="uniqueRelationId">The unique identifier of the shareholder relation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the shareholder details.</returns>
        Task<ShareholderDTO> GetShareholderAsync(string uniqueRelationId);

        /// <summary>
        /// Gets the current and the prior version of a particular Shareholder using the unique relation id.
        /// </summary>
        /// <param name="uniqueRelationId">The unique identifier of the shareholder relation.</param>
        /// <returns>A <see cref="Task{DirectorComparisonDTO}"/> representing the result of the asynchronous operation.</returns>
        Task<ShareholderComparisonDTO> GetShareholderForComparisonAsync(string uniqueRelationId);

        /// <summary>
        /// Request for update of the Shareholder data.
        /// </summary>
        /// <param name="request">The request containing the shareholder update information.</param>
        /// <returns>A <see cref="Task{RequestUpdateResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<RequestUpdateResponse> RequestUpdateAsync(RequestUpdateRequest request);

        /// <summary>
        /// Request for assistance for Shareholders.
        /// </summary>
        /// <param name="request">The request containing the assistance request information.</param>
        /// <returns>A <see cref="Task{RequestAssistanceResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<RequestAssistanceResponse> RequestAssistanceAsync(RequestAssistanceRequest request);

        /// <summary>
        /// Confirmation of the Shareholder data.
        /// </summary>
        /// <param name="request">The request containing the confirmation information.</param>
        /// <returns>A <see cref="Task{ConfirmationResponse}"/> representing the result of the asynchronous operation.</returns>
        Task<ConfirmationResponse> ConfirmDataAsync(ConfirmationRequest request);

        /// <inheritdoc />
        Task<ShareholderDTO> FindShareholderAsync(string uniqueRelationId);
    }
}