﻿// <copyright file="ShareholdersDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Shareholders;
using NetProGroup.Trust.DataManager.LegalEntityRelations.Shareholders.RequestResponses;
using NetProGroup.Trust.DataManager.LegalEntityRelations.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Defines;
using X.PagedList;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Application.Contracts.Communication;
using Microsoft.EntityFrameworkCore;

namespace NetProGroup.Trust.DataManager.LegalEntityRelations
{
    /// <summary>
    /// Manager for Directors data.
    /// </summary>
    public class ShareholdersDataManager : IShareholdersDataManager
    {
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly ISystemAuditManager _systemAuditManager;

        private readonly ILegalEntitiesRepository _legalEntityRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IMasterClientsRepository _masterClientsRepository;

        private readonly IShareholdersRepository _shareholdersRepository;
        private readonly IShareholderHistoryRepository _shareholderHistoryRepository;

        private readonly ICommunicationAppService _communicationAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ShareholdersDataManager"/> class.
        /// </summary>
        /// <param name="mapper">Mapper instance.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="systemAuditManager">Instance of the SystemAuditManager.</param>
        /// <param name="repository">Instance of the repository.</param>
        /// <param name="jurisdictionsRepository">Instance of the Jurisdiction repository.</param>
        /// <param name="masterClientsRepository">Instance of the MasterClient repository.</param>
        /// <param name="shareholdersRepository">Instance of the Shareholders repository.</param>
        /// <param name="shareholderHistoryRepository">Instance of the ShareholderHistory repository.</param>
        /// <param name="communicationAppService">The Communication App Service.</param>
        public ShareholdersDataManager(IMapper mapper,
                                       IWorkContext workContext,
                                       ISystemAuditManager systemAuditManager,
                                       ILegalEntitiesRepository repository,
                                       IJurisdictionsRepository jurisdictionsRepository,
                                       IMasterClientsRepository masterClientsRepository,
                                       IShareholdersRepository shareholdersRepository,
                                       IShareholderHistoryRepository shareholderHistoryRepository,
                                       ICommunicationAppService communicationAppService)
        {
            _mapper = mapper;
            _workContext = workContext;
            _systemAuditManager = systemAuditManager;

            _legalEntityRepository = repository;
            _jurisdictionsRepository = jurisdictionsRepository;
            _masterClientsRepository = masterClientsRepository;

            _shareholdersRepository = shareholdersRepository;
            _shareholderHistoryRepository = shareholderHistoryRepository;

            _communicationAppService = communicationAppService;
        }

        /// <inheritdoc/>
        public async Task<ShareholderDTO> GetShareholderAsync(string uniqueRelationId)
        {
            uniqueRelationId = await ToUniqueRelationIdAsync(uniqueRelationId);

            var item = await _shareholdersRepository.FindFirstOrDefaultByConditionAsync(x => x.ExternalUniqueId == uniqueRelationId);

            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.SHAREHOLDER_NOT_FOUND.ToErrorCode(), $"Shareholder '{uniqueRelationId}' was not found");
            }

            var result = _mapper.Map<ShareholderDTO>(item);

            return result;
        }

        /// <inheritdoc />
        public async Task<ShareholderDTO> FindShareholderAsync(string uniqueRelationId)
        {
            uniqueRelationId = await ToUniqueRelationIdAsync(uniqueRelationId);

            var item = await _shareholdersRepository.FindFirstOrDefaultByConditionAsync(x => x.ExternalUniqueId == uniqueRelationId,
                q => q.Include(d => d.LegalEntity));

            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.SHAREHOLDER_NOT_FOUND.ToErrorCode(), $"Shareholder '{uniqueRelationId}' was not found");
            }

            var result = _mapper.Map<ShareholderDTO>(item);
            return result;
        }

        /// <inheritdoc/>
        public async Task<ListShareholdersResponse> ListShareholdersAsync(ListShareholdersRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var items = await _shareholdersRepository.FindByConditionAsync(x => x.LegalEntityId == request.LegalEntityId);

            var itemsPaged = new PagedList<Shareholder>(items, request.PageNumber, request.PageSize);

            var dtos = _mapper.Map<List<ShareholderDTO>>(itemsPaged);

            var dtosPaged = new StaticPagedList<ShareholderDTO>(dtos, request.PageNumber, request.PageSize, items.Count());

            var result = new ListShareholdersResponse { ShareholderItems = dtosPaged };
            return result;
        }

        /// <inheritdoc/>
        public async Task<ConfirmationResponse> ConfirmDataAsync(ConfirmationRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var uniqueRelationId = await ToUniqueRelationIdAsync(request.UniqueRelationId);

            var item = await _shareholderHistoryRepository.GetCurrentShareholderByUniqueRelationIdAsync(uniqueRelationId);
            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.SHAREHOLDER_NOT_FOUND.ToErrorCode(), $"Shareholder '{uniqueRelationId}' was not found");
            }

            item = Clone(item);
            item.ConfirmData(request);
            await _shareholderHistoryRepository.InsertAsync(item);

            await _systemAuditManager.AddActivityLogAsync(item, ActivityLogActivityTypes.ShareholderDataConfirmed, "Data confirmed.", $"The data for Shareholder {item.Name} is confirmed.");

            await _shareholderHistoryRepository.SaveChangesAsync();

            return new ConfirmationResponse();
        }

        /// <inheritdoc/>
        public async Task<RequestUpdateResponse> RequestUpdateAsync(RequestUpdateRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var uniqueRelationId = await ToUniqueRelationIdAsync(request.UniqueRelationId);

            var item = await _shareholderHistoryRepository.GetCurrentShareholderByUniqueRelationIdAsync(uniqueRelationId);
            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.SHAREHOLDER_NOT_FOUND.ToErrorCode(), $"Shareholder '{uniqueRelationId}' was not found");
            }

            item = Clone(item);
            item.RequestUpdate(request);

            await _systemAuditManager.AddActivityLogAsync(item, ActivityLogActivityTypes.ShareholderDataUpdateRequested, "Update requested.", $"An update is requested for Shareholder {item.Name}.");

            var tokens = CreateTokens(item);

            var productionOffice = string.Empty;

            if (item.LegalEntity != null)
            {
                productionOffice = item.LegalEntity.ProductionOffice == null ? string.Empty : item.LegalEntity.ProductionOffice;
            }

            // Send an email
            await _communicationAppService.SendRequestForUpdateAsync(productionOffice, tokens);

            await _shareholdersRepository.SaveChangesAsync();

            return new RequestUpdateResponse();
        }

        /// <inheritdoc/>
        public async Task<RequestAssistanceResponse> RequestAssistanceAsync(RequestAssistanceRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var legalEntity = await _legalEntityRepository.GetByIdAsync(request.LegalEntityId, q => q.Include(le => le.MasterClient));
            if (legalEntity == null)
            {
                throw new NetProGroup.Framework.Exceptions.NotFoundException(ApplicationErrors.COMPANY_NOT_FOUND.ToErrorCode(), "Company not found");
            }

            await _systemAuditManager.AddActivityLogAsync(legalEntity, ActivityLogActivityTypes.ShareholderAssistanceRequested, "Assistance requested.", $"Assistance is requested for Shareholders of '{legalEntity.Name}'.");

            // Send an email
            var tokens = CreateTokens(legalEntity, request);
            await _communicationAppService.SendRequestForAssistanceAsync("", tokens);

            await _legalEntityRepository.SaveChangesAsync();

            return new RequestAssistanceResponse();
        }

        /// <inheritdoc/>
        public async Task<ShareholderComparisonDTO> GetShareholderForComparisonAsync(string uniqueRelationId)
        {
            var result = new ShareholderComparisonDTO();

            uniqueRelationId = await ToUniqueRelationIdAsync(uniqueRelationId);

            var item = await _shareholderHistoryRepository.GetCurrentShareholderByUniqueRelationIdAsync(uniqueRelationId);

            if (item == null)
            {
                throw new NotFoundException(ApplicationErrors.SHAREHOLDER_NOT_FOUND.ToErrorCode(), $"Shareholder '{uniqueRelationId}' was not found");
            }

            result.CurrentVersion = _mapper.Map<ShareholderDTO>(item);

            if (item.Status == LegalEntityRelationStatus.Refreshed || item.Status == LegalEntityRelationStatus.UpdateReceived)
            {
                // And get the last with one of the following states
                var statuses = new LegalEntityRelationStatus[] { LegalEntityRelationStatus.Confirmed, LegalEntityRelationStatus.PendingUpdateRequest, LegalEntityRelationStatus.Initial };

                item = await _shareholderHistoryRepository.GetLastShareholderByUniqueRelationIdAndStatusAsync(uniqueRelationId, statuses);
                result.PriorVersion = _mapper.Map<ShareholderDTO>(item);
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task SyncShareholders(SyncShareholderRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            Check.NotDefaultOrNull<Guid>(request.LegalEntityId, nameof(request.LegalEntityId));

            // Get the current Shareholders for the LegalEntity.
            var currentShareholders = await _shareholdersRepository.FindByConditionAsync(x => x.LegalEntityId == request.LegalEntityId);

            // Get the Shareholders history for the LegalEntity.
            var historyShareholders = await _shareholderHistoryRepository.ListCurrentShareholdersByLegalEntityIdAsync(request.LegalEntityId);

            // Remember all keys in the current Shareholders so we can remove those that are not in the import.
            var currentKeys = currentShareholders.Select(x => x.ExternalUniqueId.ToLower()).ToList();

            // Get each Shareholder from the request and compare to the current if any.
            // If ExternalUniqueId not in current then add one now and add a copy to history with status 'INITIAL'.
            // If exists in current but changed then add a record to history with the status from the last history ('REFRESHED' or 'VP DATA RECEIVED', if 'INITIAL' then 'REFRESHED').

            foreach (var importShareholder in request.Shareholders)
            {
                currentKeys.Remove(importShareholder.UniqueRelationId);

                // Get current and last history
                var currentShareholder = currentShareholders.Where(x => x.ExternalUniqueId.Equals(importShareholder.UniqueRelationId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                var lastShareholderHistory = historyShareholders.Where(x => x.ExternalUniqueId.Equals(importShareholder.UniqueRelationId, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();

                if (currentShareholder == null)
                {
                    // Initial creation of Shareholder and ShareholderHistory
                    currentShareholder = UpsertShareholder(null, importShareholder);
                    CreateShareholderHistory(importShareholder, LegalEntityRelationStatus.Initial);
                }
                else
                {
                    // Should not happen but just in case...
                    if (lastShareholderHistory == null)
                    {
                        lastShareholderHistory = CreateShareholderHistory(importShareholder, LegalEntityRelationStatus.Initial);
                    }

                    // If last history has status 'INITIAL', 'REFRESHED' or 'VP DATA RECEIVED' and any change then create new history and update the BOShareholder.
                    switch (lastShareholderHistory.Status)
                    {
                        case LegalEntityRelationStatus.Initial:
                        case LegalEntityRelationStatus.Refreshed:
                        case LegalEntityRelationStatus.UpdateReceived:
                            {
                                if (RelationChanged(lastShareholderHistory, importShareholder) ||
                                    MetaDataChanged(lastShareholderHistory, importShareholder))
                                {
                                    UpsertShareholder(currentShareholder, importShareholder);

                                    var status = lastShareholderHistory.Status;
                                    if (status == LegalEntityRelationStatus.Initial)
                                    {
                                        status = LegalEntityRelationStatus.Refreshed;
                                    }

                                    CreateShareholderHistory(importShareholder, status);
                                }

                                break;
                            }

                        case LegalEntityRelationStatus.PendingUpdateRequest:
                        case LegalEntityRelationStatus.Confirmed:
                            {
                                if (RelationChanged(lastShareholderHistory, importShareholder))
                                {
                                    // Shareholder data changed. Create a new history and set status to 'VP DATA RECEIVED'
                                    UpsertShareholder(currentShareholder, importShareholder);
                                    CreateShareholderHistory(importShareholder, LegalEntityRelationStatus.UpdateReceived);
                                }
                                else if (MetaDataChanged(lastShareholderHistory, importShareholder))
                                {
                                    // Only 'metadata' changed. Create a new history but keep the status
                                    UpsertShareholder(currentShareholder, importShareholder);
                                    CreateShareholderHistory(importShareholder, lastShareholderHistory.Status);
                                }

                                break;
                            }
                    }
                }
            }

            // Now we can delete all Shareholders no longer mentioned in the sync
            var toRemove = await _shareholdersRepository.FindByConditionAsync(x => currentKeys.Contains(x.ExternalUniqueId));
            await _shareholdersRepository.DeleteAsync(toRemove, saveChanges: false);

            // Now save the changes
            await _shareholdersRepository.SaveChangesAsync();
        }

        private static ShareholderHistory Clone(ShareholderHistory source)
        {
            ArgumentNullException.ThrowIfNull(source, nameof(source));

            var result = new ShareholderHistory(Guid.NewGuid());
            source.CopyProperties(result);

            result.CreatedAt = DateTime.UtcNow;
            result.UpdatedAt = DateTime.UtcNow;

            return result;
        }

        /// <summary>
        /// Returns true if any of the Shareholder fields does not match.
        /// </summary>
        /// <param name="current">The current ShareholderHistory.</param>
        /// <param name="syncShareholder">The importing SyncShareholder.</param>
        /// <returns>True if at least 1 ShareholderHistory field changed.</returns>
        private static bool RelationChanged(ShareholderHistory current, SyncShareholder syncShareholder)
        {
            /*if (CompareHelper.ValueChanged(current.Name, importBODirector.Name) ||
                CompareHelper.ValueChanged(current.FormerName, importBODirector.FormerName) ||
                CompareHelper.ValueChanged(current.FileType, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.DirectorIsAlternateToId, importBODirector.DirectorIsAlternateToId) ||
                CompareHelper.ValueChanged(current.DirectorIsAlternateToName, importBODirector.DirectorIsAlternateToName) ||
                CompareHelper.ValueChanged(current.ServiceAddress, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.ResidentialOrRegisteredAddress, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.OfficerTypeCode, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.FromDate, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.DateOfBirthOrIncorp, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.PlaceOfBirthOrIncorp, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.Nationality, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.Country, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.TIN, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.NameOfRegulator, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.StockExchange, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.StockCode, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.JurisdictionOfRegulationOrSovereignState, importBODirector.FileType) ||
                CompareHelper.ValueChanged(current.BoDirIncorporationNumber, importBODirector.FileType))
            {
                return true;
            }*/

            return false;
        }

        /// <summary>
        /// Returns true if any of the MetaData fields does not match.
        /// </summary>
        /// <param name="current">The current Shareholder.</param>
        /// <param name="syncShareholder">The importing SyncShareholder.</param>
        /// <returns>True if at least 1 MetData field changed.</returns>
        private static bool MetaDataChanged(ShareholderHistory current, SyncShareholder syncShareholder)
        {
            /*if (CompareHelper.ValueChanged(current.ma, importBODirector.MasterClientCode) ||
                CompareHelper.ValueChanged(current.CompanyNumber, importBODirector.CompanyNumber) ||
                CompareHelper.ValueChanged(current.EntityCode, importBODirector.EntityCode) ||
                CompareHelper.ValueChanged(current.EntityName, importBODirector.EntityName) ||
                CompareHelper.ValueChanged(current.Code, importBODirector.Code))
            {
                return true;
            }*/

            return false;
        }

        private async Task<string> ToUniqueRelationIdAsync(string value)
        {
            if (Guid.TryParse(value, out var id))
            {
                var item = await _shareholdersRepository.FindFirstOrDefaultByConditionAsync(x => x.Id == id);
                if (item == null)
                {
                    throw new Framework.Exceptions.BadRequestException("Invalid id for Shareholder)");
                }

                return item.ExternalUniqueId;
            }
            else
            {
                return value;
            }
        }

        /// <summary>
        /// Create the list of tokens for the email for an UpdateRequest and get info from the history entry.
        /// </summary>
        /// <param name="relation">The relation to add the tokens for.</param>
        /// <returns>The created tokenlist.</returns>
        private Framework.Messaging.Tokens.TokenList CreateTokens(ShareholderHistory relation)
        {
            var result = new Framework.Messaging.Tokens.TokenList();

            result.Add("company.name", relation.LegalEntity.Name);
            result.Add("company.code", relation.LegalEntity.Code);

            result.Add("masterclient.code", relation.LegalEntity.MasterClient.Code);

            result.Add("masterfile.label", "Member");
            result.Add("masterfile.code", relation.ExternalUniqueId);

            result.Add("requestor", relation.UpdateRequestedByUser.GetDisplayName());

            result.Add("position", "Member");
            result.Add("request", relation.UpdateRequestType.Value.GetDisplayText());

            result.Add("comment", relation.UpdateRequestComments);

            return result;
        }

        /// <summary>
        /// Create the list of tokens for the email for an UpdateRequest.
        /// </summary>
        /// <param name="legalEntity">The entity (company) add the tokens for.</param>
        /// <param name="request">The request for assistance.</param>
        /// <returns>The created tokenlist.</returns>
        private Framework.Messaging.Tokens.TokenList CreateTokens(LegalEntity legalEntity, RequestAssistanceRequest request)
        {
            var result = new Framework.Messaging.Tokens.TokenList();

            result.Add("company.name", legalEntity.Name);
            result.Add("company.code", legalEntity.Code);

            result.Add("masterclient.code", legalEntity.MasterClient.Code);

            result.Add("requestor", _workContext.User?.DisplayName);

            result.Add("request", request.AssistanceRequestType.GetDisplayText());
            result.Add("position", "Member");

            result.Add("comment", request.AssistanceRequestComments);

            return result;
        }

        /// <summary>
        /// Creates an entry in ShareholderHistory based on the importing SyncShareholder.
        /// </summary>
        /// <param name="syncShareholder">The importing SyncShareholder.</param>
        /// <param name="status">The status to give to the history.</param>
        /// <returns>The created ShareholderHistory.</returns>
        private ShareholderHistory CreateShareholderHistory(SyncShareholder syncShareholder, LegalEntityRelationStatus status)
        {
            var result = new ShareholderHistory
            {
                ReceivedAt = DateTime.UtcNow,

                Status = status,

                /* Shareholder
                Name = syncShareholder.Name,
                Nationality = syncShareholder.Nationality,
                TIN = syncShareholder.TIN,
                NameOfRegulator = syncShareholder.NameOfRegulator,
                StockExchange = syncShareholder.StockExchange,
                StockCode = syncShareholder.StockCode,

                // MetaData
                MasterClientCode = importShareholder.MasterClientCode,
                CompanyNumber = importShareholder.CompanyNumber,
                EntityCode = importShareholder.EntityCode,
                EntityName = importShareholder.EntityName,
                Code = importShareholder.Code,
                */
            };

            _shareholderHistoryRepository.Insert(result, saveChanges: false);
            return result;
        }

        /// <summary>
        /// Updates the current Shareholder with the importing data.
        /// </summary>
        /// <param name="currentShareholder">The current Shareholder. Can be null, then it is created.</param>
        /// <param name="syncShareholder">The importing Shareholder.</param>
        /// <returns>The updated or created BODirector.</returns>
        private Shareholder UpsertShareholder(Shareholder currentShareholder, SyncShareholder syncShareholder)
        {
            bool isNew = false;
            if (currentShareholder == null)
            {
                currentShareholder = new Shareholder();
                isNew = true;
            }

            // Shareholder

            // currentShareholder.Name = syncShareholder.Name;

            // Add fields here...
            if (isNew)
            {
                _shareholdersRepository.Insert(currentShareholder, saveChanges: false);
            }

            return currentShareholder;
        }
    }
}
