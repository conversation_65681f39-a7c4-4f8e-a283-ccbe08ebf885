﻿// <copyright file="ModulesDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Modules.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.DataManager.Modules
{
    /// <summary>
    /// Manager for modules data.
    /// </summary>
    public class ModulesDataManager : IModulesDataManager
    {
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;

        private readonly IModulesRepository _modulesRepository;
        private readonly IJurisdictionModulesRepository _jurisdictionModulesRepository;
        private readonly ILegalEntityModulesRepository _legalEntityModulesRepository;

        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly ISubmissionsRepository _submissionsRepository;
        private readonly ILogger<ModulesDataManager> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ModulesDataManager"/> class.
        /// </summary>
        /// <param name="mapper">Mapper instance.</param>
        /// <param name="workContext">Instance of the current WorkContext.</param>
        /// <param name="modulesRepository">Instance of the Modules repository.</param>
        /// <param name="jurisdictionModulesRepository">Instance of the JurisdictionModules repository.</param>
        /// <param name="legalEntityModulesRepository">Instance of the LegalEntityModules repository.</param>
        /// <param name="jurisdictionRepository">Instance of the Jurisdictions repository.</param>
        /// <param name="legalEntitiesRepository">Instance of the LegalEntities repository.</param>
        /// <param name="systemAuditManager">Instance of the SystemAuditManager.</param>
        /// <param name="submissionsRepository">Instance of the Submissions repository.</param>
        /// <param name="logger">Instance of the logger.</param>
        public ModulesDataManager(IMapper mapper, IWorkContext workContext,
            IModulesRepository modulesRepository,
            IJurisdictionModulesRepository jurisdictionModulesRepository,
            ILegalEntityModulesRepository legalEntityModulesRepository,
            IJurisdictionsRepository jurisdictionRepository,
            ILegalEntitiesRepository legalEntitiesRepository,
            ISystemAuditManager systemAuditManager,
            ISubmissionsRepository submissionsRepository,
            ILogger<ModulesDataManager> logger)
        {
            _mapper = mapper;
            _workContext = workContext;

            _modulesRepository = modulesRepository;
            _legalEntityModulesRepository = legalEntityModulesRepository;
            _jurisdictionModulesRepository = jurisdictionModulesRepository;
            _jurisdictionsRepository = jurisdictionRepository;
            _legalEntitiesRepository = legalEntitiesRepository;
            _systemAuditManager = systemAuditManager;
            _submissionsRepository = submissionsRepository;
            _logger = logger;
        }

        /// <inheritdoc/>
        public async Task<ModuleDTO> GetModuleByIdAsync(Guid moduleId)
        {
            Check.NotDefaultOrNull<Guid>(moduleId, nameof(moduleId));

            var module = await _modulesRepository.CheckModuleByIdAsync(moduleId);

            var result = _mapper.Map<ModuleDTO>(module);

            return result;
        }

        /// <inheritdoc />
        public async Task<ModuleDTO> GetModuleByKeyAsync(string key)
        {
            Check.NotNull(key, nameof(key));

            var module = (await _modulesRepository.FindByConditionAsync(x => x.Key == key)).SingleOrDefault();

            var result = _mapper.Map<ModuleDTO>(module);

            return result;
        }

        /// <inheritdoc />
        public async Task<ModuleDTO> GetModuleBySubmissionIdAsync(Guid submissionId)
        {
            var module = await _submissionsRepository.GetQueryable().Where(s => s.Id == submissionId).Select(submission => submission.Module).SingleOrDefaultAsync();

            var result = _mapper.Map<ModuleDTO>(module);

            return result;
        }

        /// <inheritdoc/>
        public async Task<ListModulesResponse> GetModulesAsync(ListModulesRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            if (request.JurisdictionId.HasValue && request.CompanyId.HasValue)
            {
                throw new ArgumentException("Either JurisdictionId or CompanyId should be set, not both.", nameof(request));
            }

            if (request.JurisdictionId.HasValue)
            {
                Check.NotDefaultOrNull<Guid>(request.JurisdictionId.Value, nameof(request.JurisdictionId));
                await _jurisdictionsRepository.CheckJurisdictionByIdAsync(request.JurisdictionId.Value);
            }

            if (request.CompanyId.HasValue)
            {
                Check.NotDefaultOrNull<Guid>(request.CompanyId.Value, nameof(request.CompanyId));
                await _legalEntitiesRepository.CheckLegalEntityByIdAsync(request.CompanyId.Value);
            }

            var result = new ListModulesResponse();

            List<Module> assignedModules = null;

            Guid? companyJurisdictionId = null;

            _logger.LogTrace("Retrieving all modules for jurisdiction '{JurisdictionId}' and company '{CompanyId}'", request.JurisdictionId, request.CompanyId);
            var allModules = await _modulesRepository.FindAllAsync(q => q.Include(m => m.JurisdictionModules));

            if (request.JurisdictionId.HasValue)
            {
                if (!request.ForClientUI)
                {
                    request.IsActive = null;
                }

                _logger.LogTrace("Retrieving modules for jurisdiction {JurisdictionId}", request.JurisdictionId.Value);
                assignedModules = (await _modulesRepository.FindByConditionAsync(
                    x => x.JurisdictionModules.Any(x => x.JurisdictionId == request.JurisdictionId),
                    q => q.Include(m => m.JurisdictionModules))).ToList();
            }
            else if (request.CompanyId.HasValue)
            {
                if (!request.ForClientUI)
                {
                    request.IsActive = null;
                }

                _logger.LogTrace("Retrieving modules for company {CompanyId}", request.CompanyId.Value);
                var legalEntityModules = await _legalEntityModulesRepository.FindByConditionAsync(
                    le => le.LegalEntityId == request.CompanyId,
                    q => q.Include(m => m.Module));

                assignedModules = legalEntityModules.Select(module => module.Module).ToList();

                // Get the jurisdiction for the company and filter the complete list of modules for only that jurisdiction so we only return modules that are applicable
                _logger.LogTrace("Retrieving company {CompanyId} with jurisdiction", request.CompanyId.Value);
                var company = await _legalEntitiesRepository.GetByIdAsync(request.CompanyId.Value,
                                                                          q => q.Include(c => c.MasterClient)
                                                                                .Include(c => c.Jurisdiction));
                _logger.LogTrace("Retrieved company {CompanyId} with jurisdiction", request.CompanyId.Value);

                if (company != null)
                {
                    companyJurisdictionId = company.Jurisdiction?.Id;

                    if (companyJurisdictionId.HasValue)
                    {
                        // Filter the available modules to the ones for the jurisdiction of the company
                        allModules = allModules.Where(m => m.JurisdictionModules.Any(jm => jm.JurisdictionId == companyJurisdictionId));
                    }
                }
            }
            else
            {
                assignedModules = new List<Module>();
            }

            // Filter on IsActive?
            if (request.IsActive.HasValue)
            {
                allModules = allModules.Where(x => x.IsActive == request.IsActive.Value);
            }

            // Enumerate all modules and create DTOs
            foreach (var module in allModules)
            {
                // Get the module from the assigned list.
                var assignedModule = assignedModules.FirstOrDefault(x => x.Id == module.Id);

                if (request.JurisdictionId.HasValue)
                {
                    // Prepare the DTO
                    var dto = new ModuleDTO
                    {
                        Id = module.Id,
                        Key = module.Key,
                        Name = module.Name,
                        IsActive = module.IsActive,
                    };

                    if (assignedModule != null)
                    {
                        // Is this module assigned to the requested jurisdiction?
                        var jurisdictionModule = assignedModule.JurisdictionModules.SingleOrDefault(x => x.JurisdictionId == request.JurisdictionId.Value);

                        // If no explicit setting for the jurisdiction then assume 'disabled'
                        dto.IsEnabled = jurisdictionModule == null ? false : jurisdictionModule.IsEnabled.GetValueOrDefault();
                    }
                    else
                    {
                        dto.IsEnabled = false;
                    }

                    result.ModuleItems.Add(dto);
                }
                else if (request.CompanyId.HasValue)
                {
                    // Prepare the DTO
                    var dto = new CompanyModuleDTO
                    {
                        Id = module.Id,
                        Key = module.Key,
                        Name = module.Name,
                        IsActive = module.IsActive,
                    };

                    if (assignedModule != null)
                    {
                        var companyModule = assignedModule.LegalEntityModules.Single(x => x.LegalEntityId == request.CompanyId.Value);

                        // Is this module assigned to the requested company?
                        dto.IsEnabled = companyModule.IsEnabled.GetValueOrDefault();
                        dto.IsApproved = companyModule.IsApproved;
                    }
                    else
                    {
                        // The module is not explicitly added to the company so assume 'disabled' and 'disapproved'
                        dto.IsEnabled = false;
                        dto.IsApproved = false;
                    }

                    // Get the module for the jurisdiction and set the Enabled for the jurisdiction.
                    var moduleForJurisdiction = module.JurisdictionModules.SingleOrDefault(x => x.JurisdictionId == companyJurisdictionId);
                    if (moduleForJurisdiction != null)
                    {
                        dto.JurisdictionIsEnabled = moduleForJurisdiction.IsEnabled.GetValueOrDefault();
                    }

                    result.CompanyModuleItems.Add(dto);
                }
                else
                {
                    // Prepare the DTO
                    var dto = new ModuleDTO
                    {
                        Id = module.Id,
                        Key = module.Key,
                        Name = module.Name,
                        IsActive = module.IsActive,
                    };
                    result.ModuleItems.Add(dto);
                }
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task<SetModulesResponse> SetModulesAsync(SetModulesRequest request, bool removeModulesNotInList = false)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            if (request.JurisdictionId.HasValue)
            {
                var jurisdictionId = request.JurisdictionId.Value;

                // Get the modules that we already have a relation with the jurisdiction for
                var existingModules = await _jurisdictionModulesRepository.FindByConditionAsync(x => x.JurisdictionId == jurisdictionId);

                foreach (var module in request.ModuleItems)
                {
                    var existingModule = existingModules.FirstOrDefault(x => x.ModuleId == module.Id);
                    if (existingModule != null)
                    {
                        if (module.IsEnabled.HasValue)
                        {
                            existingModule.IsEnabled = module.IsEnabled.Value;
                        }
                    }
                    else
                    {
                        if (module.IsEnabled.GetValueOrDefault())
                        {
                            await _modulesRepository.CheckModuleByIdAsync(module.Id, throwNotFound: false);

                            // Create the relation
                            await _jurisdictionModulesRepository.InsertAsync(new JurisdictionModule(jurisdictionId, module.Id) { IsEnabled = true }, saveChanges: false);
                        }
                    }
                }

                if (removeModulesNotInList)
                {
                    // Remove the JurisdictionModules that are not in the request
                    var requestModuleIds = request.ModuleItems.Select(x => x.Id);
                    var toRemove = existingModules.Where(x => !requestModuleIds.Contains(x.ModuleId));
                    await _jurisdictionModulesRepository.DeleteAsync(toRemove, saveChanges: false);
                }

                await _jurisdictionModulesRepository.SaveChangesAsync();
            }
            else if (request.CompanyId.HasValue)
            {
                var companyId = request.CompanyId.Value;
                var company = await _legalEntitiesRepository.CheckLegalEntityByIdAsync(companyId, options: q => q.Include(c => c.MasterClient));

                // Get the modules that we already have a relation with the legalentity for
                var existingModules = await _legalEntityModulesRepository.FindByConditionAsync(x => x.LegalEntityId == companyId);

                foreach (var module in request.CompanyModuleItems)
                {
                    var moduleEntity = await _modulesRepository.CheckModuleByIdAsync(module.Id);

                    // Only validate if one of the flags are set
                    if (module.IsEnabled || module.IsApproved)
                    {
                        ValidateIfApprovalAllowed(company, moduleEntity);
                    }

                    var dbModule = existingModules.FirstOrDefault(x => x.ModuleId == module.Id);
                    if (dbModule != null)
                    {
                        if (dbModule.IsEnabled != module.IsEnabled)
                        {
                            await _systemAuditManager.AddActivityLogAsync(
                                company,
                                ActivityLogActivityTypes.CompanyModuleEnabledChanged,
                                $"Module {moduleEntity.Key} {(module.IsEnabled ? "enabled" : "disabled")}",
                                $"Module {moduleEntity.Key} {(module.IsEnabled ? "enabled" : "disabled")} for legal entity {company.Code}.",
                                saveChanges: false);
                            dbModule.IsEnabled = module.IsEnabled;
                        }
                    }
                    else
                    {
                        await _modulesRepository.CheckModuleByIdAsync(module.Id, throwNotFound: false);

                        // Create the relation
                        dbModule = new LegalEntityModule(companyId, module.Id) { IsEnabled = module.IsEnabled };

                        await _systemAuditManager.AddActivityLogAsync(
                            company,
                            ActivityLogActivityTypes.CompanyModuleEnabledChanged,
                            $"Module added '{(module.IsEnabled ? "enabled" : "disabled")}'",
                            $"Module {moduleEntity.Key} with status '{(module.IsEnabled ? "enabled" : "disabled")}' added for legal entity {company.Code}.",
                            saveChanges: false);

                        await _legalEntityModulesRepository.InsertAsync(dbModule, saveChanges: false);
                    }

                    if (module.IsApproved != dbModule.IsApproved)
                    {
                        await _systemAuditManager.AddActivityLogAsync(
                            company,
                            ActivityLogActivityTypes.CompanyModuleApprovalChanged,
                            $"Module {(module.IsApproved ? "approved" : "disapproved")}",
                            $"Module {moduleEntity.Key} {(module.IsApproved ? "approved" : "disapproved")} for legal entity {company.Code}.",
                            saveChanges: false);

                        dbModule.IsApproved = module.IsApproved;
                        dbModule.ApprovedByUserId = module.IsApproved ? _workContext.IdentityUserId : null;
                    }
                }

                if (removeModulesNotInList)
                {
                    // Remove the LegalEntityModules that are not in the request
                    var requestModuleIds = request.CompanyModuleItems.Select(x => x.Id);
                    var toRemove = existingModules.Where(x => !requestModuleIds.Contains(x.ModuleId));
                    await _legalEntityModulesRepository.DeleteAsync(toRemove, saveChanges: false);
                }

                await _legalEntityModulesRepository.SaveChangesAsync();
            }

            // Read the modules ad if the list was requested
            var listResponse = await GetModulesAsync(new ListModulesRequest { JurisdictionId = request.JurisdictionId, CompanyId = request.CompanyId });

            return new SetModulesResponse
            {
                CompanyModuleItems = listResponse.CompanyModuleItems,
                ModuleItems = listResponse.ModuleItems
            };
        }

        /// <inheritdoc/>
        public async Task DeactivateModulesForInactiveLegalEntityAsync(LegalEntity legalEntity, List<ActivityLog> activityLogs, bool saveChanges = false)
        {
            // Do not disable modules for now
            // See comment of Marcel Slats on 2025-jan-23 in UserStory 14904
            // https://dev.azure.com/netprogroup/Trident%20Trust%20-%20Private%20Client%20Portal/_workitems/edit/14904
            return;

            ArgumentNullException.ThrowIfNull(legalEntity, nameof(legalEntity));

            var moduleKeysForDeactivation = new string[] { ModuleKeyConsts.BODirectors };

            var legalEntityModules = await _legalEntityModulesRepository.FindByConditionAsync(lem => lem.LegalEntityId == legalEntity.Id && moduleKeysForDeactivation.Contains(lem.Module.Key));

            foreach (var item in legalEntityModules)
            {
                if (item.IsEnabled.GetValueOrDefault(false))
                {
                    item.IsEnabled = false;
                    await _legalEntityModulesRepository.UpdateAsync(item, saveChanges: saveChanges);

                    if (activityLogs != null)
                    {
                        activityLogs.Add(await _systemAuditManager.CreateModuleDeactivatedActivityLogAsync(legalEntity, item.Module));
                    }
                }
            }
        }

        private static void ValidateIfApprovalAllowed(LegalEntity company, Module module)
        {
            // Allow SimplifiedTaxReturn only when the company type is IBC or LLC
            if (module.Key.Equals(ModuleKeyConsts.SimplifiedTaxReturn, StringComparison.OrdinalIgnoreCase))
            {
                string[] allowedTypes = { LegalEntityTypes.IBC, LegalEntityTypes.LLC };
                var entityTypeName = company.EntityTypeName == null ? string.Empty : company.EntityTypeName.ToUpper();

                if (!allowedTypes.Contains(entityTypeName))
                {
                    // Not allowed for this company
                    throw new BadRequestException(ApplicationErrors.MODULE_NOT_ALLOWED.ToErrorCode(), $"Module 'STR' not allowed for company of type '{entityTypeName}'");
                }
            }
        }
    }
}
