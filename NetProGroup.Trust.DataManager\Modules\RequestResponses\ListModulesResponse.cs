// <copyright file="ListModulesResponse.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Modules;

namespace NetProGroup.Trust.DataManager.Modules.RequestResponses
{
    /// <summary>
    /// Response model for getting module data.
    /// </summary>
    public class ListModulesResponse
    {
        /// <summary>
        /// Gets or sets the list with ModuleDTO items.
        /// </summary>
        public List<ModuleDTO> ModuleItems { get; set; } = new List<ModuleDTO>();

        public List<CompanyModuleDTO> CompanyModuleItems { get; set; } = new List<CompanyModuleDTO>();
    }
}
