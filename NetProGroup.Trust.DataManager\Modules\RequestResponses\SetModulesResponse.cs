﻿// <copyright file="SetModulesResponse.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Modules;

namespace NetProGroup.Trust.DataManager.Modules.RequestResponses
{
    /// <summary>
    /// Response model for setting module data.
    /// </summary>
    public class SetModulesResponse
    {
        /// <summary>
        /// Gets or sets the list with ModuleDTO items after the set operation.
        /// </summary>
        public List<ModuleDTO> ModuleItems { get; set; } = new List<ModuleDTO>();

        /// <summary>
        /// Gets or sets the list with CompanyModuleDTO items after the set operation.
        /// </summary>
        public List<CompanyModuleDTO> CompanyModuleItems { get; set; } = new List<CompanyModuleDTO>();
    }
}
