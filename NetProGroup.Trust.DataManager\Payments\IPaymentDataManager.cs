// <copyright file="IPaymentDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Payments;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Application.Contracts.Payments.Response;
using NetProGroup.Trust.DataManager.Payments.RequestResponses;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Payments.Invoices;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Payments
{
    /// <summary>
    /// Defines data management operations for handling payment-related functionalities,
    /// specifically for managing invoices.
    /// </summary>
    public interface IPaymentDataManager : IScopedService
    {
        /// <summary>
        /// Retrieves a paginated list of payments based on the specified request criteria.
        /// </summary>
        /// <param name="request">The request object containing filter and pagination criteria for Payments.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a paginated list of <see cref="PaymentDTO"/> objects.</returns>
        Task<IPagedList<PaymentDTO>> ListPaymentsAsync(PaymentsRequest request);

        /// <summary>
        /// Retrieves the details of a specific payment by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the payment.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the details of the specified <see cref="PaymentDTO"/>.</returns>
        Task<PaymentDetailsResponseDTO> GetPaymentAsync(Guid id);

        /// <summary>
        /// Creates a payment based on the specified request criteria.
        /// </summary>
        /// <param name="request">The request object containing the details required to create a payment.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a <see cref="CreatePaymentRequestDTO"/> object representing the created payment.
        /// </returns>
        Task<CreatePaymentResponseDTO> CreatePaymentsAsync(CreatePaymentRequestDTO request);

        /// <summary>
        /// Creates or updates a manual payment for the specified invoice and legal entity.
        /// </summary>
        /// <param name="invoice">The invoice for which the payment is being created or updated.</param>
        /// <param name="legalEntity">The legal entity associated with the payment.</param>
        /// <param name="saveChanges">True to save changes to the database, false to only update the entity.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task CreateOrUpdateManualPaymentAsync(Invoice invoice, LegalEntity legalEntity, bool saveChanges);

        /// <summary>
        /// Cancels a payment by resetting the expiration date. This enables creating a new payment for the invoices.
        /// </summary>
        /// <param name="paymentId">Id of the payment to cancel.</param>
        /// <param name="saveChanges">True to save changes to the database, false to only update the entity.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task CancelPaymentAsync(Guid paymentId, bool saveChanges = false);

        /// <summary>
        /// Handles the completion of a payment after the transaction was completed successfully.
        /// </summary>
        /// <param name="transactionId">Id of the completed transaction.</param>
        /// <param name="saveChanges">Indicates whether to save changes to the database immediately.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task HandleCompletedPaymentByTransactionIdAsync(Guid transactionId, bool saveChanges = false);
    }
}