// <copyright file="IInvoiceDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.Application.Contracts.Payments.Invoices;
using NetProGroup.Trust.DataManager.Payments.Invoices.RequestResponses;
using NetProGroup.Trust.Domain.Payments.Invoices;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Payments.Invoices
{
    /// <summary>
    /// Interface for the invoice datamanager.
    /// </summary>
    public interface IInvoiceDataManager : IScopedService
    {
        /// <summary>
        /// Lists invoices.
        /// </summary>
        /// <param name="request">The request DTO holding the parameters for the search.</param>
        /// <returns>A <see cref="Task{IPagedList}"/> representing the asynchronous operation.</returns>
        Task<IPagedList<InvoiceDTO>> ListInvoicesAsync(InvoiceListRequestDTO request);

        /// <summary>
        /// Gets a specific invoice.
        /// </summary>
        /// <param name="id">The id of the invoice to get.</param>
        /// <returns>A <see cref="Task{InvoiceDTO}"/> representing the asynchronous operation.</returns>
        Task<InvoiceDTO> GetInvoiceAsync(Guid id);

        /// <summary>
        /// Deletes the given invoice.
        /// </summary>
        /// <param name="id">The id of the invoice to delete.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task DeleteInvoiceAsync(Guid id);

        /// <summary>
        /// Generates a new invoicenumber for the jurisdiction and optional module.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to generate an invoicenumber for.</param>
        /// <param name="moduleId">Optional id of the module to generate the invoicenumber for.</param>
        /// <returns>The generated invoicenumber.</returns>
        Task<string> GenerateInvoiceNumber(Guid jurisdictionId, Guid? moduleId);

        /// <summary>
        /// Creates a new invoice using the data from the request.
        /// </summary>
        /// <param name="request">The request holding the parameters for creating the invoice.</param>
        /// <returns>A <see cref="Task{Invoice}"/> representing the asynchronous operation.</returns>
        Task<Invoice> CreateInvoiceAsync(CreateInvoiceRequest request);

        /// <summary>
        /// Marks the invoice as paid/unpaid, creating a Payment if needed.
        /// </summary>
        /// <param name="invoice">The Invoice.</param>
        /// <param name="isPaid">True for paid, false for unpaid.</param>
        /// <param name="saveChanges">True to save changes to the database, false to only update the entity.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task MarkInvoiceAsPaidAsync(Invoice invoice, bool isPaid, bool saveChanges = false);

        /// <summary>
        /// Gets the module key for the invoice.
        /// </summary>
        /// <param name="id">The id of the invoice to get the module key for.</param>
        /// <returns>A <see cref="Task{ModuleDTO}"/> representing the asynchronous operation.</returns>
        Task<List<ModuleDTO>> GetModulesForInvoiceIdAsync(Guid id);

        /// <summary>
        /// Gets the master client for the invoice.
        /// </summary>
        /// <param name="id">The id of the invoice to get the master client for.</param>
        /// <returns>A <see cref="Task{MasterClientDTO}"/> representing the asynchronous operation.</returns>
        Task<MasterClientDTO> GetMasterClientByInvoiceId(Guid id);

        /// <summary>
        /// Checks the invoice.
        /// </summary>
        /// <param name="id">The unique identifier of the invoice to check.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the checked invoice.</returns>
        Task<Invoice> CheckInvoiceAsync(Guid id);
    }
}