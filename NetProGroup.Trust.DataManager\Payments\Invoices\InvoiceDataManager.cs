// <copyright file="InvoiceDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Diagnostics;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.Application.Contracts.Payments.Invoices;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Payments.Invoices.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Currencies;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Payments.Invoices
{
    /// <summary>
    /// Manages operations related to invoices such as retrieval, listing, and deletion of invoice records.
    /// </summary>
    public class InvoiceDataManager : IInvoiceDataManager
    {
        private readonly IMapper _mapper;
        private readonly IInvoiceRepository _invoiceRepository;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly IDocumentManager _documentManager;
        private readonly ISettingsManager _settingsManager;
        private readonly ILockManager _lockManager;

        private readonly ICurrenciesRepository _currencyRepository;
        private readonly ILegalEntitiesRepository _legalEntityRepository;
        private readonly IPaymentDataManager _paymentDataManager;
        private readonly ILogger<InvoiceDataManager> _logger;
        private readonly ISubmissionsRepository _submissionsRepository;
        private readonly IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="InvoiceDataManager"/> class.
        /// </summary>
        /// <param name="mapper">AutoMapper instance for mapping entities to DTOs.</param>
        /// <param name="invoiceRepository">Repository for accessing invoice data.</param>
        /// <param name="systemAuditManager">Manager for system auditing operations.</param>
        /// <param name="documentManager">Manager for document-related operations.</param>
        /// <param name="settingsManager">Manager instance for the settings.</param>
        /// <param name="currencyRepository">Repository for accessing currency data.</param>
        /// <param name="legalEntityRepository">Repository for accessing legal entity data.</param>
        /// <param name="lockManager">Manager for lock operations.</param>
        /// <param name="paymentDataManager">Manager for payment-related operations.</param>
        /// <param name="logger">Logger instance for logging operations.</param>
        /// <param name="submissionsRepository">Repository for accessing submission data.</param>
        /// <param name="authorizationFilterExpressionFactory">Factory for creating authorization filter expressions.</param>
        public InvoiceDataManager(
            IMapper mapper,
            IInvoiceRepository invoiceRepository,
            ISystemAuditManager systemAuditManager,
            IDocumentManager documentManager,
            ISettingsManager settingsManager,
            ICurrenciesRepository currencyRepository,
            ILegalEntitiesRepository legalEntityRepository,
            ILockManager lockManager,
            IPaymentDataManager paymentDataManager,
            ILogger<InvoiceDataManager> logger,
            ISubmissionsRepository submissionsRepository,
            IAuthorizationFilterExpressionFactory authorizationFilterExpressionFactory)
        {
            _mapper = mapper;
            _invoiceRepository = invoiceRepository;
            _systemAuditManager = systemAuditManager;
            _documentManager = documentManager;
            _settingsManager = settingsManager;
            _currencyRepository = currencyRepository;
            _legalEntityRepository = legalEntityRepository;
            _lockManager = lockManager;
            _paymentDataManager = paymentDataManager;
            _logger = logger;
            _submissionsRepository = submissionsRepository;
            _authorizationFilterExpressionFactory = authorizationFilterExpressionFactory;
        }

        /// <summary>
        /// Retrieves an invoice by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the invoice.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the <see cref="InvoiceDTO"/> representing the invoice.</returns>
        /// <exception cref="NotFoundException">Thrown if the invoice with the specified ID is not found.</exception>
        public async Task<InvoiceDTO> GetInvoiceAsync(Guid id)
        {
            var invoice = await _invoiceRepository.GetByIdAsync(id, options: invoices =>
                AddInvoiceDtoMapperIncludes(invoices)
                    .Include(i => i.InvoiceLines)
                    .AsSplitQuery());

            if (invoice == null)
            {
                throw new NotFoundException(ApplicationErrors.INVOICE_NOT_FOUND.ToErrorCode(), "Invoice not found");
            }

            return _mapper.Map<InvoiceDTO>(invoice);
        }

        /// <summary>
        /// Retrieves a paginated list of invoices based on the specified request parameters.
        /// </summary>
        /// <param name="request">The request containing filtering and pagination options.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains an <see cref="IPagedList{InvoiceDTO}"/> of invoices.</returns>
        /// <exception cref="ArgumentNullException">Thrown if the request parameter is null.</exception>
        public async Task<IPagedList<InvoiceDTO>> ListInvoicesAsync(InvoiceListRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var predicate = _authorizationFilterExpressionFactory.GetInvoiceUserIdFilterPredicate(request);

            var query = _invoiceRepository.GetQueryable().Where(predicate);

            query = AddInvoiceDtoMapperIncludes(query)
                        .Include(i => i.Document)
                        .AsSplitQuery();

            // Apply filters
            query = ApplyFilters(request, query);

            // Apply sorting
            query = ApplySorting(query, request.SortBy, request.SortOrder);

            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            // Convert invoices
            var dtos = _mapper.Map<List<InvoiceDTO>>(items);

            return new StaticPagedList<InvoiceDTO>(
                dtos, request.PageNumber, request.PageSize, totalCount);
        }

        /// <inheritdoc/>
        public async Task<string> GenerateInvoiceNumber(Guid jurisdictionId, Guid? moduleId)
        {
            // Get the configuration from the settings manager
            var numberingConfiguration = await _settingsManager.GetInvoiceNumberingSettingsAsync(jurisdictionId, moduleId);

            // Get the latest invoice number data
            var lastInvoiceNumberData = await _settingsManager.GetLastInvoiceNumberAsync(jurisdictionId, moduleId);

            // Generate a new invoice number

            // Save the latest back to the configuration

            return string.Empty;
        }

        /// <summary>
        /// Deletes the specified invoice by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the invoice to be deleted.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        /// <exception cref="NotFoundException">Thrown if the invoice with the specified ID is not found.</exception>
        public async Task DeleteInvoiceAsync(Guid id)
        {
            var invoice = await _invoiceRepository.GetByIdAsync(id);
            if (invoice == null)
            {
                throw new NotFoundException(ApplicationErrors.INVOICE_NOT_FOUND.ToErrorCode(), "Invoice not found");
            }

            await _invoiceRepository.DeleteAsync(invoice);

            await _systemAuditManager.AddActivityLogAsync(invoice, ActivityLogActivityTypes.InvoiceDeleted, "Invoice deleted", $"Invoice {invoice.InvoiceNr} deleted");

            await _invoiceRepository.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task<Invoice> CreateInvoiceAsync(CreateInvoiceRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            // A transaction is required when creating an invoice (for the invoice numbering)
            if (_invoiceRepository.DbContext.Database.CurrentTransaction == null)
            {
                if (_invoiceRepository.DbContext.Database.ProviderName != "Microsoft.EntityFrameworkCore.InMemory")
                {
                    throw new APIException("A transaction is required when creating an invoice");
                }
            }

            var currency = await _currencyRepository.FindFirstOrDefaultByConditionAsync(c => c.Code == request.CurrencyCode);
            if (currency == null)
            {
                throw new ConstraintException(ApplicationErrors.CURRENCY_NOT_FOUND.ToErrorCode(), $"Currency '{request.CurrencyCode}' not found");
            }

            var currencyId = currency.Id;

            var invoice = new Invoice(Guid.NewGuid())
            {
                Status = InvoiceStatus.Pending,
                LegalEntityId = request.LegalEntityId,
                FinancialYear = request.FinancialYear.GetValueOrDefault(0),
                CurrencyId = currencyId,
                Date = DateTime.UtcNow,
                Layout = LayoutConsts.TridentTrust
            };

            decimal totalAmount = 0;

            foreach (var invoiceLine in request.InvoiceLines)
            {
                var dbInvoiceLine = new InvoiceLine
                {
                    Sequence = invoiceLine.Sequence,
                    Description = invoiceLine.Description,
                    CurrencyId = currencyId,
                    Amount = invoiceLine.Amount,
                    ArticleNr = invoiceLine.ArticleNr
                };

                totalAmount += dbInvoiceLine.Amount;

                invoice.InvoiceLines.Add(dbInvoiceLine);
            }

            invoice.Amount = totalAmount;

            // Create an invoicenumber. Use a lock for exclusive use of the invoicenumber.
            var invoiceLock = await AcquireLockAsync();
            try
            {
                var legalEntity = await _legalEntityRepository.GetByIdAsync(invoice.LegalEntityId);
                var invoiceNumberingSettings = await _settingsManager.GetInvoiceNumberingSettingsAsync(legalEntity.JurisdictionId.Value, request.ModuleId);
                var lastInvoiceData = await _settingsManager.GetLastInvoiceNumberAsync(legalEntity.JurisdictionId.Value, request.ModuleId);

                if (invoiceNumberingSettings == null)
                {
                    throw new ConstraintException(ApplicationErrors.INVOICE_NUMBERING_SETTINGS_NOT_FOUND.ToErrorCode(), "No settings found for invoice numbering");
                }

                var newInvoiceData = InvoiceNumberGenerator.GenerateNewInvoiceNumber(invoiceNumberingSettings, lastInvoiceData);
                invoice.InvoiceNr = newInvoiceData.FullInvoiceNumber;

                await _settingsManager.SetLastInvoiceNumberAsync(legalEntity.JurisdictionId.Value, request.ModuleId, newInvoiceData);

                await _invoiceRepository.InsertAsync(invoice);
            }
            finally
            {
                await _lockManager.ReleaseLockAsync(invoiceLock);
            }

            return invoice;
        }

        /// <inheritdoc />
        public async Task MarkInvoiceAsPaidAsync(Invoice invoice, bool isPaid, bool saveChanges = false)
        {
            Check.NotNull(invoice, nameof(invoice));
            Check.NotNull(invoice.LegalEntity, nameof(invoice.LegalEntity));

            var legalEntity = invoice.LegalEntity;

            if (invoice.PaymentInvoices != null)
            {
                foreach (var paymentInvoice in invoice.PaymentInvoices)
                {
                    if (paymentInvoice.Payment.ExpirationDate > DateTime.UtcNow)
                    {
                        if (paymentInvoice.Payment.Status == PaymentStatus.Pending ||
                            paymentInvoice.Payment.Status == PaymentStatus.InProgress)
                        {
                            _logger.LogError("Invoice {InvoiceNr} is currently involved in a payment transaction", invoice.InvoiceNr);
                            throw new PreconditionFailedException(ApplicationErrors.INVOICE_IS_IN_PAYMENT_TRANSACTION.ToErrorCode(), "Invoice is in a payment transaction");
                        }
                    }
                }
            }

            if (isPaid)
            {
                if (invoice.Status == InvoiceStatus.Paid)
                {
                    throw new PreconditionFailedException(ApplicationErrors.INVOICE_IS_ALREADY_PAID.ToErrorCode(), "Invoice is already paid");
                }

                _logger.LogInformation("Marking invoice {InvoiceNr} as paid", invoice.InvoiceNr);
                await _paymentDataManager.CreateOrUpdateManualPaymentAsync(invoice, legalEntity, saveChanges: false);

                invoice.Status = InvoiceStatus.Paid;
            }
            else
            {
                _logger.LogInformation("Marking invoice {InvoiceNr} as unpaid", invoice.InvoiceNr);
                invoice.Status = InvoiceStatus.Pending;
            }

            await _invoiceRepository.UpdateAsync(invoice, saveChanges);
        }

        /// <inheritdoc />
        public async Task<List<ModuleDTO>> GetModulesForInvoiceIdAsync(Guid id)
        {
            var module = await _submissionsRepository.DbContext.Set<Submission>()
                                                     .Where(i => i.InvoiceId == id)
                                                     .Select(x => x.Module)
                                                     .ToListAsync();

            return _mapper.Map<List<ModuleDTO>>(module);
        }

        /// <inheritdoc />
        public async Task<MasterClientDTO> GetMasterClientByInvoiceId(Guid id)
        {
            var masterClient = await _invoiceRepository.GetMasterClientByInvoiceId(id);

            return _mapper.Map<MasterClientDTO>(masterClient);
        }

        /// <inheritdoc />
        public async Task<Invoice> CheckInvoiceAsync(Guid id)
        {
            var invoice = await _invoiceRepository.CheckInvoiceByIdAsync(id);

            return invoice;
        }

        /// <summary>
        /// Adds the necessary includes to the invoice query to map the entities to DTOs.
        /// </summary>
        /// <param name="queryable">The queryable object representing the invoice query.</param>
        /// <returns>An IQueryable of invoices with the necessary includes.</returns>
        private static IQueryable<Invoice> AddInvoiceDtoMapperIncludes(IQueryable<Invoice> queryable)
        {
            return queryable
                   .Include(i => i.LegalEntity)
                   .Include(i => i.Currency)
                   .Include(i => i.Submissions)
                        .ThenInclude(s => s.FormDocument.Attributes)
                   .Include(i => i.PaymentInvoices)
                        .ThenInclude(pi => pi.Payment)
                            .ThenInclude(p => p.PaymentTransactions);
        }

        /// <summary>
        /// Applies filters to the invoice query based on the specified request parameters.
        /// </summary>
        /// <param name="request">The request containing filter options.</param>
        /// <param name="query">The IQueryable object representing the invoice query.</param>
        /// <returns>An IQueryable of invoices with the applied filters.</returns>
        private static IQueryable<Invoice> ApplyFilters(InvoiceListRequestDTO request, IQueryable<Invoice> query)
        {
            var filters = new Dictionary<Func<InvoiceListRequestDTO, bool>, Func<IQueryable<Invoice>, IQueryable<Invoice>>>
            {
                { r => !string.IsNullOrEmpty(r.SearchTerm), q => q.Where(i => i.InvoiceNr.Contains(request.SearchTerm) || i.LegalEntity.Name.Contains(request.SearchTerm)) },
                { r => r.Status.HasValue, q => ApplyStatusFilter(q, request.Status.Value) },
                { r => r.MasterClientId.HasValue, q => q.Where(i => i.LegalEntity.MasterClientId == request.MasterClientId.Value) },
                { r => r.CompanyId.HasValue, q => q.Where(i => i.LegalEntityId == request.CompanyId.Value) },
                { r => r.FinancialYear.HasValue, q => q.Where(i => i.FinancialYear == request.FinancialYear.Value) },
                { r => r.ToDate.HasValue, q => q.Where(i => i.Date <= request.ToDate.Value) }
            };

            return filters.Aggregate(query, (current, filter) =>
                filter.Key(request) ? filter.Value(current) : current);
        }

        private static IQueryable<Invoice> ApplyStatusFilter(IQueryable<Invoice> query, InvoicePaymentStatus status)
        {
            return status switch
            {
                InvoicePaymentStatus.UnPaid => query.Where(i => !i.PaymentInvoices.Any(pi => !pi.Payment.IsDeleted) || i.PaymentInvoices.Any(pi => pi.Payment.Status != PaymentStatus.Completed && !pi.Payment.IsDeleted)),
                InvoicePaymentStatus.Paid => query.Where(i => i.PaymentInvoices.Any() && i.PaymentInvoices.All(pi => pi.Payment.Status == PaymentStatus.Completed)),
                _ => query
            };
        }

        /// <summary>
        /// Applies sorting to the invoice query based on the specified sort parameters.
        /// </summary>
        /// <param name="query">The IQueryable object representing the invoice query.</param>
        /// <param name="sortBy">The field by which to sort the query.</param>
        /// <param name="sortOrder">The order in which to sort (asc or desc).</param>
        /// <returns>An IQueryable of invoices with the applied sorting.</returns>
        private static IQueryable<Invoice> ApplySorting(IQueryable<Invoice> query, string sortBy, string sortOrder)
        {
            return sortBy switch
            {
                var sb when string.Equals(sb, "InvoiceNr", StringComparison.OrdinalIgnoreCase) => sortOrder == "asc"
                    ? query.OrderBy(i => i.InvoiceNr)
                    : query.OrderByDescending(i => i.InvoiceNr),

                var sb when string.Equals(sb, "Date", StringComparison.OrdinalIgnoreCase) => sortOrder == "asc"
                    ? query.OrderBy(i => i.Date)
                    : query.OrderByDescending(i => i.Date),

                var sb when string.Equals(sb, "Amount", StringComparison.OrdinalIgnoreCase) => sortOrder == "asc"
                    ? query.OrderBy(i => i.Amount)
                    : query.OrderByDescending(i => i.Amount),

                var sb when string.Equals(sb, "LegalEntityName", StringComparison.OrdinalIgnoreCase) => sortOrder == "asc"
                    ? query.OrderBy(i => i.LegalEntity.Name)
                    : query.OrderByDescending(i => i.LegalEntity.Name),

                _ => query.OrderByDescending(i => i.Date)
            };
        }

        /// <summary>
        /// Converts the list of invoices into a list of <see cref="InvoiceDTO"/>s with document content encoded as base64.
        /// </summary>
        /// <param name="invoices">The list of invoice entities to convert.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of <see cref="InvoiceDTO"/>s.</returns>
        private async Task<List<InvoiceDTO>> RetrieveDocumentsAsBase64(List<Invoice> invoices)
        {
            var invoicesDto = new List<InvoiceDTO>();
            foreach (var invoice in invoices)
            {
                var dto = _mapper.Map<InvoiceDTO>(invoice);
                if (invoice.Document != null && !string.IsNullOrEmpty(invoice.Document.BlobName))
                {
                    var fileBytes = await _documentManager.GetDocumentDataAsync(invoice.Document);
                    dto.File = Convert.ToBase64String(fileBytes);
                }

                invoicesDto.Add(dto);
            }

            return invoicesDto;
        }

        private async Task<LockDTO> AcquireLockAsync(int timeout = 5)
        {
            var request = new AcquireLockRequestDTO
            {
                IdentityUserId = Guid.NewGuid(),
                EntityName = "InvoiceNumberLock",
                EntityId = Guid.Empty,
                Session = string.Empty
            };

            var sw = new Stopwatch();
            sw.Start();

            while (sw.Elapsed.TotalSeconds < timeout)
            {
                var response = await _lockManager.AcquireLockAsync(request);

                if (response.Id.HasValue)
                {
                    return response;
                }
            }

            throw new APIException("Timeout on acquiring invoicenumber lock");
        }
    }
}