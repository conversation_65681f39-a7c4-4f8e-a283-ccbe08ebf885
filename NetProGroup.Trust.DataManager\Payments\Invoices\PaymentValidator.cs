// <copyright file="PaymentValidator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;

namespace NetProGroup.Trust.DataManager.Payments.Invoices
{
    /// <summary>
    /// Validates payment information related to invoices.
    /// </summary>
    public class PaymentValidator : IPaymentValidator
    {
        private readonly IInvoiceRepository _invoiceRepository;
        private readonly IPaymentRepository _paymentRepository;
        private readonly IDateTimeProvider _dateTimeProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentValidator"/> class.
        /// </summary>
        /// <param name="invoiceRepository">The repository for invoice data.</param>
        /// <param name="paymentRepository">The repository for payment data.</param>
        /// <param name="dateTimeProvider">The provider for date and time operations.</param>
        /// <exception cref="ArgumentNullException">
        /// Thrown when any of the provided repository or provider parameters are <c>null</c>.
        /// </exception>
        public PaymentValidator(IInvoiceRepository invoiceRepository,
            IPaymentRepository paymentRepository,
            IDateTimeProvider dateTimeProvider)
        {
            _invoiceRepository = invoiceRepository ?? throw new ArgumentNullException(nameof(invoiceRepository));
            _paymentRepository = paymentRepository ?? throw new ArgumentNullException(nameof(paymentRepository));
            _dateTimeProvider = dateTimeProvider ?? throw new ArgumentNullException(nameof(dateTimeProvider));
        }

        /// <summary>
        /// Validates the payment request by checking jurisdiction, currency, and existing payment status.
        /// </summary>
        /// <param name="request">The payment request containing invoice and currency information.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        /// <exception cref="ArgumentException">Thrown when validation fails for jurisdiction or currency.</exception>
        /// <exception cref="InvalidOperationException">Thrown when payment status prohibits further actions.</exception>
        public async Task ValidatePaymentRequest(CreatePaymentRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request);

            await ValidateInvoicesInSameJurisdiction(request.InvoiceIds);
            await ValidateInvoicesInSameCurrency(request.InvoiceIds, request.CurrencyId);
            await ValidateInvoices(request.InvoiceIds);
        }

        /// <summary>
        /// This method validates the payment transaction status for existing payments.
        /// It checks if the payment is in progress, if it has expired, or if it is deletable.
        /// </summary>
        /// <param name="request">THe payment request object containing invoice and currency information.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task ValidatePaymentTransactionStatusAsync(CreatePaymentRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            var payment = await FetchExistingPayment(request.InvoiceIds);
            if (payment == null)
            {
                return;
            }

            await HandleExistingPayment(payment);
        }

        /// <summary>
        /// Validates that all invoices are within the same jurisdiction.
        /// </summary>
        private async Task ValidateInvoicesInSameJurisdiction(List<Guid> invoiceIds)
        {
            if (!await _invoiceRepository.IsSameJurisdiction(invoiceIds))
            {
                throw new ConstraintException(ApplicationErrors.INVOICE_IS_NOT_SAME_JURISDICTION.ToErrorCode(),
                    "All invoices must be in the same jurisdiction.");
            }
        }

        /// <summary>
        /// Validates that all invoices share the same currency.
        /// </summary>
        private async Task ValidateInvoicesInSameCurrency(List<Guid> invoiceIds, Guid expectedCurrencyId)
        {
            if (!await _invoiceRepository.IsSameCurrency(invoiceIds, expectedCurrencyId))
            {
                throw new ConstraintException(ApplicationErrors.INVOICE_IS_NOT_SAME_CURRENCY.ToErrorCode(),
                    "All invoices must be in the same currency.");
            }
        }

        /// <summary>
        /// Validates if any of the invoices are associated with an existing payment.
        /// </summary>
        /// <exception cref="ArgumentException">Thrown when invoice IDs are null or empty.</exception>
        private async Task ValidateInvoices(IEnumerable<Guid> invoiceIds)
        {
            if (invoiceIds == null || !invoiceIds.Any())
            {
                throw new NotFoundException(ApplicationErrors.INVOICE_NOT_FOUND.ToErrorCode(), "Invoice IDs cannot be null or empty");
            }

            var invoices = await _invoiceRepository.FindByConditionAsync(i => invoiceIds.Contains(i.Id));
            foreach (var invoice in invoices)
            {
                if (invoice.Status == DomainShared.Enums.InvoiceStatus.Paid)
                {
                    throw new BadRequestException(ApplicationErrors.INVOICE_IS_ALREADY_PAID.ToErrorCode(), $"Invoice {invoice.InvoiceNr} is already paid");
                }
            }
        }

        private async Task<Domain.Payments.Payment> FetchExistingPayment(IEnumerable<Guid> invoiceIds)
        {
            return await _paymentRepository.GetActivePaymentForInvoices(invoiceIds);
        }

        private async Task HandleExistingPayment(Domain.Payments.Payment payment)
        {
            if (IsPaymentTransactionOnPlace(payment))
            {
                throw new ConstraintException(ApplicationErrors.PAYMENT_CANNOT_BE_REPLACED.ToErrorCode(),
                    "Transactions is not suitable for replacement.");
            }

            if (IsDeletablePayment(payment))
            {
                await _paymentRepository.SoftDeletePayment(payment);
            }
        }

        private bool IsDeletablePayment(Domain.Payments.Payment payment)
        {
            // Check if the payment has expired
            bool hasExpired = HasPaymentExpired(payment);

            // Check if the payment status allows deletion
            bool isDeletableStatus = payment.Status != PaymentStatus.Pending && payment.Status != PaymentStatus.Completed;

            // Return true if either the payment has expired or it has a deletable status
            return !payment.IsDeleted && !payment.PaidAt.HasValue && (hasExpired || isDeletableStatus);
        }

        /// <summary>
        /// Checks if the payment is valid for replacement.
        /// </summary>
        /// <param name="payment">The payment to check.</param>
        /// <returns>True if the payment is in valid replacement state, false otherwise.</returns>
        private bool IsPaymentTransactionOnPlace(Domain.Payments.Payment payment)
        {
            // Check if the payment has expired
            bool isValidPayment = !payment.IsDeleted && !HasPaymentExpired(payment);
            bool isDeletablePayment = payment.Status is PaymentStatus.InProgress or
                PaymentStatus.Completed or PaymentStatus.Pending;
            return isValidPayment && isDeletablePayment;
        }

        /// <summary>
        /// Checks if the payment has expired.
        /// </summary>
        /// <param name="payment">The payment to check.</param>
        /// <returns>True if the payment has expired, false otherwise.</returns>
        private bool HasPaymentExpired(Domain.Payments.Payment payment)
            => payment.ExpirationDate <= _dateTimeProvider.UtcNow;
    }
}