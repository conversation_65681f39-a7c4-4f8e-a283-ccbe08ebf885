// <copyright file="CreateInvoiceLine.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.DataManager.Payments.Invoices.RequestResponses
{
    /// <summary>
    /// Represents a request to create an invoice line.
    /// </summary>
    public class CreateInvoiceLine
    {
        /// <summary>
        /// Gets or sets the description of the invoice line.
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the sequence number of the invoice line.
        /// </summary>
        public int Sequence { get; set; }

        /// <summary>
        /// Gets or sets the amount for the invoice line.
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Gets or sets the ArticleNr of the invoice line.
        /// </summary>
        public string ArticleNr { get; set; }
    }
}
