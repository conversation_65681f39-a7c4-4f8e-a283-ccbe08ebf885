// <copyright file="PaymentDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Locks;
using NetProGroup.Trust.Application.Contracts.Payments;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Application.Contracts.Payments.Response;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Payments.Invoices;
using NetProGroup.Trust.DataManager.Payments.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Shared.Defines;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Payments
{
    /// <summary>
    /// Manages operations related to payments such as retrieval, listing, and deletion of payment records.
    /// </summary>
    public class PaymentDataManager : IPaymentDataManager
    {
        private readonly IMapper _mapper;
        private readonly IServiceProvider _serviceProvider;
        private readonly IPaymentRepository _paymentRepository;
        private readonly IPaymentValidator _paymentValidator;
        private readonly ILockManager _lockManager;
        private readonly IInvoiceRepository _invoiceRepository;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly IDateTimeProvider _dateTimeProvider;
        private readonly IWorkContext _workContext;
        private readonly ILogger _logger;
        private readonly IPaymentTransactionRepository _paymentTransactionRepository;
        private readonly IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentDataManager"/> class.
        /// </summary>
        /// <param name="mapper">The mapper used for object-to-object mapping.</param>
        /// <param name="serviceProvider">The service provider used for dependency injection.</param>
        /// <param name="paymentRepository">The repository used for accessing payment data.</param>
        /// <param name="invoiceRepository">The repository used for accessing invoice data.</param>
        /// <param name="systemAuditManager">The audit manager used for logging system activities.</param>
        /// <param name="paymentValidator">The validator used for validating invoice payments.</param>
        /// <param name="lockManager">The lock manager used for managing locks.</param>
        /// <param name="dateTimeProvider">The datetime provider used for generating expiration dates.</param>
        /// <param name="workContext">The work context used for accessing user information.</param>
        /// <param name="logger">The logger used for logging.</param>
        /// <param name="paymentTransactionRepository">The repository used for accessing payment transaction data.</param>
        /// <param name="authorizationFilterExpressionFactory">The authorization filter expression factory.</param>
        public PaymentDataManager(
            IMapper mapper,
            IServiceProvider serviceProvider,
            IPaymentRepository paymentRepository,
            IPaymentValidator paymentValidator,
            IInvoiceRepository invoiceRepository,
            ISystemAuditManager systemAuditManager,
            ILockManager lockManager,
            IDateTimeProvider dateTimeProvider,
            IWorkContext workContext,
            ILogger<PaymentDataManager> logger,
            IPaymentTransactionRepository paymentTransactionRepository,
            IAuthorizationFilterExpressionFactory authorizationFilterExpressionFactory)
        {
            _mapper = mapper;
            _paymentRepository = paymentRepository;
            _paymentValidator = paymentValidator;
            _invoiceRepository = invoiceRepository;
            _systemAuditManager = systemAuditManager;
            _lockManager = lockManager;
            _dateTimeProvider = dateTimeProvider;
            _workContext = workContext;
            _logger = logger;
            _paymentTransactionRepository = paymentTransactionRepository;
            _authorizationFilterExpressionFactory = authorizationFilterExpressionFactory;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Retrieves a payment by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the payment.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the payment data transfer object.</returns>
        /// <exception cref="NotFoundException">Thrown when the payment is not found.</exception>
        public async Task<PaymentDetailsResponseDTO> GetPaymentAsync(Guid id)
        {
            var payment = await _paymentRepository.GetByIdAsync(id,
                q => q.Include(p => p.PaymentTransactions)) ??
                throw new NotFoundException(ApplicationErrors.PAYMENT_NOT_FOUND.ToErrorCode(), "Payment not found");
            return _mapper.Map<PaymentDetailsResponseDTO>(payment);
        }

        /// <summary>
        /// Creates a payment based on the specified request criteria.
        /// </summary>
        /// <param name="request">The request object containing the details required to create a payment.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a <see cref="PaymentDTO"/> object representing the created payment.
        /// </returns>
        /// <exception cref="ArgumentException">Thrown when invoices are already in payments or not in the same currency.</exception>
        public async Task<CreatePaymentResponseDTO> CreatePaymentsAsync(CreatePaymentRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            await _paymentValidator.ValidatePaymentRequest(request);

            var jurisdiction = await _invoiceRepository.GetJurisdictionByInvoiceId(request.InvoiceIds.First());
            var masterClient = await _invoiceRepository.GetMasterClientByInvoiceId(request.InvoiceIds.First());
            var legalEntities = await _invoiceRepository.GetLegalEntitiesByInvoiceIds(request.InvoiceIds);

            // Acquire lock prevent new payment for the same masterclient
            var paymentLock = await AcquireLockAsync(masterClient.Id);

            // Create the reference for the payment
            var reference = CreatePaymentReference(masterClient, null);
            if (legalEntities.Count == 1)
            {
                var legalEntity = legalEntities[0];

                reference = CreatePaymentReference(masterClient, legalEntity);
            }

            try
            {
                await _paymentValidator.ValidatePaymentTransactionStatusAsync(request);

                var amount = await _invoiceRepository.GetQueryable()
                    .Where(il => request.InvoiceIds.Contains(il.Id))
                    .SumAsync(il => il.Amount);

                var paymentProvider = _invoiceRepository.GetPaymentProviderSettingsForCompany(request.InvoiceIds.First());

                var payment = new Domain.Payments.Payment(Guid.NewGuid())
                {
                    Amount = amount,
                    Status = PaymentStatus.InProgress,
                    LegalEntityId = request.LegalEntityId,
                    JurisdictionId = jurisdiction.Id,
                    CurrencyId = request.CurrencyId,
                    CreatedAt = _dateTimeProvider.UtcNow,
                    Type = PaymentType.CreditCard,
                    ExpirationDate = _dateTimeProvider.UtcNow
                        .Add(paymentProvider.GatewayExpirationTimestamp),
                    PaymentInvoices = request.InvoiceIds
                        .Select(i => new PaymentInvoice { InvoiceId = i }).ToList(),
                    Reference = reference,
                };

                await _paymentRepository.InsertAsync(payment);
                await _paymentRepository.SaveChangesAsync();
                return _mapper.Map<CreatePaymentResponseDTO>(payment);
            }
            finally
            {
                await _lockManager.ReleaseLockAsync(paymentLock);
            }
        }

        /// <inheritdoc />
        public async Task CancelPaymentAsync(Guid paymentId, bool saveChanges = false)
        {
            var payment = await _paymentRepository.GetByIdAsync(paymentId);
            if (payment == null)
            {
                throw new NotFoundException(ApplicationErrors.PAYMENT_NOT_FOUND.ToErrorCode(), $"Payment '{paymentId}' was not found");
            }

            SetPaymentSoftDeleted(payment);

            await _paymentRepository.UpdateAsync(payment, saveChanges: saveChanges);
        }

        /// <inheritdoc />
        public async Task CreateOrUpdateManualPaymentAsync(Invoice invoice, LegalEntity legalEntity, bool saveChanges)
        {
            var userName = _workContext.UserName;

            Check.NotNull(invoice, nameof(invoice));
            Check.NotNull(legalEntity, nameof(legalEntity));
            Check.NotNull(legalEntity.MasterClient, nameof(legalEntity.MasterClient));

            Domain.Payments.Payment payment;
            var paymentInvoice = invoice.PaymentInvoices.SingleOrDefault(i => i.Payment.Type == PaymentType.WireTransfer);
            if (paymentInvoice == null)
            {
                _logger.LogInformation("Creating new payment for invoice {InvoiceNr}", invoice.InvoiceNr);
                paymentInvoice = new PaymentInvoice { Invoice = invoice, Payment = new Domain.Payments.Payment() };
                invoice.PaymentInvoices.Add(paymentInvoice);
                payment = paymentInvoice.Payment;
                await _paymentRepository.InsertAsync(payment, false);
            }
            else
            {
                _logger.LogInformation("Updating existing payment for invoice {InvoiceNr}", invoice.InvoiceNr);
                payment = paymentInvoice.Payment;
                await _paymentRepository.UpdateAsync(payment, false);
            }

            // Create the reference for the payment
            var reference = CreatePaymentReference(legalEntity.MasterClient, legalEntity);

            payment.Amount = invoice.Amount;
            payment.CurrencyId = invoice.CurrencyId;
            payment.LegalEntityId = invoice.LegalEntityId;
            payment.Reference = reference;
            payment.Status = PaymentStatus.Completed;
            payment.JurisdictionId = legalEntity.JurisdictionId;
            payment.Type = PaymentType.WireTransfer;
            payment.ConfirmedBy = userName;
            payment.PaidAt = _dateTimeProvider.UtcNow;
            payment.MasterClientId = legalEntity.MasterClient.Id;

            // Check for pending payments for the invoice (credit card transactions never completed) and soft delete them
            var paymentStatuses = new PaymentStatus[] { PaymentStatus.Pending, PaymentStatus.InProgress };
            var incompletePayments = invoice.PaymentInvoices.Select(pi => pi.Payment).Where(p => paymentStatuses.Contains(p.Status) && p.IsDeleted == false);
            foreach (var incompletePayment in incompletePayments)
            {
                SetPaymentSoftDeleted(incompletePayment);
                await _paymentRepository.UpdateAsync(incompletePayment, false);

                _logger.LogInformation("Soft delete existing payment {PaymentId} for invoice {InvoiceNr}", incompletePayment.Id, invoice.InvoiceNr);
            }

            if (saveChanges)
            {
                await _paymentRepository.SaveChangesAsync();
            }
        }

        /// <inheritdoc/>
        public async Task HandleCompletedPaymentByTransactionIdAsync(Guid transactionId, bool saveChanges = false)
        {
            Check.NotDefaultOrNull<Guid>(transactionId, nameof(transactionId));

            // Get the transaction with the invoices
            var transaction = await _paymentTransactionRepository.GetByIdAsync(transactionId, q => q.Include(t => t.Payment)
                                                                                                     .ThenInclude(p => p.PaymentInvoices)
                                                                                                     .ThenInclude(pi => pi.Invoice));

            var payment = transaction.Payment;

            _logger.LogInformation("Marking invoices for payment {PaymentReference} as paid", payment.Reference);

            var submissionsManager = _serviceProvider.GetRequiredService<ISubmissionsManager>();

            // Iterate the invoices
            foreach (var paymentInvoice in payment.PaymentInvoices)
            {
                var invoice = paymentInvoice.Invoice;

                if (invoice.Status != DomainShared.Enums.InvoiceStatus.Paid)
                {
                    _logger.LogInformation("Marking invoice {InvoiceNr} as paid", invoice.InvoiceNr);

                    invoice.Status = DomainShared.Enums.InvoiceStatus.Paid;
                    await _invoiceRepository.UpdateAsync(invoice, false);
                }

                // Let the submissionsmanager check if the there is a submission for the invoice and mark it as paid
                await submissionsManager.MarkSubmissionsAsPaidByInvoiceIdAsync(invoice.Id, saveChanges: false);
            }

            // Update the payment itself
            if (payment.Status != PaymentStatus.Completed)
            {
                payment.Status = PaymentStatus.Completed;
                payment.PaidAt = DateTime.UtcNow;
            }
            else
            {
                _logger.LogWarning("Payment status for transaction {TransactionId} is already {PaymentStatus}, only update invoices and submissions", transactionId, payment.Status);
            }

            await _paymentRepository.UpdateAsync(payment, saveChanges: false);

            if (saveChanges)
            {
                await _paymentTransactionRepository.SaveChangesAsync();
            }
        }

        /// <summary>
        /// Retrieves a paginated list of payments based on the specified filter criteria.
        /// </summary>
        /// <param name="request">The request containing filter criteria and pagination information.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a paginated list of payment data transfer objects.</returns>
        public async Task<IPagedList<PaymentDTO>> ListPaymentsAsync(PaymentsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            Expression<Func<Domain.Payments.Payment, bool>> predicate = _authorizationFilterExpressionFactory.GetPaymentUserIdFilterPredicate(request);

            var query = _paymentRepository.GetQueryable()
                .Where(predicate)
                .Include(p => p.LegalEntity)
                .Include(p => p.Currency)
                .Include(p => p.PaymentInvoices)
                .ThenInclude(i => i.Invoice)
                .AsQueryable();

            // Apply filters
            query = ApplyFilters(request, query);
            query = ApplyMandatoryFilters(query);
            // Apply sorting
            query = ApplySorting(query, request.SortBy, request.SortOrder);

            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync();

            var dtos = _mapper.Map<List<PaymentDTO>>(items);
            return new StaticPagedList<PaymentDTO>(
                dtos, request.PageNumber, request.PageSize, totalCount);
        }

        /// <summary>
        /// Deletes a payment by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the payment.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        /// <exception cref="NotFoundException">Thrown when the payment is not found.</exception>
        public async Task DeletePaymentAsync(Guid id)
        {
            var payment = await _paymentRepository.GetByIdAsync(id);
            if (payment == null)
            {
                throw new NotFoundException(ApplicationErrors.PAYMENT_NOT_FOUND.ToErrorCode(), "Payment not found");
            }

            await _paymentRepository.DeleteAsync(payment);

            await _systemAuditManager.AddActivityLogAsync(payment, ActivityLogActivityTypes.PaymentDeleted,
                "Payment deleted", $"Payment with Id {payment.Id} deleted");

            await _paymentRepository.SaveChangesAsync();
        }

        private static void SetPaymentSoftDeleted(Domain.Payments.Payment payment)
        {
            payment.IsDeleted = true;
            payment.DeletedAt = DateTime.UtcNow;
            payment.ExpirationDate = new DateTime(2000, 1, 1);
        }

        /// <summary>
        /// Applies filters to the payment query based on the specified request.
        /// </summary>
        /// <param name="request">The request containing the filter criteria.</param>
        /// <param name="query">The initial queryable collection of payments.</param>
        /// <returns>The filtered queryable collection of payments.</returns>
        private static IQueryable<Domain.Payments.Payment> ApplyFilters(PaymentsRequest request, IQueryable<Domain.Payments.Payment> query)
        {
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                query = query.Where(p => p.LegalEntity.Name.Contains(request.SearchTerm) ||
                                         p.PaymentInvoices.Any(i => i.Invoice.InvoiceNr.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)));
            }

            if (request.CompanyId.HasValue)
            {
                query = query.Where(p => p.LegalEntityId == request.CompanyId.Value);
            }

            if (request.FinancialYear.HasValue)
            {
                query = query.Where(p => p.PaymentInvoices.Any(i => i.Invoice.FinancialYear == request.FinancialYear.Value));
            }

            if (request.FromDate.HasValue)
            {
                query = query.Where(p => p.PaymentInvoices.Any(i => i.Invoice.Date >= request.FromDate.Value));
            }

            if (request.ToDate.HasValue)
            {
                query = query.Where(p => p.PaymentInvoices.Any(i => i.Invoice.Date <= request.ToDate.Value));
            }

            if (request.Status.HasValue)
            {
                query = query.Where(p => p.Status == request.Status.Value);
            }

            return query;
        }

        private static IQueryable<Domain.Payments.Payment> ApplyMandatoryFilters(IQueryable<Domain.Payments.Payment> query)
        {
            query = query.Where(p => !p.IsDeleted);
            return query;
        }

        /// <summary>
        /// Shared method to create the payment reference.
        /// </summary>
        /// <param name="masterClient">The masterclient to get the code from.</param>
        /// <param name="legalEntity">The optional legalentity to get te code from.</param>
        /// <returns>The payment reference.</returns>
        private static string CreatePaymentReference(MasterClient masterClient, LegalEntity legalEntity)
        {
            if (legalEntity == null)
            {
                return $"{masterClient.Code}-{DateTime.Now:yyyyMMdd}";
            }
            else
            {
                return $"{masterClient.Code}-{legalEntity.Code}-{DateTime.Now:yyyyMMdd}";
            }
        }

        /// <summary>
        /// Applies sorting to the payment query based on the specified sort criteria.
        /// </summary>
        /// <param name="query">The initial queryable collection of payments.</param>
        /// <param name="sortBy">The field to sort by.</param>
        /// <param name="sortOrder">The order to sort (asc or desc).</param>
        /// <returns>The sorted queryable collection of payments.</returns>
        private static IQueryable<Domain.Payments.Payment> ApplySorting(IQueryable<Domain.Payments.Payment> query, string sortBy, string sortOrder)
        {
            return sortBy switch
            {
                var sb when string.Equals(sb, "Amount", StringComparison.OrdinalIgnoreCase) => sortOrder == "asc"
                    ? query.OrderBy(p => p.Amount)
                    : query.OrderByDescending(p => p.Amount),

                var sb when string.Equals(sb, "Date", StringComparison.OrdinalIgnoreCase) => sortOrder == "asc"
                    ? query.OrderBy(p => p.PaymentInvoices.Min(i => i.Invoice.Date))
                    : query.OrderByDescending(p => p.PaymentInvoices.Max(i => i.Invoice.Date)),

                var sb when string.Equals(sb, "Status", StringComparison.OrdinalIgnoreCase) => sortOrder == "asc"
                    ? query.OrderBy(p => p.Status)
                    : query.OrderByDescending(p => p.Status),

                var sb when string.Equals(sb, "LegalEntityName", StringComparison.OrdinalIgnoreCase) => sortOrder == "asc"
                    ? query.OrderBy(p => p.LegalEntity.Name)
                    : query.OrderByDescending(p => p.LegalEntity.Name),

                _ => query.OrderByDescending(p => p.PaymentInvoices.Max(i => i.Invoice.Date))
            };
        }

        private async Task<LockDTO> AcquireLockAsync(Guid entityId)
        {
            var request = new AcquireLockRequestDTO
            {
                IdentityUserId = Guid.NewGuid(),
                EntityName = ApplicationLocks.PAYMENT_TRANSACTION_LOCK,
                EntityId = entityId,
                Session = string.Empty
            };

            var response = await _lockManager.AcquireLockAsync(request);

            if (response.Id.HasValue)
            {
                return response;
            }

            throw new ConstraintException(ApplicationErrors.PAYMENT_TRANSACTION_LOCK.ToErrorCode(), "Payment transaction is locked");
        }
    }
}
