﻿// <copyright file="ISettingsManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.SettingsModels;

namespace NetProGroup.Trust.DataManager.Settings
{
    /// <summary>
    /// Interface for the settingsmanager.
    /// </summary>
    public interface ISettingsManager : IScopedService
    {
        /// <summary>
        /// Gets the settings for invoicing for the given company.
        /// </summary>
        /// <remarks>
        /// If a setting is not defined for the compnay it is looked up at the masterclient and not found there at the jurisdiction.
        /// </remarks>
        /// <param name="companyId">Id of the compnay to get the settings for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the invoice settings for the company.</returns>
        Task<InvoiceSettingsDTO> GetInvoiceSettingsForCompanyAsync(Guid companyId);

        ///// <summary>
        ///// Asynchronously retrieves the payment provider settings for the specified company.
        ///// </summary>
        ///// <param name="companyId">The unique identifier of the company for which to retrieve the payment provider settings.</param>
        ///// <returns>
        ///// A task that represents the asynchronous operation. The task result contains the <see cref="PaymentProvider"/> settings for the specified company.
        ///// </returns>
        ///// <exception cref="ArgumentException">Thrown when the <paramref name="companyId"/> is an empty GUID.</exception>
        ///// <exception cref="EntityNotFoundException">Thrown when no payment provider settings are found for the specified company.</exception>
        // PaymentProvider GetPaymentProviderSettingsForCompany(Guid companyId);

        /// <summary>
        /// Gets the value for a specific setting for a company.
        /// </summary>
        /// <typeparam name="TPropType">The type of the setting value to retrieve.</typeparam>
        /// <param name="legalEntity">The legal entity (company) to get the setting for.</param>
        /// <param name="prefix">The prefix for the setting key.</param>
        /// <param name="key">The setting key to retrieve.</param>
        /// <param name="default">The default value to return if the setting is not found.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the setting value of the specified type.</returns>
        Task<TPropType> GetSettingValueForCompanyAsync<TPropType>(LegalEntity legalEntity, string prefix, string key, TPropType @default = default(TPropType));

        /// <summary>
        /// Saves all the settings from the settings instance to the database for the given jurisdiction.
        /// </summary>
        /// <typeparam name="TSettings">The type of settings object to save.</typeparam>
        /// <param name="settings">Instance of the settings object.</param>
        /// <param name="jurisdictionId">Id of the jurisdiction that the settings are for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if the settings were saved successfully.</returns>
        Task<bool> SaveSettingsForJurisdictionAsync<TSettings>(TSettings settings, Guid jurisdictionId);

        /// <summary>
        /// Saves all the settings from the settings instance to the database for the given master client.
        /// </summary>
        /// <typeparam name="TSettings">The type of settings object to save.</typeparam>
        /// <param name="settings">Instance of the settings object.</param>
        /// <param name="masterClientId">Id of the master client that the settings are for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if the settings were saved successfully.</returns>
        Task<bool> SaveSettingsForMasterClientAsync<TSettings>(TSettings settings, Guid masterClientId);

        /// <summary>
        /// Saves all the settings from the settings instance to the database for the given legal entity.
        /// </summary>
        /// <typeparam name="TSettings">The type of settings object to save.</typeparam>
        /// <param name="settings">Instance of the settings object.</param>
        /// <param name="legalEntityId">Id of the legal entity that the settings are for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains true if the settings were saved successfully.</returns>
        Task<bool> SaveSettingsForCompanyAsync<TSettings>(TSettings settings, Guid legalEntityId);

        /// <summary>
        /// Reads all the settings for the settings instance from the database for the given jurisdiction.
        /// </summary>
        /// <typeparam name="TSettings">The type of settings object to read.</typeparam>
        /// <param name="jurisdictionId">Id of the jurisdiction that the settings are for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the settings object of the specified type.</returns>
        Task<TSettings> ReadSettingsForJurisdictionAsync<TSettings>(Guid jurisdictionId);

        /// <summary>
        /// Reads all the settings for the settings instance from the database for the given masterclient.
        /// </summary>
        /// <typeparam name="TSettings">The type of settings object to read.</typeparam>
        /// <param name="masterClientId">Id of the masterclient that the settings are for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the settings object of the specified type.</returns>
        Task<TSettings> ReadSettingsForMasterClientAsync<TSettings>(Guid masterClientId);

        /// <summary>
        /// Reads all the settings for the settings instance from the database for the given legal entity.
        /// </summary>
        /// <typeparam name="TSettings">The type of settings object to read.</typeparam>
        /// <param name="legalEntityId">Id of the legal entity that the settings are for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the settings object of the specified type.</returns>
        Task<TSettings> ReadSettingsForCompanyAsync<TSettings>(Guid legalEntityId);

        /// <summary>
        /// Gets a settings objects for the company by searching up to masterclient and jurisdiction until a value other than null found.
        /// </summary>
        /// <typeparam name="TSettings">The type of settings object to retrieve.</typeparam>
        /// <param name="legalEntityId">The unique identifier of the legal entity to get derived settings for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the derived settings object of the specified type.</returns>
        Task<TSettings> GetDerivedSettingsForCompany<TSettings>(Guid legalEntityId);

        /// <summary>
        /// Gets the STRLatePaymentFeeDTO for the jurisdiction for the given year.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction to get the settings for.</param>
        /// <param name="year">The year to get the settings for.</param>
        /// <returns>Collection of <see cref="STRLatePaymentFeeDTO"/>.</returns>
        Task<IReadOnlyCollection<STRLatePaymentFeeDTO>> GetSTRLatePaymentFeesForJurisdictionAsync(Guid jurisdictionId, int? year);

        /// <summary>
        /// Gets the settings for the numbering of invoices.
        /// </summary>
        /// <param name="jurisdictionId">The unique identifier of the jurisdiction.</param>
        /// <param name="moduleId">The optional unique identifier of the module. If null, gets the default jurisdiction setting.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the invoice numbering settings.</returns>
        Task<InvoiceNumberingSettingsDTO> GetInvoiceNumberingSettingsAsync(Guid jurisdictionId, Guid? moduleId);

        /// <summary>
        /// Sets the settings for the numbering of invoices.
        /// </summary>
        /// <param name="jurisdictionId">The unique identifier of the jurisdiction.</param>
        /// <param name="moduleId">The optional unique identifier of the module. If null, sets the default jurisdiction setting.</param>
        /// <param name="invoiceNumbering">The invoice numbering settings to save.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task SaveInvoiceNumberingSettingsAsync(Guid jurisdictionId, Guid? moduleId, InvoiceNumberingSettingsDTO invoiceNumbering);

        /// <summary>
        /// Gets the last generated invoicenumber for the jurisdiction and the optional module.
        /// </summary>
        /// <param name="jurisdictionId">The unique identifier of the jurisdiction.</param>
        /// <param name="moduleId">The optional unique identifier of the module. If null, gets the default jurisdiction setting.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the invoice number data.</returns>
        Task<InvoiceNumberData> GetLastInvoiceNumberAsync(Guid jurisdictionId, Guid? moduleId);

        /// <summary>
        /// Sets the last generated invoicenumber for the jurisdiction and the optional module.
        /// </summary>
        /// <param name="jurisdictionId">The unique identifier of the jurisdiction.</param>
        /// <param name="moduleId">The optional unique identifier of the module. If null, sets the default jurisdiction setting.</param>
        /// <param name="data">The invoice number data containing the last generated invoice number information.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task SetLastInvoiceNumberAsync(Guid jurisdictionId, Guid? moduleId, InvoiceNumberData data);
    }
}