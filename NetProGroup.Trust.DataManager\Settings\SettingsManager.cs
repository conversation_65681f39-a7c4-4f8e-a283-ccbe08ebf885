﻿// <copyright file="SettingsManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.EF.Repository.Interfaces;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Helpers;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Helpers;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Settings;
using NetProGroup.Trust.Domain.SettingsModels;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Settings;
using Newtonsoft.Json;
using System.Reflection;

namespace NetProGroup.Trust.DataManager.Settings
{
    /// <summary>
    /// Manager for settings related to Jurisdiction, MasterClient or Company.
    /// </summary>
    public class SettingsManager : ISettingsManager
    {
        private readonly IWorkContext _workContext;
        private readonly ISettingsRepository _settingsRepository;
        private readonly IMapper _mapper;

        private readonly IJurisdictionsRepository _jurisdictionRepository;
        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly ISystemAuditManager _systemAuditManager;

        private readonly Dictionary<string, List<Setting>> _cache = new Dictionary<string, List<Setting>>(StringComparer.InvariantCultureIgnoreCase);

        /// <summary>
        /// Initializes a new instance of the <see cref="SettingsManager"/> class.
        /// </summary>
        /// <param name="workContext">The current workcontext.</param>
        /// <param name="mapper">The mapper to use.</param>
        /// <param name="settingsRepository">Instance of settings repository.</param>
        /// <param name="jurisdictionRepository">The jurisdiction repository.</param>
        /// <param name="masterClientsRepository">The master client repository.</param>
        /// <param name="legalEntitiesRepository">The legal entities repository.</param>
        /// <param name="systemAuditManager">The audit manager.</param>
        public SettingsManager(IWorkContext workContext,
                               IMapper mapper,
                               ISettingsRepository settingsRepository,
                               IJurisdictionsRepository jurisdictionRepository,
                               IMasterClientsRepository masterClientsRepository,
                               ILegalEntitiesRepository legalEntitiesRepository,
                               ISystemAuditManager systemAuditManager)
        {
            _workContext = workContext;
            _mapper = mapper;
            _settingsRepository = settingsRepository;

            _jurisdictionRepository = jurisdictionRepository;
            _masterClientsRepository = masterClientsRepository;
            _legalEntitiesRepository = legalEntitiesRepository;
            _systemAuditManager = systemAuditManager;
        }

        /// <inheritdoc/>
        public async Task<IReadOnlyCollection<STRLatePaymentFeeDTO>> GetSTRLatePaymentFeesForJurisdictionAsync(Guid jurisdictionId, int? year)
        {
            var result = new List<STRLatePaymentFeeDTO>();

            var setting = await GetSettingForJurisdictionAsync(jurisdictionId, null, ConfigurationKeys.STRLatePaymentFeeConfiguration);

            if (setting == null)
            {
                // result.Add(new STRLatePaymentFeeDTO { FinancialYear = year, Amount = 0, CurrencyCode = "USD", StartAt = new DateTime(year, 1, 1), EndAt = new DateTime(year, 12, 31) });
            }
            else
            {
                var configuration = JsonConvert.DeserializeObject<LatePaymentFeeConfiguration>(setting.Value);
                var fees = configuration.LatePaymentFees.Where(lpf => !year.HasValue || lpf.FinancialYear == year).OrderBy(lpf => lpf.FinancialYear).Select(lpf => _mapper.Map<STRLatePaymentFeeDTO>(lpf));
                result.AddRange(fees);
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task<InvoiceSettingsDTO> GetInvoiceSettingsForCompanyAsync(Guid companyId)
        {
            var result = new InvoiceSettingsDTO();

            var legalEntity = await _legalEntitiesRepository.GetByIdAsync(companyId, q => q.Include(le => le.MasterClient)
                                                                                           .Include(mc => mc.Jurisdiction));

            // TODO: Implement the application of the entity specific invoice settings.
            return result;
        }

        /// <inheritdoc/>
        public async Task<bool> SaveSettingsForJurisdictionAsync<TSettings>(TSettings settings, Guid jurisdictionId)
        {
            ValidateJurisdictionSettings(settings);

            if (settings is STRLatePaymentFeeDTO)
            {
                return await SaveSTRLatePaymentFeeAsync(settings as STRLatePaymentFeeDTO, jurisdictionId);
            }
            else if (settings is STRLatePaymentFeeSettingsDTO)
            {
                return await SaveSTRLatePaymentFeeAsync(settings as STRLatePaymentFeeSettingsDTO, jurisdictionId);
            }
            else
            {
                var settingsFromDb = await GetJurisdictionSettingsAsync(jurisdictionId);
                return await SaveSettingsForAsync(settings, settingsFromDb, jurisdictionId, null, null);
            }
        }

        /// <inheritdoc/>
        public async Task<bool> SaveSettingsForMasterClientAsync<TSettings>(TSettings settings, Guid masterClientId)
        {
            var settingsFromDb = await GetMasterClientSettingsAsync(masterClientId);

            return await SaveSettingsForAsync(settings, settingsFromDb, null, masterClientId, null);
        }

        /// <inheritdoc/>
        public async Task<bool> SaveSettingsForCompanyAsync<TSettings>(TSettings settings, Guid legalEntityId)
        {
            ValidateCompanySettings(settings);

            var settingsFromDb = await GetLegalEntitySettingsAsync(legalEntityId);

            return await SaveSettingsForAsync(settings, settingsFromDb, null, null, legalEntityId);
        }

        /// <inheritdoc/>
        public async Task<TSettings> ReadSettingsForJurisdictionAsync<TSettings>(Guid jurisdictionId)
        {
            var settingsFromDb = await GetJurisdictionSettingsAsync(jurisdictionId);

            return ReadSettings<TSettings>(settingsFromDb);
        }

        /// <inheritdoc/>
        public async Task<TSettings> ReadSettingsForMasterClientAsync<TSettings>(Guid masterClientId)
        {
            var settingsFromDb = await GetMasterClientSettingsAsync(masterClientId);

            return ReadSettings<TSettings>(settingsFromDb);
        }

        /// <inheritdoc/>
        public async Task<TSettings> ReadSettingsForCompanyAsync<TSettings>(Guid legalEntityId)
        {
            var settingsFromDb = await GetLegalEntitySettingsAsync(legalEntityId);

            return ReadSettings<TSettings>(settingsFromDb);
        }

        /// <summary>
        /// Gets the company level settings for the specified company/legal entity.
        /// </summary>
        /// <typeparam name="TSettings">The settings type to retrieve.</typeparam>
        /// <param name="legalEntityId">The legal entity/company id.</param>
        /// <returns>The company settings.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("StyleCop.CSharp.OrderingRules", "SA1202:Elements should be ordered by access", Justification = "Not changing the order of this class...")]
        public async Task<TSettings> GetDerivedSettingsForCompany<TSettings>(Guid legalEntityId)
        {
            var legalEntity = await _legalEntitiesRepository.GetByIdAsync(legalEntityId, q => q.Include(le => le.MasterClient)
                                                                                               .Include(mc => mc.Jurisdiction));

            TSettings result = (TSettings)Activator.CreateInstance(typeof(TSettings));

            PropertyInfo[] properties = typeof(TSettings).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                var attribute = property.GetCustomAttribute(typeof(SettingKeyAttribute));
                if (attribute != null)
                {
                    var settingKey = ((SettingKeyAttribute)attribute).Key;

                    var value = await GetSettingValueForCompanyAsync<string>(legalEntity, null, settingKey, null);

                    if (value != null)
                    {
                        property.SetValue(result, Converter.To(value, property.PropertyType));
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Gets the value of a setting for the given legal entity.
        /// </summary>
        /// <typeparam name="TPropType">The type of the value to return.</typeparam>
        /// <param name="legalEntity">The legal entity to get the settings for.</param>
        /// <param name="prefix">Use the prefix to preload and cache multiple settings.</param>
        /// <param name="key">Key of the setting to get.</param>
        /// <param name="default">Option default value to return if setting does not exist.</param>
        /// <returns>The value of the setting or the default as TPropType.</returns>
        public async Task<TPropType> GetSettingValueForCompanyAsync<TPropType>(LegalEntity legalEntity, string prefix, string key, TPropType @default = default(TPropType))
        {
            Check.NotNull(legalEntity, nameof(legalEntity));

            var setting = await GetSettingForLegalEntityAsync(legalEntity.Id, prefix, key);
            if (setting == null)
            {
                setting = await GetSettingForMasterClientAsync(legalEntity.MasterClientId, prefix, key);
            }

            if (setting == null)
            {
                setting = await GetSettingForJurisdictionAsync(legalEntity.JurisdictionId.Value, prefix, key);
            }

            if (setting == null || setting.Value == null)
            {
                return @default;
            }

            return Converter.To<TPropType>(setting.Value);
        }

        /// <inheritdoc/>
        public async Task<InvoiceNumberingSettingsDTO> GetInvoiceNumberingSettingsAsync(Guid jurisdictionId, Guid? moduleId)
        {
            var result = new InvoiceNumberingSettingsDTO();

            Setting setting = null;

            var key = ConfigurationKeys.InvoiceNumberingConfiguration;
            var keyForModule = $"{ConfigurationKeys.InvoiceNumberingConfiguration}.module.{moduleId}";

            // Try to get configuration for specific module
            if (moduleId.HasValue)
            {
                setting = await GetSettingForJurisdictionAsync(jurisdictionId, null, keyForModule);
            }

            // If no configuration for module (or no module specified) then use default for jurisdiction
            if (setting == null)
            {
                moduleId = null;
                setting = await GetSettingForJurisdictionAsync(jurisdictionId, null, key);
            }

            // Still no configuration?
            if (setting == null)
            {
                result = new InvoiceNumberingSettingsDTO { JurisdictionId = jurisdictionId, ModuleId = moduleId };
            }
            else
            {
                var configuration = JsonConvert.DeserializeObject<InvoiceNumberingConfiguration>(setting.Value);
                result = _mapper.Map<InvoiceNumberingSettingsDTO>(configuration);
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task SetLastInvoiceNumberAsync(Guid jurisdictionId, Guid? moduleId, InvoiceNumberData data)
        {
            ArgumentNullException.ThrowIfNull(data, nameof(data));

            Setting setting = null;

            var key = ConfigurationKeys.LastInvoiceNumber;
            if (moduleId.HasValue)
            {
                key += $".module.{moduleId}";
                setting = await GetSettingForJurisdictionAsync(jurisdictionId, null, key);
            }
            else
            {
                setting = await GetSettingForJurisdictionAsync(jurisdictionId, null, key);
            }

            if (setting == null)
            {
                setting = new Setting(Guid.NewGuid(), key, key, jurisdictionId, null, null);
                await _settingsRepository.InsertAsync(setting);
            }

            setting.Value = $"{data.InvoiceNumber}:{data.RangeIdentifier}";
            await _settingsRepository.UpdateAsync(setting);
            await _settingsRepository.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task<InvoiceNumberData> GetLastInvoiceNumberAsync(Guid jurisdictionId, Guid? moduleId)
        {
            var result = new InvoiceNumberData();

            Setting setting = null;

            var key = ConfigurationKeys.LastInvoiceNumber;
            var keyForModule = $"{ConfigurationKeys.LastInvoiceNumber}.module.{moduleId}";
            if (moduleId.HasValue)
            {
                setting = await GetSettingForJurisdictionAsync(jurisdictionId, null, keyForModule);
            }

            if (setting == null)
            {
                setting = await GetSettingForJurisdictionAsync(jurisdictionId, null, key);
            }

            if (setting != null && !string.IsNullOrEmpty(setting.Value))
            {
                var colonIx = setting.Value.IndexOf(':', StringComparison.InvariantCultureIgnoreCase);
                if (colonIx > -1)
                {
                    result.InvoiceNumber = int.Parse(setting.Value.Substring(0, colonIx));
                    result.RangeIdentifier = setting.Value.Substring(colonIx + 1);
                }
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task SaveInvoiceNumberingSettingsAsync(Guid jurisdictionId, Guid? moduleId, InvoiceNumberingSettingsDTO invoiceNumbering)
        {
            Setting setting = null;

            var key = ConfigurationKeys.InvoiceNumberingConfiguration;
            var keyForModule = $"{ConfigurationKeys.InvoiceNumberingConfiguration}.module.{moduleId}";
            if (moduleId.HasValue)
            {
                setting = await GetSettingForJurisdictionAsync(jurisdictionId, null, keyForModule);
                if (setting == null)
                {
                    setting = new Setting(Guid.NewGuid(), keyForModule, keyForModule, jurisdictionId);
                    await _settingsRepository.InsertAsync(setting);
                }
            }
            else
            {
                setting = await GetSettingForJurisdictionAsync(jurisdictionId, null, key);
                if (setting == null)
                {
                    setting = new Setting(Guid.NewGuid(), key, key, jurisdictionId);
                    await _settingsRepository.InsertAsync(setting);
                }
            }

            InvoiceNumberingConfiguration invoiceNumberingConfiguration = null;

            if (string.IsNullOrEmpty(setting.Value))
            {
                invoiceNumberingConfiguration = new InvoiceNumberingConfiguration
                {
                    JurisdictionId = jurisdictionId,
                    ModuleId = moduleId,
                };
            }
            else
            {
                invoiceNumberingConfiguration = JsonConvert.DeserializeObject<InvoiceNumberingConfiguration>(setting.Value);
            }

            // Map settings
            _mapper.Map(invoiceNumbering, invoiceNumberingConfiguration);

            setting.Value = JsonConvert.SerializeObject(invoiceNumberingConfiguration);

            await _settingsRepository.SaveChangesAsync();

            _cache.Clear();
        }

        /// <summary>
        /// Gets the value of a setting for the given masterclient.
        /// </summary>
        /// <typeparam name="TPropType">The type of the value to return.</typeparam>
        /// <param name="masterClient">The masterclient to get the settings for.</param>
        /// <param name="prefix">Use the prefix to preload and cache multiple settings.</param>
        /// <param name="key">Key of the setting to get.</param>
        /// <param name="default">Option default value to return if setting does not exist.</param>
        /// <returns>The value of the setting or the default as TPropType.</returns>
        public async Task<TPropType> GetSettingValueForMasterClientAsync<TPropType>(MasterClient masterClient, string prefix, string key, TPropType @default = default(TPropType))
        {
            Check.NotNull(masterClient, nameof(masterClient));

            var setting = await GetSettingForMasterClientAsync(masterClient.Id, prefix, key);

            if (setting == null || setting.Value == null)
            {
                return @default;
            }

            return Converter.To<TPropType>(setting.Value);
        }

        /// <summary>
        /// Gets the value of a setting for the given jurisdiction.
        /// </summary>
        /// <typeparam name="TPropType">The type of the value to return.</typeparam>
        /// <param name="jurisdiction">The jurisdiction to get the settings for.</param>
        /// <param name="prefix">Use the prefix to preload and cache multiple settings.</param>
        /// <param name="key">Key of the setting to get.</param>
        /// <param name="default">Option default value to return if setting does not exist.</param>
        /// <returns>The value of the setting or the default as TPropType.</returns>
        public async Task<TPropType> GetSettingValueForJurisdictionAsync<TPropType>(Jurisdiction jurisdiction, string prefix, string key, TPropType @default = default(TPropType))
        {
            Check.NotNull(jurisdiction, nameof(jurisdiction));

            var setting = await GetSettingForJurisdictionAsync(jurisdiction.Id, prefix, key);

            if (setting == null || setting.Value == null)
            {
                return @default;
            }

            return Converter.To<TPropType>(setting.Value);
        }

        /// <summary>
        /// Shared method to read the settings from the database to a settinsg object using the SettingsKey attribute.
        /// </summary>
        /// <typeparam name="TSettings">Type of the settings object that need to be populated.</typeparam>
        /// <param name="dbSettings">All settings from the database to get the values from.</param>
        /// <returns>An isntance of TSettings.</returns>
        private static TSettings ReadSettings<TSettings>(IReadOnlyCollection<Setting> dbSettings)
        {
            TSettings result = (TSettings)Activator.CreateInstance(typeof(TSettings));

            PropertyInfo[] properties = typeof(TSettings).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                var attribute = property.GetCustomAttribute(typeof(SettingKeyAttribute));
                if (attribute != null)
                {
                    var settingKey = ((SettingKeyAttribute)attribute).Key;

                    var settingFromDb = dbSettings.FirstOrDefault(s => s.Key.Equals(settingKey, StringComparison.OrdinalIgnoreCase));

                    if (settingFromDb != null)
                    {
                        property.SetValue(result, Converter.To(settingFromDb.Value, property.PropertyType));
                    }
                }
            }

            return result;
        }

        private async Task<bool> SaveSettingsForAsync<TSettings>(TSettings settings, IReadOnlyCollection<Setting> dbSettings, Guid? jurisdictionId, Guid? masterClientId, Guid? legalEntityId)
        {
            bool changed = false;

            PropertyInfo[] properties = settings.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                var attribute = property.GetCustomAttribute(typeof(SettingKeyAttribute));
                if (attribute != null)
                {
                    var settingKey = ((SettingKeyAttribute)attribute).Key;

                    var settingFromDb = dbSettings.FirstOrDefault(s => s.Key.Equals(settingKey, StringComparison.OrdinalIgnoreCase));

                    var value = property.GetValue(settings);

                    if (value == null && settingFromDb == null)
                    {
                        // skip
                    }
                    else if (settingFromDb != null && value == null)
                    {
                        await LogChangedSettingsAsync(settingKey, null, null, true, jurisdictionId, masterClientId, legalEntityId);
                        await _settingsRepository.DeleteAsync(settingFromDb);
                        changed = true;
                    }
                    else
                    {
                        if (settingFromDb == null)
                        {
                            settingFromDb = new Setting(Guid.NewGuid(), settingKey, settingKey, jurisdictionId, masterClientId, legalEntityId)
                            {
                                Value = Converter.To<string>(value)
                            };
                            await LogChangedSettingsAsync(settingKey, null, settingFromDb.Value, false, jurisdictionId, masterClientId, legalEntityId);
                            await _settingsRepository.InsertAsync(settingFromDb);
                            changed = true;
                        }
                        else
                        {
                            var newValue = Converter.To<string>(value);
                            if (settingFromDb.Value != newValue)
                            {
                                await LogChangedSettingsAsync(settingKey, settingFromDb.Value, newValue, false, jurisdictionId, masterClientId, legalEntityId);
                                settingFromDb.Value = newValue;
                                changed = true;
                            }
                        }
                    }
                }
            }

            await _settingsRepository.SaveChangesAsync();

            _cache.Clear();

            return changed;
        }

        private async Task LogChangedSettingsAsync(string key, string oldValue, string newValue, bool deleted, Guid? jurisdictionId, Guid? masterClientId, Guid? legalEntityId)
        {
            var shortText = string.Empty;
            var text = string.Empty;
            var activtyType = string.Empty;

            switch (key)
            {
                case ConfigurationKeys.STRSubmissionLatePaymentFeeExempt:
                    {
                        activtyType = ActivityLogActivityTypes.LatePaymentFeeExemptUpdated;
                        if (deleted)
                        {
                            shortText = "Late payment exemption is deleted";
                            text = shortText;
                        }
                        else if (oldValue == null)
                        {
                            shortText = "Late payment exemption is set";
                            text = $"Late payment exemption is set to '{newValue}'";
                        }
                        else
                        {
                            shortText = "Late payment exemption changed";
                            text = $"Late payment exemption changed from '{oldValue}' to '{newValue}'";
                        }

                        break;
                    }

                case ConfigurationKeys.STRSubmissionFee:
                    {
                        activtyType = ActivityLogActivityTypes.SubmissionFeeUpdated;
                        if (deleted)
                        {
                            shortText = "Submission fee is deleted";
                            text = shortText;
                        }
                        else if (oldValue == null)
                        {
                            shortText = "Submission fee is set";
                            text = $"Submission fee is set to '{newValue}'";
                        }
                        else
                        {
                            shortText = "Submission fee changed";
                            text = $"Submission fee changed from '{oldValue}' to '{newValue}'";
                        }

                        break;
                    }
            }

            // The entity to log for
            IEntity<Guid> entity = null;
            if (legalEntityId.HasValue)
            {
                entity = new LegalEntity(legalEntityId.Value);
            }
            else if (masterClientId.HasValue)
            {
                entity = new MasterClient(masterClientId.Value);
            }
            else if (jurisdictionId.HasValue)
            {
                entity = new Jurisdiction(jurisdictionId.Value);
            }

            // Log it
            if (!string.IsNullOrWhiteSpace(shortText) && entity != null)
            {
                await _systemAuditManager.AddActivityLogAsync(entity, activtyType, shortText, text);
            }
        }

        private async Task<Setting> GetSettingForLegalEntityAsync(Guid legalEntityId, string prefix, string key)
        {
            return (await GetLegalEntitySettingsAsync(legalEntityId, prefix)).FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
        }

        private async Task<Setting> GetSettingForMasterClientAsync(Guid masterClientId, string prefix, string key)
        {
            return (await GetMasterClientSettingsAsync(masterClientId, prefix)).FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
        }

        private async Task<Setting> GetSettingForJurisdictionAsync(Guid jurisdictionId, string prefix, string key)
        {
            return (await GetJurisdictionSettingsAsync(jurisdictionId, prefix)).FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Gets the complete list of settings for the given jurisdiction. Uses caching in current instance.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to get the settings for.</param>
        /// <param name="prefix">(Optional) setting prefix to check.</param>
        /// <returns>Collection of settings.</returns>
        private async Task<IReadOnlyCollection<Setting>> GetJurisdictionSettingsAsync(Guid jurisdictionId, string prefix = null)
        {
            var key = $"jurisdiction_{jurisdictionId}_{prefix}";
            if (!_cache.TryGetValue(key, out List<Setting> value))
            {
                value = new List<Setting>();
                _cache.Add(key, value);
                _cache[key].AddRange(await _settingsRepository.FindByConditionAsync(s => s.JurisdictionId == jurisdictionId &&
                                                                                         s.MasterClientId == null &&
                                                                                         s.LegalEntityId == null &&
                                                                                         (prefix == null || s.Key.StartsWith(prefix))));
            }

            return value;
        }

        /// <summary>
        /// Gets the complete list of settings for the given masterclient. Uses caching in current instance.
        /// </summary>
        /// <param name="masterClientId">Id of the masterclient to get the settings for.</param>
        /// <param name="prefix">Prefix to use for only getting particular settings.</param>
        /// <returns>Collection of settings.</returns>
        private async Task<IReadOnlyCollection<Setting>> GetMasterClientSettingsAsync(Guid masterClientId, string prefix = null)
        {
            var key = $"masterclient_{masterClientId}_{prefix}";
            if (!_cache.TryGetValue(key, out List<Setting> value))
            {
                value = new List<Setting>();
                _cache.Add(key, value);
                _cache[key].AddRange(await _settingsRepository.FindByConditionAsync(s => s.JurisdictionId == null &&
                                                                                         s.MasterClientId == masterClientId &&
                                                                                         s.LegalEntityId == null &&
                                                                                         (prefix == null || s.Key.StartsWith(prefix))));
            }

            return value;
        }

        /// <summary>
        /// Gets the complete list of settings for the given legalentity. Uses caching in current instance.
        /// </summary>
        /// <param name="legalEntityId">Id of the legalentity to get the settings for.</param>
        /// <param name="prefix">Prefix to use for only getting particular settings.</param>
        /// <returns>Collection of settings.</returns>
        private async Task<IReadOnlyCollection<Setting>> GetLegalEntitySettingsAsync(Guid legalEntityId, string prefix = null)
        {
            var key = $"legalentity_{legalEntityId}_{prefix}";
            if (!_cache.TryGetValue(key, out List<Setting> value))
            {
                value = new List<Setting>();
                _cache.Add(key, value);
                _cache[key].AddRange(await _settingsRepository.FindByConditionAsync(s => s.JurisdictionId == null &&
                                                                                         s.MasterClientId == null &&
                                                                                         s.LegalEntityId == legalEntityId &&
                                                                                         (prefix == null || s.Key.StartsWith(prefix))));
            }

            return value;
        }

        private async Task<bool> SaveSTRLatePaymentFeeAsync(STRLatePaymentFeeSettingsDTO model, Guid? jurisdictionId)
        {
            bool result = false;
            foreach (var item in model.STRLatePaymentFees)
            {
                if (await SaveSTRLatePaymentFeeAsync(item, jurisdictionId))
                {
                    result = true;
                }
            }

            return result;
        }

        /// <summary>
        /// Saves the data for a LatePaymentFee model to the settings.
        /// </summary>
        /// <param name="model">The model with the Late Payment Fee for Simple Tax Return.</param>
        /// <param name="jurisdictionId">The id of the jurisdiction.</param>
        /// <returns>True if configuration changed.</returns>
        private async Task<bool> SaveSTRLatePaymentFeeAsync(STRLatePaymentFeeDTO model, Guid? jurisdictionId)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            var maxYear = DateTime.Today.Year + 5;
            if (model.FinancialYear.HasValue && (model.FinancialYear.Value < 2000 || model.FinancialYear.Value > maxYear))
            {
                throw new ArgumentException($"Year for 'Late Payment Fee' must be between 2000 and {maxYear}");
            }

            if (model.FinancialYear.HasValue && model.Amount < 0)
            {
                throw new ArgumentException("Amount for 'Late Payment Fee' can not be negative");
            }

            LatePaymentFeeConfiguration latePaymentFeeConfiguration;

            // Make sure we have the setting, existing or new
            var setting = await PrepareSettingAsync(ConfigurationKeys.STRLatePaymentFeeConfiguration, jurisdictionId, null, null);

            // If no setting and we should delete (year = null) then get out
            if (setting == null && !model.FinancialYear.HasValue)
            {
                return true;
            }

            if (string.IsNullOrEmpty(setting.Value))
            {
                latePaymentFeeConfiguration = new LatePaymentFeeConfiguration();
            }
            else
            {
                latePaymentFeeConfiguration = JsonConvert.DeserializeObject<LatePaymentFeeConfiguration>(setting.Value);
            }

            LatePaymentFee latePaymentFee = null;

            if (model.Id.HasValue && model.Id.Value != Guid.Empty)
            {
                latePaymentFee = latePaymentFeeConfiguration.LatePaymentFees.FirstOrDefault(lpf => lpf.Id == model.Id.Value);

                if (latePaymentFee == null)
                {
                    throw new ConstraintException($"LatePaymentFee with id '{model.Id.Value}' does not exist");
                }

                if (!model.FinancialYear.HasValue)
                {
                    latePaymentFeeConfiguration.LatePaymentFees.Remove(latePaymentFee);
                }
            }
            else
            {
                if (model.FinancialYear.HasValue)
                {
                    latePaymentFee = new LatePaymentFee { Id = Guid.NewGuid() };
                    latePaymentFeeConfiguration.LatePaymentFees.Add(latePaymentFee);
                }
            }

            if (latePaymentFee != null && model.FinancialYear.HasValue)
            {
                latePaymentFee.FinancialYear = model.FinancialYear.Value;
                latePaymentFee.StartAt = model.StartAt;
                latePaymentFee.EndAt = model.EndAt;
                latePaymentFee.Amount = model.Amount;
                latePaymentFee.CurrencyCode = model.CurrencyCode;
                latePaymentFee.Description = model.Description;
                latePaymentFee.InvoiceText = model.InvoiceText;
            }

            // Check for overlap and valid start/end
            if (latePaymentFee != null)
            {
                ValidateSTRLatePaymentFeeConfiguration(latePaymentFeeConfiguration, latePaymentFee.FinancialYear);
            }

            var oldValue = setting.Value;

            setting.Value = JsonConvert.SerializeObject(latePaymentFeeConfiguration);

            await _settingsRepository.SaveChangesAsync();

            _cache.Clear();

            return setting.Value != oldValue;
        }

        private async Task<Setting> PrepareSettingAsync(string key, Guid? jurisdictionId, Guid? masterClientId, Guid? legalEntityId)
        {
            Setting setting = null;

            if (legalEntityId.HasValue)
            {
                setting = await GetSettingForJurisdictionAsync(legalEntityId.Value, null, key);
                if (setting == null)
                {
                    setting = new Setting(Guid.NewGuid(), key, key, null, null, legalEntityId);
                    await _settingsRepository.InsertAsync(setting, saveChanges: false);
                }
            }
            else if (masterClientId.HasValue)
            {
                setting = await GetSettingForMasterClientAsync(masterClientId.Value, null, key);
                if (setting == null)
                {
                    setting = new Setting(Guid.NewGuid(), key, key, null, masterClientId, null);
                    await _settingsRepository.InsertAsync(setting, saveChanges: false);
                }
            }
            else if (jurisdictionId.HasValue)
            {
                setting = await GetSettingForJurisdictionAsync(jurisdictionId.Value, null, key);
                if (setting == null)
                {
                    setting = new Setting(Guid.NewGuid(), key, key, jurisdictionId, null, null);
                    await _settingsRepository.InsertAsync(setting, saveChanges: false);
                }
            }

            return setting;
        }

        #region Validation

#pragma warning disable SA1204 // Static elements should appear before instance elements
        private static void ValidateCompanySettings<TSettings>(TSettings settings)
        {
            if (settings is FeeSettingsDTO feeSettings)
            {
                if (feeSettings.STRSubmissionFee.HasValue && feeSettings.STRSubmissionFee.Value < 0)
                {
                    throw new ArgumentException($"Amount for '{nameof(feeSettings.STRSubmissionFee)}' can not be negative");
                }

                if (feeSettings.BFRSubmissionFee.HasValue && feeSettings.BFRSubmissionFee.Value < 0)
                {
                    throw new ArgumentException($"Amount for '{nameof(feeSettings.BFRSubmissionFee)}' can not be negative");
                }
            }
        }

        private static void ValidateJurisdictionSettings<TSettings>(TSettings settings)
        {
            if (settings is FeeSettingsDTO feeSettings)
            {
                if (feeSettings.STRSubmissionFee.HasValue && feeSettings.STRSubmissionFee.Value < 0)
                {
                    throw new ArgumentException($"Amount for '{nameof(feeSettings.STRSubmissionFee)}' can not be negative");
                }

                if (feeSettings.BFRSubmissionFee.HasValue && feeSettings.BFRSubmissionFee.Value < 0)
                {
                    throw new ArgumentException($"Amount for '{nameof(feeSettings.BFRSubmissionFee)}' can not be negative");
                }
            }
        }

        /// <summary>
        /// Validates the LatePaymentFeeConfiguration.
        /// </summary>
        /// <remarks>
        /// Checks for:
        ///   - Invalid periods (Start later than End).
        ///   - Overlapping period in same financial year.
        /// </remarks>
        /// <param name="configuration">The LatePaymentFeeConfiguration.</param>
        /// <param name="financialYear">The financail year to check.</param>
        /// <exception cref="ArgumentException">Exception in case of invalid data.</exception>
        public static void ValidateSTRLatePaymentFeeConfiguration(LatePaymentFeeConfiguration configuration, int financialYear)
        {
            ArgumentNullException.ThrowIfNull(configuration, nameof(configuration));

            var feesForFinancialYear = configuration.LatePaymentFees.Where(lpf => lpf.FinancialYear == financialYear).ToArray();

            foreach (var fee in feesForFinancialYear)
            {
                if (fee.StartAt.Date > fee.EndAt.Date)
                {
                    throw new ConstraintException($"Startdate must be before enddate for period '{fee.StartAt:yyyy-MM-dd} / {fee.EndAt:yyyy-MM-dd}' in financial year {financialYear}");
                }

                foreach (var otherFee in feesForFinancialYear.Where(f => f.Id != fee.Id))
                {
                    if (DateTimeHelper.HasOverlap(fee.StartAt.Date, fee.EndAt.Date, otherFee.StartAt, otherFee.EndAt))
                    {
                        throw new ConstraintException($"There is an overlap for period '{fee.StartAt:yyyy-MM-dd} / {fee.EndAt:yyyy-MM-dd}' with '{otherFee.StartAt:yyyy-MM-dd} / {otherFee.EndAt:yyyy-MM-dd}' in financial year {financialYear}");
                    }
                }
            }
        }

#pragma warning restore SA1204 // Static elements should appear before instance elements

        #endregion
    }
}
