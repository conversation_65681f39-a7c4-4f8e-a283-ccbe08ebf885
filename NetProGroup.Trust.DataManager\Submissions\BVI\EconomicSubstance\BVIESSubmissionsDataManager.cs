﻿// <copyright file="BVIESSubmissionsDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Forms;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Forms.Forms;

namespace NetProGroup.Trust.DataManager.Submissions.BVI.EconomicSubstance
{
    /// <summary>
    /// Manager for handling Economic Substance submissions for BVI.
    /// </summary>
    public class BVIESSubmissionsDataManager : SubmissionsDataManagerBase, IBVIESSubmissionsDataManager
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BVIESSubmissionsDataManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="serviceProvider">Service provider instance.</param>
        public BVIESSubmissionsDataManager(ILogger<BVIESSubmissionsDataManager> logger,
                                           IServiceProvider serviceProvider)
            : base(logger, serviceProvider)
        {
        }

        /// <inheritdoc/>
        public override async Task<SubmissionDTO> StartSubmissionAsync(StartSubmissionDTO model, Jurisdiction jurisdiction, Module module)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));
            ArgumentNullException.ThrowIfNull(jurisdiction, nameof(jurisdiction));
            ArgumentNullException.ThrowIfNull(module, nameof(module));

            // Check if there is an active submission for the given company and module
            var exists = await SubmissionsRepository.AnyByConditionAsync(
                s => s.LegalEntityId == model.LegalEntityId &&
                     s.ModuleId == model.ModuleId &&
                    (s.Status != SubmissionStatus.Submitted && s.Status != SubmissionStatus.Temporal));

            if (exists)
            {
                throw new ConstraintException(ApplicationErrors.SUBMISSION_ALREADY_EXISTS.ToErrorCode(), $"An active submission for this company/module already exists.");
            }

            // Get the template
            var formTemplate = await FormTemplatesRepository.FindByConditionAsync(
                ft => ft.JurisdictionId == jurisdiction.Id &&
                      ft.ModuleId == model.ModuleId,
                q => q.Include(ft => ft.FormTemplateVersions)
                      .Include(ft => ft.Module));

            // Sanity
            switch (formTemplate.Count())
            {
                case 0:
                    {
                        throw new BadRequestException($"No templates found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}'");
                    }

                case 1: break;
                default:
                    {
                        throw new ConstraintException($"Multiple templates found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}'");
                    }
            }

            // Get the default version for the formTemplate
            var templateVersion = formTemplate.First().FormTemplateVersions.First();

            if (templateVersion == null)
            {
                throw new ConstraintException($"No version found for submission for module '{module.Name}' in jurisdiction '{jurisdiction.Name}' in template '{formTemplate.First().Name}'.");
            }

            // Check if there is a temporal submission to avoid creating a new one.
            var temporalSubmission = await SubmissionsRepository.FindFirstOrDefaultByConditionAsync(
                s => s.Status == SubmissionStatus.Temporal &&
                     s.LegalEntityId == model.LegalEntityId &&
                     s.ModuleId == model.ModuleId);

            if (temporalSubmission != null)
            {
                var submissionData = await GetSubmissionAsync(temporalSubmission.Id, true);
                return Mapper.Map<SubmissionDTO>(submissionData);
            }

            return await StartSubmission(model, templateVersion, SubmissionStatus.Temporal, async (submission) =>
            {
                await CalculateSubmissionDatesAsync(submission);
            });
        }

        /// <inheritdoc/>
        public override async Task UpdateSubmissionGeneralInformationAsync(Guid submissionId, UpdateSubmissionInformationDTO model, bool saveChanges = false)
        {
            await Task.CompletedTask;
            throw new BadRequestException(ApplicationErrors.SUBMISSION_OPERATION_NOT_ALLOWED.ToErrorCode(), "General information can not be updated");
        }

        /// <inheritdoc/>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Ignore")]
        public override async Task<SubmissionDTO> SubmitSubmissionAsync(SubmitSubmissionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            // Check for the submission entity.
            var submission = await SubmissionsRepository.CheckSubmissionByIdAsync(model.SubmissionId);

            // Get the last revision for the submission
            var formDocumentRevision = await FormDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.FormDocumentId == submission.FormDocumentId,
                                                                                                                q => q.Include(fdr => fdr.FormDocument).ThenInclude(f => f.Attributes)
                                                                                                                      .OrderByDescending(fdr => fdr.Revision));

            ValidateSubmissionForSubmit(submission, formDocumentRevision, model.ScheduleSubmit);

            if (model.ScheduleSubmit)
            {
                await DoScheduleSubmitAsync(submission, formDocumentRevision);
            }
            else
            {
                // Do the actual submit
                await DoSubmitAsync(submission, formDocumentRevision);
            }

            SetLastActivity(submission);

            // Return the updated submission with the latest document (revision)
            return await GetSubmissionAsync(model.SubmissionId, formDocumentRevision.Id, true);
        }

        /// <inheritdoc/>
        public override async Task<SubmissionDTO> ReopenSubmissionAsync(ReopenSubmissionDTO model)
        {
            ArgumentNullException.ThrowIfNull(model, nameof(model));

            // Check for the submission entity.
            var submission = await SubmissionsRepository.CheckSubmissionByIdAsync(model.SubmissionId);

            // Can only re-open when finalized
            if (submission.Status != DomainShared.Enums.SubmissionStatus.Submitted)
            {
                throw new ConstraintException(ApplicationErrors.SUBMISSION_INCORRECT_STATUS.ToErrorCode(), $"Submission has status '{submission.Status}");
            }

            // Get the last revision for the submission
            var formDocumentRevision = await FormDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.FormDocumentId == submission.FormDocumentId,
                                                                                                                 q => q.Include(fdr => fdr.FormDocument)
                                                                                                                       .OrderByDescending(fdr => fdr.Revision));

            if (formDocumentRevision == null)
            {
                throw new NotFoundException(ApplicationErrors.FORMDOCUMENTREVISION_NOT_FOUND.ToErrorCode(), "No last revision found");
            }

            if (formDocumentRevision.Status != DomainShared.Enums.FormDocumentRevisionStatus.Finalized)
            {
                throw new ConstraintException(ApplicationErrors.FORMDOCUMENTREVISION_INCORRECT_STATUS.ToErrorCode(), $"Last revision has status '{formDocumentRevision.Status}");
            }

            if (formDocumentRevision.FormDocument.Status != DomainShared.Enums.FormDocumentStatus.Finalized)
            {
                throw new ConstraintException(ApplicationErrors.FORMDOCUMENT_INCORRECT_STATUS.ToErrorCode(), $"Document has status '{formDocumentRevision.FormDocument.Status}");
            }

            // Create a new revision based on the last revision
            var newFormDocumentRevision = new FormDocumentRevision
            {
                Status = DomainShared.Enums.FormDocumentRevisionStatus.Draft,
                DataAsJson = formDocumentRevision.DataAsJson,
                Revision = formDocumentRevision.Revision + 1,
            };

            formDocumentRevision.FormDocument.FormDocumentRevisions.Add(newFormDocumentRevision);

            // Set the status of the submission and the formdocument to 'revision'.
            formDocumentRevision.FormDocument.Status = DomainShared.Enums.FormDocumentStatus.Revision;
            submission.Status = DomainShared.Enums.SubmissionStatus.Revision;

            await SystemAuditManager.AddActivityLogAsync(submission,
                                                          ActivityLogActivityTypes.SubmissionReopened,
                                                          "Submission reopened",
                                                          $"Submission '{submission.Name}' reopened");

            await FormDocumentRevisionsRepository.SaveChangesAsync();

            // Return the updated submission with the newly created revision
            return await GetSubmissionAsync(model.SubmissionId, newFormDocumentRevision.Id, true);
        }

        /// <inheritdoc/>
        public override async Task<List<ListSubmissionDTO>> ListScheduledSubmissionsAsync(DateTime? scheduledDate)
        {
            var submissions = await SubmissionsRepository.FindByConditionAsync(
                s => s.Status == SubmissionStatus.Scheduled &&
                     (!scheduledDate.HasValue || s.EndsAt < scheduledDate),
                options: q => q
                              .Include(s => s.LegalEntity.MasterClient)
                              .Include(s => s.Invoice.PaymentInvoices).ThenInclude(pi => pi.Payment)
                              .Include(s => s.SubmittedByUser)
                              .OrderBy(s => s.EndsAt)
                              .TagWithCallSite());

            return Mapper.Map<List<ListSubmissionDTO>>(submissions);
        }

        /// <inheritdoc/>
        public override async Task SubmitScheduledSubmissionAsync(Guid submissionId)
        {
            // Check for the submission entity.
            var submission = await SubmissionsRepository.CheckSubmissionByIdAsync(submissionId);

            // Get the last revision for the submission
            var formDocumentRevision = await FormDocumentRevisionsRepository.FindFirstOrDefaultByConditionAsync(fdr => fdr.FormDocumentId == submission.FormDocumentId,
                                                                                                                 q => q.Include(fdr => fdr.FormDocument).ThenInclude(f => f.Attributes)
                                                                                                                       .OrderByDescending(fdr => fdr.Revision));

            ValidateSubmissionForSubmit(submission, formDocumentRevision, true);

            if (submission.Status != DomainShared.Enums.SubmissionStatus.Scheduled)
            {
                throw new PreconditionFailedException(ApplicationErrors.SUBMISSION_INCORRECT_STATUS.ToErrorCode(), $"Submission has status '{submission.Status}");
            }

            // Do the actual submit
            await DoSubmitAsync(submission, formDocumentRevision);
        }

        private static string GetDateWithoutYearAsString(DateTime? date)
        {
            if (date.HasValue)
            {
                return date.Value.ToString("dd/MM");
            }

            return string.Empty;
        }

        private static void ValidateSubmissionForSubmit(Submission submission, FormDocumentRevision formDocumentRevision, bool scheduledSubmit)
        {
            if (submission.Status == DomainShared.Enums.SubmissionStatus.Submitted)
            {
                throw new ConstraintException(ApplicationErrors.SUBMISSION_ALREADY_SUBMITTED.ToErrorCode(), $"Submission has status '{submission.Status}");
            }

            // Does the end of the financial period allow a submit?
            if (!scheduledSubmit && submission.EndsAt.HasValue && submission.EndsAt.Value.Date > DateTime.UtcNow.Date)
            {
                throw new PreconditionFailedException(ApplicationErrors.SUBMISSION_FINANCIALPERIOD_ENDS_IN_FUTURE.ToErrorCode(), $"End of financial period can not be in the future");
            }

            if (formDocumentRevision == null)
            {
                throw new ConstraintException(ApplicationErrors.FORMDOCUMENTREVISION_NOT_FOUND.ToErrorCode(), "No last revision found");
            }

            if (formDocumentRevision.FormDocument.Status == DomainShared.Enums.FormDocumentStatus.Finalized)
            {
                throw new ConstraintException(ApplicationErrors.FORMDOCUMENTREVISION_ALREADY_FINALIZED.ToErrorCode(), "Last revision has status 'Finalized");
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Ignore")]
        private async Task DoSubmitAsync(Submission submission, FormDocumentRevision formDocumentRevision)
        {
            // Set the current company data on the submission
            var legalEntity = await LegalEntitiesRepository.GetByIdAsync(submission.LegalEntityId,
                q => q.Include(le => le.MasterClient));

            KeyValueForm theform = null;
            if (formDocumentRevision.GetFormBuilder().Form is KeyValueForm form)
            {
                theform = form;

                form.DataSet[FormKeys.CompanyName] = legalEntity.Name;
                form.DataSet[FormKeys.CompanyCode] = legalEntity.Code;
                form.DataSet[FormKeys.CompanyIncorporationNumber] = legalEntity.IncorporationNr;
                form.DataSet[FormKeys.CompanyMasterClientCode] = legalEntity.MasterClient.Code;
                form.DataSet[FormKeys.CompanyReferralOffice] = legalEntity.ReferralOffice;
                form.DataSet[FormKeys.CompanyIsActive] = legalEntity.IsActive.ToString().ToLowerInvariant();

                formDocumentRevision.DataAsJson = form.ToJson();
            }

            // Set the status of the submission, the formdocument and the revision all to finalized.
            formDocumentRevision.Status = DomainShared.Enums.FormDocumentRevisionStatus.Finalized;

            formDocumentRevision.FormDocument.Status = DomainShared.Enums.FormDocumentStatus.Finalized;
            formDocumentRevision.FormDocument.FinalizedAt = DateTime.UtcNow;

            var previousStatus = submission.Status;
            submission.Status = DomainShared.Enums.SubmissionStatus.Submitted;
            submission.SubmittedAt = DateTime.UtcNow;
            if (WorkContext.IdentityUserId.HasValue)
            {
                submission.SubmittedBy = WorkContext.IdentityUserId;
            }

            using var tx = await SubmissionsRepository.DbContext.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);

            try
            {
                // Need to take some additional actions here?
                // Like creating an invoice?
                // Make sure we use a transaction
                if (!submission.InvoiceId.HasValue && !submission.IsPaid)
                {
                    //await CreateInvoiceForSTRSubmissionAsync(submission);
                }

                await SystemAuditManager.AddSubmissionSubmittedActivityLogAsync(submission, previousStatus);

                // Get the values from the form and put them as FormAttributes
                await UpdateFormAttributes(theform, formDocumentRevision.FormDocument);

                await FormDocumentRevisionsRepository.SaveChangesAsync();

                await tx.CommitAsync();
            }
            catch
            {
                await tx.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Marks the submission as scheduled for submit.
        /// </summary>
        /// <param name="submission">The submission to schedule.</param>
        /// <param name="formDocumentRevision">The document revision.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task DoScheduleSubmitAsync(Submission submission, FormDocumentRevision formDocumentRevision)
        {
            if (submission.Status != DomainShared.Enums.SubmissionStatus.Draft &&
                submission.Status != DomainShared.Enums.SubmissionStatus.Revision)
            {
                throw new ConstraintException(ApplicationErrors.SUBMISSION_INCORRECT_STATUS.ToErrorCode(), $"Submission has status '{submission.Status}");
            }

            submission.Status = DomainShared.Enums.SubmissionStatus.Scheduled;

            // Set the user as the submitter
            submission.SubmittedBy = WorkContext.IdentityUserId;

            using var tx = await SubmissionsRepository.DbContext.Database.BeginTransactionAsync(System.Data.IsolationLevel.RepeatableRead);

            try
            {
                await SystemAuditManager.AddSubmissionScheduledActivityLogAsync(submission);

                await FormDocumentRevisionsRepository.SaveChangesAsync();

                await tx.CommitAsync();
            }
            catch
            {
                await tx.RollbackAsync();
                throw;
            }
        }

        /// <summary>
        /// Calculates the dates for a new submission based on settings and previous submissions.
        /// </summary>
        /// <remarks>
        /// Logic/calculations based on the createForm.js of the old application.
        /// </remarks>
        /// <param name="submission">The submission to calculate the dates for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Globalization", "CA1308:Normalize strings to uppercase", Justification = "Ignore")]
        private async Task CalculateSubmissionDatesAsync(Submission submission)
        {
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));

            var legalEntityId = submission.LegalEntityId;
            var moduleId = submission.ModuleId;

            var validation2019 = new DateTime(2019, 1, 1);
            DateTime newFinancialPeriodStart;
            DateTime newFinancialPeriodEnd;

            var legalEntity = await LegalEntitiesRepository.GetByIdAsync(legalEntityId);
            var incorporationDate = legalEntity.IncorporationDate;
            var incorporationDateBefore2019 = incorporationDate.Value < validation2019;

            var lastSubmission = await SubmissionsRepository.FindFirstOrDefaultByConditionAsync(s => s.LegalEntityId == legalEntityId && s.ModuleId == moduleId,
                                                                                                q => q.OrderByDescending(s => s.EndsAt));

            var message = "It seems amendments are needed in a prior submission. Please contact your Trident Officer for assistance.";

            // Get some legal entity settings for ITA (International Tax Authority)
            var itaStartDate = await SettingsManager.GetSettingValueForCompanyAsync<DateTime?>(legalEntity, null, ConfigurationKeys.ITAApprovedStartDate, null);
            var itaEndDate = await SettingsManager.GetSettingValueForCompanyAsync<DateTime?>(legalEntity, null, ConfigurationKeys.ITAApprovedEndDate, null);
            var hasITADate = itaStartDate.HasValue && itaEndDate.HasValue;

            // There is a previous submission?
            if (lastSubmission != null)
            {
                // Has appproved dates from ITA?
                if (hasITADate)
                {
                    // There is an ITA date
                    // If the ITA end date matches the end of the last submission then just create a new succeeding period
                    if (GetDateWithoutYearAsString(itaEndDate) == GetDateWithoutYearAsString(lastSubmission.EndsAt))
                    {
                        newFinancialPeriodStart = lastSubmission.EndsAt.Value.AddDays(1);
                        newFinancialPeriodEnd = newFinancialPeriodStart.AddYears(1).AddDays(-1);
                    }
                    else
                    {
                        // If the ITA start date is later than the end date of the last submission then create a new period based on the ITA dates
                        if (itaStartDate.Value > lastSubmission.EndsAt.Value)
                        {
                            newFinancialPeriodStart = itaStartDate.Value;
                            newFinancialPeriodEnd = itaEndDate.Value;
                        }
                        else
                        {
                            throw new PreconditionFailedException(ApplicationErrors.PRIOR_SUBMISSION_NEEDS_AMENDMENTS.ToErrorCode(), message);
                        }
                    }
                }

                // No ITA date
                else
                {
                    if (!incorporationDate.HasValue)
                    {
                        throw new PreconditionFailedException(ApplicationErrors.COMPANY_NO_INCORPORATIONDATE.ToErrorCode(), "Incorporation date is not set");
                    }

                    // Is the incorporation date before the threshold of 2019?
                    if (incorporationDateBefore2019)
                    {
                        // The start and end of the latest submission should match 30/06 and 29/06
                        var lastStartAsString = GetDateWithoutYearAsString(lastSubmission.StartsAt);
                        var lastEndAsString = GetDateWithoutYearAsString(lastSubmission.EndsAt);

                        if (lastStartAsString == "30/06" && lastEndAsString == "29/06")
                        {
                            newFinancialPeriodStart = lastSubmission.EndsAt.Value.AddDays(1);
                            newFinancialPeriodEnd = newFinancialPeriodStart.AddYears(1).AddDays(-1);
                        }
                        else
                        {
                            throw new PreconditionFailedException(ApplicationErrors.PRIOR_SUBMISSION_NEEDS_AMENDMENTS.ToErrorCode(), message);
                        }
                    }

                    // So the incorporation date is on or after the threshold of 2019
                    else
                    {
                        var startDateAsString = GetDateWithoutYearAsString(lastSubmission.StartsAt);

                        var financialPeriodEndBasedOnStart = lastSubmission.StartsAt.Value.AddYears(1).AddDays(-1);
                        var financialPeriodEndActual = lastSubmission.EndsAt.Value;

                        // If the start of the last submission matches the incorporationdate (day and month, no year) and the last end of the financialperiod matches the calculated end
                        if (startDateAsString == GetDateWithoutYearAsString(incorporationDate) && financialPeriodEndBasedOnStart == financialPeriodEndActual)
                        {
                            newFinancialPeriodStart = lastSubmission.EndsAt.Value.AddDays(1);
                            newFinancialPeriodEnd = newFinancialPeriodStart.AddYears(1).AddDays(-1);
                        }
                        else
                        {
                            throw new PreconditionFailedException(ApplicationErrors.PRIOR_SUBMISSION_NEEDS_AMENDMENTS.ToErrorCode(), message);
                        }
                    }
                }
            }

            // No submission yet
            else
            {
                // Has appproved dates from ITA?
                if (hasITADate)
                {
                    newFinancialPeriodStart = itaStartDate.Value;
                    newFinancialPeriodEnd = itaEndDate.Value;
                }

                // No ITA date
                else
                {
                    if (!incorporationDate.HasValue)
                    {
                        throw new PreconditionFailedException(ApplicationErrors.COMPANY_NO_INCORPORATIONDATE.ToErrorCode(), "Incorporation date is not set");
                    }

                    // Is the incorporation date before the threshold of 2019?
                    if (incorporationDateBefore2019)
                    {
                        newFinancialPeriodStart = new DateTime(2019, 6, 30);
                        newFinancialPeriodEnd = newFinancialPeriodStart.AddYears(1).AddDays(-1); ;
                    }

                    // So the incorporation date is on or after the threshold of 2019
                    else
                    {
                        newFinancialPeriodStart = incorporationDate.Value;
                        newFinancialPeriodEnd = newFinancialPeriodStart.AddYears(1).AddDays(-1); ;
                    }
                }
            }

            submission.StartsAt = newFinancialPeriodStart;
            submission.EndsAt = newFinancialPeriodEnd;
            submission.FinancialYear = newFinancialPeriodEnd.Year;

            KeyValueForm theform = null;
            var formDocumentRevision = submission.FormDocument.FormDocumentRevisions.Single();
            if (formDocumentRevision.GetFormBuilder().Form is KeyValueForm form)
            {
                theform = form;

                form.SetFormValue(FormKeys.ITAHasDates, hasITADate);
                if (hasITADate)
                {
                    form.SetFormValue(FormKeys.ITAApprovedStartDate, itaStartDate);
                    form.SetFormValue(FormKeys.ITAApprovedEndDate, itaStartDate);
                }

                form.SetFormValue(FormKeys.ITABefore2019, incorporationDateBefore2019);

                formDocumentRevision.DataAsJson = form.ToJson();
            }
        }
    }
}