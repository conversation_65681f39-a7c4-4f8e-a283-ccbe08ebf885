// <copyright file="ISubmissionsManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Submissions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Submissions
{
    /// <summary>
    /// Interface for the submissions manager.
    /// </summary>
    public interface ISubmissionsManager : IScopedService
    {
        /// <summary>
        /// Updates the payment status of multiple submissions.
        /// </summary>
        /// <param name="submissionIds">The IDs of the submissions to update.</param>
        /// <param name="isPaid">True to mark as paid, false to mark as unpaid.</param>
        /// <param name="authorizedJurisdictionIDs">The list of jurisdiction IDs that the user is authorized to access.</param>
        /// <returns>The updated submissions.</returns>
        Task<MarkSubmissionsAsPaidResponse> MarkSubmissionsAsPaidAsync(List<Guid> submissionIds, bool isPaid, List<Guid> authorizedJurisdictionIDs);

        /// <summary>
        /// Updates the payment status of multiple submissions based on company code and filing year.
        /// </summary>
        /// <param name="companyFilingYearDtos">The list of company code and filing year pairs.</param>
        /// <param name="isPaid">True to mark as paid, false to mark as unpaid.</param>
        /// <param name="moduleId">The id of the module that this request is related to.</param>
        /// <param name="authorizedJurisdictionIDs">The list of jurisdiction IDs that the user is authorized to access.</param>
        /// <returns>The updated submissions.</returns>
        Task<MarkSubmissionsAsPaidByCompanyYearResponse> MarkSubmissionsAsPaidByCompanyAndYearAsync(IEnumerable<CompanyFinancialYearDto> companyFilingYearDtos,
            bool isPaid,
            Guid moduleId,
            List<Guid> authorizedJurisdictionIDs);

        /// <summary>
        /// Gets the paid status of submissions based on company code and filing year.
        /// </summary>
        /// <param name="companyFilingYearDtos">The list of company code and filing year pairs.</param>
        /// <param name="moduleId">The unique identifier of the module to check submissions for.</param>
        /// <param name="authorizedJurisdictionIDs">The list of jurisdiction IDs that the user is authorized to access.</param>
        /// <returns>The paid status for each company/year pair.</returns>
        Task<SubmissionsPaidStatusResponse> GetSubmissionsPaidStatusByCompanyAndYearAsync(IEnumerable<CompanyFinancialYearDto> companyFilingYearDtos,
            Guid moduleId,
            List<Guid> authorizedJurisdictionIDs);

        /// <summary>
        /// Starts a new submission for the company mentioned in the data, for the given module and year.
        /// </summary>
        /// <param name="model">The data required to start a new submission, including legal entity ID, module ID, and financial year.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the created submission.</returns>
        Task<SubmissionDTO> StartSubmissionAsync(StartSubmissionDTO model);

        /// <summary>
        /// Searches for the submissions for a legalentity/module.
        /// </summary>
        /// <param name="request">The request containing search criteria for submissions by legal entity and module.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a paged list of submissions.</returns>
        Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(ListSubmissionsRequest request);

        /// <summary>
        /// Searches for the submissions for a master client.
        /// </summary>
        /// <param name="request">The request containing search criteria for submissions by master client.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a paged list of submissions.</returns>
        Task<IPagedList<ListSubmissionDTO>> ListSubmissionsAsync(ListSubmissionsByMasterClientRequest request);

        /// <summary>
        /// Gets just the submission without any other data. No check on permission.
        /// </summary>
        /// <param name="submissionId">The id of the submission to get.</param>
        /// <returns>The found submission.</returns>
        Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId);

        /// <summary>
        /// Gets the latest version of the submission.
        /// </summary>
        /// <param name="submissionId">The id of the submission to get.</param>
        /// <param name="includeFormDocument">Whether to include the form document data in the response.</param>
        /// <param name="allowDeleted">Whether to include deleted submissions.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the submission data.</returns>
        Task<SubmissionDTO> GetSubmissionAsync(Guid submissionId, bool includeFormDocument, bool allowDeleted = false);

        /// <summary>
        /// Updates the export status of the submissions.
        /// </summary>
        /// <param name="submissionIds">The list of submission ids to export.</param>
        /// <param name="userId">The user id of the user exporting the submissions.</param>
        /// <param name="moduleId">The unique identifier of the module for which submissions are being exported.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task UpdateSubmissionExport(IEnumerable<Guid> submissionIds, Guid userId, Guid moduleId);

        /// <summary>
        /// Updates the dataset of the revision of the submission.
        /// </summary>
        /// <param name="model">The submission dataset containing the updated key-value pairs and document IDs.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the updated submission.</returns>
        Task<SubmissionDTO> UpdateSubmissionDataSetAsync(SubmissionDataSetDTO model);

        /// <summary>
        /// Submits the submission. This will make the submission final.
        /// </summary>
        /// <param name="model">The submission data containing the submission ID and scheduling options.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the submitted submission.</returns>
        Task<SubmissionDTO> SubmitSubmissionAsync(SubmitSubmissionDTO model);

        /// <summary>
        /// Reopens the submission by creating a new revision in draft status.
        /// </summary>
        /// <param name="model">The reopen submission data containing the submission ID and comments.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the reopened submission.</returns>
        Task<SubmissionDTO> ReopenSubmissionAsync(ReopenSubmissionDTO model);

        /// <summary>
        /// Gets the list of years that the user can select from when creating a new submission.
        /// </summary>
        /// <param name="request">The request with parameters.</param>
        /// <returns>AvailableSubmissionYearsDTO.</returns>
        Task<AvailableSubmissionYearsDTO> GetAvailableSubmissionYears(AvailableSubmissionYearsRequest request);

        /// <summary>
        /// Gets the list of years that the user can select from when for submissions submission.
        /// </summary>
        /// <param name="request">The request with parameters.</param>
        /// <returns>AllSubmissionYearsDTO.</returns>
        Task<AllSubmissionYearsDTO> GetAllSubmissionYears(AllSubmissionYearsRequest request);

        /// <summary>
        /// Searches for the submissions for a module with extra search criteria.
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A paged list with <see cref="SubmissionDTO"/>.</returns>
        Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsAsync(SearchSubmissionsRequest request);

        /// <summary>
        /// Mark the submission as deleted.
        /// </summary>
        /// <remarks>
        /// This will mark the submission as deleted if the status is not Submitted.
        /// </remarks>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task DeleteSubmissionAsync(Guid submissionId);

        /// <summary>
        /// Marks the submission with the given invoiceid as paid. Skip if there is no submission for this invoice.
        /// </summary>
        /// <param name="invoiceId">Id of the invoice.</param>
        /// <param name="saveChanges">Whether to save changes to the database immediately.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task MarkSubmissionsAsPaidByInvoiceIdAsync(Guid invoiceId, bool saveChanges = false);

        /// <summary>
        /// Delete the FormDocumentDocument entity given a document id if exists.
        /// </summary>
        /// <param name="documentId">The id of the document as Guid.</param>
        /// <param name="saveChanges">Indicates whther the changes must be saved.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task DeleteFormDocumentDocumentByDocumentIdAsync(Guid documentId, bool saveChanges = false);

        /// <summary>
        /// Update the general information for a submission entity.
        /// </summary>
        /// <remarks>
        /// Used to check and update the basic configuration for submissions.
        /// </remarks>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <param name="data">The data used to update the general information for the submission.</param>
        /// <param name="saveChanges">Indicates whther the changes must be saved.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task UpdateSubmissionGeneralInformationAsync(Guid submissionId, UpdateSubmissionInformationDTO data, bool saveChanges = false);

        /// <summary>
        /// Update the general information for a submission entity.
        /// For management users only.
        /// </summary>
        /// <remarks>
        /// Used to check and update the basic configuration for submissions.
        /// </remarks>
        /// <param name="submissionId">The id of the submission as Guid.</param>
        /// <param name="data">The data used to update the general information for the submission.</param>
        /// <param name="saveChanges">Indicates whther the changes must be saved.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task UpdateSubmissionGeneralInformationManagementAsync(Guid submissionId, UpdateSubmissionInformationDTO data, bool saveChanges = false);

        /// <summary>
        /// Searches the Panama submissions for a module with extra search criteria (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A paged list with <see cref="ListSubmissionDTO"/>.</returns>
        Task<IPagedList<ListSubmissionDTO>> SearchSubmissionsForPanamaAsync(FilterSubmissionsRequest request);

        /// <summary>
        /// Searches the Bahamas submissions for a module with extra search criteria (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A paged list with <see cref="ListSubmissionBahamasDTO"/>.</returns>
        Task<IPagedList<ListSubmissionBahamasDTO>> SearchSubmissionsForBahamasAsync(FilterSubmissionsRequestForBahamas request);

        /// <summary>
        /// Searches the Bahamas submissions for a module with extra search criteria (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A paged list with <see cref="ListSubmissionBahamasDTO"/>.</returns>
        Task<List<ListSubmissionBahamasDTO>> SearchSubmissionsForBahamasReportAsync(FilterSubmissionsRequestForBahamas request);

        /// <summary>
        /// Retreive Panama submissions filtered for report (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A list of filtered submissions for the module.</returns>
        Task<List<Submission>> FilterPanamaSubmissionsForReportAsync(FilterSubmissionsRequest request);

        /// <summary>
        /// Retrieve the selected submissions given a list of ids (Management).
        /// </summary>
        /// <param name="request">The request with the necessary data to retrieve submissions.</param>
        /// <returns>A list of submissions given the seleted ids.</returns>
        Task<List<Submission>> RetrieveSubmissionsByIdsAsync(RetrieveSubmissionsRequest request);

        /// <summary>
        /// Retreive Nevis submissions filtered for report (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A list of filtered submissions for the module.</returns>
        Task<List<SubmissionNevisReportDTO>> FilterNevisSubmissionsForReportAsync(SearchSubmissionsRequest request);

        /// <summary>
        /// Retreive Bahamas submissions filtered for report (Management).
        /// </summary>
        /// <param name="request">The request with all the parameters for the search.</param>
        /// <returns>A list of filtered submissions for the module.</returns>
        Task<List<Submission>> FilterBahamasSubmissionsForReportAsync(SearchSubmissionsRequest request);

        /// <summary>
        /// Gets the key of the module that this submission is for.
        /// </summary>
        /// <param name="submissionId">Id of the submission.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the module key as a string.</returns>
        Task<string> GetModuleKeyForSubmissionAsync(Guid submissionId);

        /// <summary>
        /// Gets just the submissions without any other data. No check on permission.
        /// </summary>
        /// <param name="submissionIds">The submission ids as list of Guid.</param>
        /// <returns>The found submissions.</returns>
        Task<List<SubmissionDTO>> GetSubmissionsAsync(List<Guid> submissionIds);
    }
}
