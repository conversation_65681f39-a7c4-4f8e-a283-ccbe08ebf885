﻿// <copyright file="FilterSubmissionsRequestForBahamas.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.DataManager.Submissions.RequestResponses
{
    /// <summary>
    /// DTO for the request to search for submissions for the Bahamas jurisdiction.
    /// </summary>
    public class FilterSubmissionsRequestForBahamas : PagedAndSortedRequest, IJurisdictionFilteredRequest
    {
        /// <summary>
        /// Gets or sets the module id as Guid.
        /// </summary>
        public Guid ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within companies/legalentities, masterclients or referral offices.
        /// </summary>
        /// <remarks>
        /// Use this searchterm to search with a single term for 'or-ring' the search.
        /// </remarks>
        public string GeneralSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the status for filtering submissions with the given status.
        /// </summary>
        public SubmissionStatus? Status { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be submitted or not.
        /// </summary>
        public bool? ShowSubmitted { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission can be reopened or not.
        /// </summary>
        public bool? AllowReopen { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted before a specific date.
        /// </summary>
        public DateTime? SubmittedAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted after a specific date.
        /// </summary>
        public DateTime? SubmittedBeforeDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission with legal entities incorporated before a specific date.
        /// </summary>
        public DateTime? CompanyIncorporatedAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission with legal entities incorporated after a specific date.
        /// </summary>
        public DateTime? CompanyIncorporatedBeforeDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission which the financial period starts.
        /// </summary>
        public DateTime? FinancialPeriodStartAt { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission which the financial period ends.
        /// </summary>
        public DateTime? FinancialPeriodEndAt { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission with payment was done before a specific date.
        /// </summary>
        public DateTime? PaidAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission with payment was done after a specific date.
        /// </summary>
        public DateTime? PaidBeforeDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission with the selected relevant activities.
        /// </summary>
        public List<string> RelevantActivities { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets an indication whether the submission has been exported or not.
        /// </summary>
        public bool? IsExported { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be deleted or not.
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be paid or unpaid.
        /// </summary>
        public bool? IsPaid { get; set; }

        /// <inheritdoc />
        public List<Guid> AuthorizedJurisdictionIDs { get; set; }
    }
}
