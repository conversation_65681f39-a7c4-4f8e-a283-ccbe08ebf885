﻿// <copyright file="ListSubmissionsRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Paging;
using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.DataManager.Submissions.RequestResponses
{
    /// <summary>
    /// Represents a request for listing submissions with paging and sorting options.
    /// </summary>
    public class ListSubmissionsRequest
    {
        /// <summary>
        /// Gets or sets the info for paging.
        /// </summary>
        public PagingInfo PagingInfo { get; set; }

        /// <summary>
        /// Gets or sets the info for sorting.
        /// </summary>
        public SortingInfo SortingInfo { get; set; }

        /// <summary>
        /// Gets or sets the id of the legalentity to get submissions for.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the id of the module to get submissions for.
        /// </summary>
        public Guid ModuleId { get; set; }
    }
}
