﻿// <copyright file="SearchSubmissionsRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Paging;
using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.DataManager.Submissions.RequestResponses
{
    /// <summary>
    /// A request model for the submissions datamanager to search for submissions.
    /// </summary>
    public class SearchSubmissionsRequest : IJurisdictionFilteredRequest
    {
        /// <summary>
        /// Gets or sets the info for paging.
        /// </summary>
        public PagingInfo PagingInfo { get; set; } = new PagingInfo(1, int.MaxValue);

        /// <summary>
        /// Gets or sets the info for sorting.
        /// </summary>
        public SortingInfo SortingInfo { get; set; } = new SortingInfo();

        /// <summary>
        /// Gets or sets the id of the module to get submissions for.
        /// </summary>
        public Guid ModuleId { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within companies/legalentities.
        /// </summary>
        public string LegalEntitySearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within masterclients.
        /// </summary>
        public string MasterClientSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within companies/legalentities.
        /// </summary>
        public string ReferralOfficeSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the text to use for searching within companies/legalentities, masterclients or referral offices.
        /// </summary>
        /// <remarks>
        /// Use this searchterm to search with a single term for 'or-ring' the search.
        /// </remarks>
        public string GeneralSearchTerm { get; set; }

        /// <summary>
        /// Gets or sets the country to search for.
        /// </summary>
        public string Country { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted before a specific date.
        /// </summary>
        public DateTime? SubmittedAfterDate { get; set; }

        /// <summary>
        /// Gets or sets the date for filtering submission submitted after a specific date.
        /// </summary>
        public DateTime? SubmittedBeforeDate { get; set; }

        /// <summary>
        /// Gets or sets the financial year to search the submisisons for.
        /// </summary>
        public int FinancialYear { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be paid or unpaid.
        /// </summary>
        public bool? IsPaid { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission has been exported or not.
        /// </summary>
        public bool? IsExported { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be deleted or not.
        /// </summary>
        public bool? IsDeleted { get; set; }

        /// <summary>
        /// Gets or sets the ids of the jurisdictions to search the submissions for (via companies).
        /// </summary>
        public List<Guid> AuthorizedJurisdictionIDs { get; set; } = new List<Guid>();
    }
}
