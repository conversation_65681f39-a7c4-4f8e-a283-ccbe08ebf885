﻿// <copyright file="ListUsersRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.Paging;
using NetProGroup.Trust.Application.Contracts.Shared;

namespace NetProGroup.Trust.DataManager.Users.RequestResponses
{
    /// <summary>
    /// Represents a request to list users with optional filtering, paging, and sorting capabilities.
    /// </summary>
    public class ListUsersRequest
    {
        /// <summary>
        /// Gets or sets the info for paging.
        /// </summary>
        public PagingInfo PagingInfo { get; set; }

        /// <summary>
        /// Gets or sets the info for sorting.
        /// </summary>
        public SortingInfo SortingInfo { get; set; }

        /// <summary>
        /// Gets or sets the id of the masterclient to get users for.
        /// </summary>
        public Guid? MasterClientId { get; set; }
    }
}
