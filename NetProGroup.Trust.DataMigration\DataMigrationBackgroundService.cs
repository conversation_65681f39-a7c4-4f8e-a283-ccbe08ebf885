using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Threading.Channels;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Domain.Shared.Consts;

namespace NetProGroup.Trust.DataMigration
{
    /// <summary>
    /// Background service responsible for executing data migration tasks.
    /// </summary>
    public class DataMigrationBackgroundService : BackgroundService
    {
        private readonly ILogger<DataMigrationBackgroundService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly Channel<(string region, Guid startedByUserId, LockDTO jobLock)> _migrationQueue;
        private readonly DataMigrationAppSettings _settings;

        /// <summary>
        /// Initializes a new instance of the <see cref="DataMigrationBackgroundService"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="serviceProvider">The service provider for dependency injection.</param>
        /// <param name="options">The application settings for data migration.</param>
        public DataMigrationBackgroundService(
            ILogger<DataMigrationBackgroundService> logger,
            IServiceProvider serviceProvider,
            IOptions<DataMigrationAppSettings> options)
        {
            _logger = Check.NotNull(logger, nameof(logger));
            _serviceProvider = Check.NotNull(serviceProvider, nameof(serviceProvider));
            _migrationQueue = Channel.CreateUnbounded<(string region, Guid startedByUserId, LockDTO jobLock)>();
            _settings = Check.NotNull(options, nameof(options)).Value;
        }

        /// <summary>
        /// Queues a migration task for execution.
        /// </summary>
        /// <param name="region">The region for which to perform the migration.</param>
        /// <param name="startedByUserId">The ID of the user who initiated the migration.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        /// <exception cref="ConflictException">Thrown when a migration is already in progress.</exception>
        public async Task QueueMigrationAsync(string region, Guid startedByUserId)
        {
            if (!_settings.Enabled)
            {
                throw new InvalidOperationException("Data migration is not enabled.");
            }

            using var scope = _serviceProvider.CreateScope();

            var lockManager = scope.ServiceProvider.GetRequiredService<ILockManager>();
            var jobLock = await AcquireLockAsync(new Guid(ScheduledJobConsts.ViewPointSyncJobId), lockManager, startedByUserId);

            if (!jobLock.Id.HasValue)
            {
                var message = "Migration or VP data sync already in progress.";
                _logger.LogWarning(message);
                throw new ConflictException(message);
            }

            await _migrationQueue.Writer.WriteAsync((region, startedByUserId, jobLock));
        }

        /// <summary>
        /// Executes the background task, processing migration requests from the queue.
        /// </summary>
        /// <param name="stoppingToken">Cancellation token to stop the background service.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                // Wait for a migration task to be available in the queue
                await _migrationQueue.Reader.WaitToReadAsync(stoppingToken);
                (string region, Guid startedByUserId, LockDTO jobLock) = await _migrationQueue.Reader.ReadAsync(stoppingToken);

                using (var scope = _serviceProvider.CreateScope())
                {
                    var lockManager = scope.ServiceProvider.GetRequiredService<ILockManager>();
                    var dependencyTracker = scope.ServiceProvider.GetRequiredService<IApplicationInsightsDependencyTracker>();

                    await dependencyTracker.TrackDependencyAsync(
                        "DataMigration",
                        async () =>
                        {
                            try
                            {
                                // Resolve and execute the migration service
                                var dataMigrationService = scope.ServiceProvider.GetRequiredService<DataMigrationService>();
                                await dataMigrationService.ExecuteMigrationAsync(region, startedByUserId, jobLock);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error occurred while processing migration for region {Region}", region);
                                throw; // Re-throw to let the dependency tracker mark as failed
                            }
                            finally
                            {
                                await lockManager.ReleaseLockAsync(jobLock.Id.GetValueOrDefault());
                            }
                        },
                        "Background",
                        data: $"Region: {region}");
                }
            }
        }

        /// <summary>
        /// Acquires a lock to execute the migration.
        /// </summary>
        /// <param name="jobId">Id of the job to acqiure the lock for.</param>
        /// <param name="lockManager"></param>
        /// <param name="startedByUserId"></param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private static async Task<LockDTO> AcquireLockAsync(Guid jobId, ILockManager lockManager, Guid startedByUserId)
        {
            var request = new AcquireLockRequestDTO
            {
                IdentityUserId = startedByUserId,
                EntityName = "ScheduledJob",
                EntityId = jobId,
                Session = string.Empty
            };

            return await lockManager.AcquireLockAsync(request);
        }
    }
}
