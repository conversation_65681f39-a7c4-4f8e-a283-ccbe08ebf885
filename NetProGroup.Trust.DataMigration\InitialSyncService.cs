// <copyright file="InitialSyncService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.DataMigration.Models.Nevis;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.DataMigration
{
    /// <summary>
    /// Provides services for performing initial synchronization of data during migration processes.
    /// </summary>
    public class InitialSyncService
    {
        private const string CompaniesCollectionName = "companies";
        private const string CompanyDummySyncDisplayName = "Company dummy sync";

        private readonly ILogger<InitialSyncService> _logger;
        private readonly IDataMigrationsDataManager _dataMigrationsDataManager;
        private readonly EntityMigrationService _entityMigrationService;
        private readonly TrustDbContext _sqlDbContext;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly IMasterClientsRepository _masterClientRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IUserRepository _userRepository;
        private readonly DataMigrationAppSettings _dataMigrationAppSettings;

        /// <summary>
        /// Initializes a new instance of the <see cref="InitialSyncService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="dataMigrationsDataManager">Instance of the DataMigrationsDataManager.</param>
        /// <param name="entityMigrationService">Instance of the EntityMigrationService.</param>
        /// <param name="sqlDbContext">Instance of the TrustDbContext.</param>
        /// <param name="legalEntitiesRepository">Instance of the ILegalEntitiesRepository.</param>
        /// <param name="masterClientRepository">Instance of the IMasterClientsRepository.</param>
        /// <param name="jurisdictionsRepository">Instance of the IJurisdictionsRepository.</param>
        /// <param name="userRepository">Instance of the IUserRepository.</param>
        /// <param name="dataMigrationAppSettings">Instance of the DataMigrationAppSettings.</param>
        public InitialSyncService(
            ILogger<InitialSyncService> logger,
            IDataMigrationsDataManager dataMigrationsDataManager,
            EntityMigrationService entityMigrationService,
            TrustDbContext sqlDbContext,
            ILegalEntitiesRepository legalEntitiesRepository,
            IMasterClientsRepository masterClientRepository,
            IJurisdictionsRepository jurisdictionsRepository,
            IUserRepository userRepository,
            IOptions<DataMigrationAppSettings> dataMigrationAppSettings)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            ArgumentNullException.ThrowIfNull(dataMigrationAppSettings, nameof(dataMigrationAppSettings));
            _dataMigrationsDataManager = dataMigrationsDataManager ?? throw new ArgumentNullException(nameof(dataMigrationsDataManager));
            _entityMigrationService = entityMigrationService ?? throw new ArgumentNullException(nameof(entityMigrationService));
            _sqlDbContext = sqlDbContext ?? throw new ArgumentNullException(nameof(sqlDbContext));
            _legalEntitiesRepository = legalEntitiesRepository ?? throw new ArgumentNullException(nameof(legalEntitiesRepository));
            _masterClientRepository = masterClientRepository ?? throw new ArgumentNullException(nameof(masterClientRepository));
            _jurisdictionsRepository = jurisdictionsRepository ?? throw new ArgumentNullException(nameof(jurisdictionsRepository));
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _dataMigrationAppSettings = dataMigrationAppSettings.Value;
        }

        /// <summary>
        /// Performs the initial synchronization process.
        /// </summary>
        /// <param name="migrationRecord">The migration record for the sync process.</param>
        /// <param name="jobLock">The job lock.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task PerformInitialSyncAsync(Domain.DataMigrations.DataMigration migrationRecord, LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));
            ArgumentNullException.ThrowIfNull(jobLock, nameof(jobLock));

            try
            {
                _logger.LogInformation("Starting initial sync for region: {Region}", migrationRecord.Region);
                await _dataMigrationsDataManager.UpdateMigrationRecordAsync(migrationRecord);

                var jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == migrationRecord.Region);
                if (_dataMigrationAppSettings.UseDummyInitialSync)
                {
                    if (migrationRecord.Region == JurisdictionCodes.Nevis)
                    {
                        // TODO, implement for other jurisdictions?
                        await DummyInitialSync(migrationRecord, jobLock, jurisdiction);
                    }
                }
                else
                {
                    if (!jurisdiction.InitialSyncCompleted)
                    {
                        throw new InvalidOperationException("Initial sync not completed for jurisdiction.");
                    }
                }

                migrationRecord.InitialSyncCompleted = true;
                await _dataMigrationsDataManager.UpdateMigrationRecordAsync(migrationRecord);
                _logger.LogInformation("Initial sync completed for region: {Region}", migrationRecord.Region);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during initial sync for region: {Region}", migrationRecord.Region);
                throw;
            }
        }

        private static void UpdateLegalEntityDetails(LegalEntity legalEntity, Company company)
        {
            legalEntity.Name = company.Name;
            if (company.IsDeleted)
            {
                legalEntity.SetInactive();
            }
            else
            {
                legalEntity.ApproveOnboarding();
            }

            legalEntity.Code = company.VpCode ?? company.Code;
            legalEntity.LegacyCode = company.Code;
            legalEntity.EntityType = LegalEntityType.Company;
            legalEntity.IncorporationDate = DateTime.Now;
            legalEntity.IncorporationNr = company.IncorporationCode;
            legalEntity.ReferralOffice = company.ReferralOffice;
        }

        /// <summary>
        /// This method only exists because the VP->PCP dataflow is not implemented yet and that causes all of the migrations to fail,
        /// because the master clients and companies are not being created. This method is a temporary solution to create the master clients and companies.
        /// </summary>
        /// <param name="migrationRecord">The migration record for the sync process.</param>
        /// <param name="jobLock">The job lock.</param>
        /// <param name="jurisdiction">The jurisdiction for which to perform the dummy initial sync.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task DummyInitialSync(Domain.DataMigrations.DataMigration migrationRecord, LockDTO jobLock, Jurisdiction jurisdiction)
        {
            await _entityMigrationService.MigrateEntityAsync<Company>(migrationRecord, jobLock, CompaniesCollectionName, CompanyDummySyncDisplayName, async (company) =>
            {
                var companyCode = company.Code;
                var companyVpCode = company.VpCode;

                try
                {
                    if (companyVpCode == null)
                    {
                        _logger.LogWarning("Company with Code {Code} does not have a VpCode. Skipping...", companyCode);
                        return (false, new List<string> { companyCode });
                    }
                    var legalEntity = await GetOrCreateLegalEntity(companyVpCode);
                    UpdateLegalEntityDetails(legalEntity, company);
                    await AssignMasterClient(legalEntity, company);
                    await AssignJurisdiction(legalEntity, jurisdiction.Id);

                    await _legalEntitiesRepository.SaveChangesAsync();

                    _logger.LogInformation("Successfully synced company with LegacyCode {LegacyCode} and Code {Code}", companyCode, companyVpCode);
                    return (true, new List<string>());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error syncing company: {ErrorMessage}", ex.Message);
                    return (false, new List<string> { companyCode });
                }
            }, company => new { company.Code });

            jurisdiction.InitialSyncCompleted = true;
            await _jurisdictionsRepository.UpdateAsync(jurisdiction, true);
        }

        private async Task<LegalEntity> GetOrCreateLegalEntity(string companyVpCode)
        {
            var legalEntity = await _sqlDbContext.Set<LegalEntity>()
                .Include(le => le.Jurisdiction)
                .FirstOrDefaultAsync(le => le.Code == companyVpCode);

            if (legalEntity != null)
            {
                _logger.LogInformation("LegalEntity with Code {Code} found. Updating...", companyVpCode);
                await _legalEntitiesRepository.UpdateAsync(legalEntity);
            }
            else
            {
                _logger.LogInformation("LegalEntity with Code {Code} not found. Inserting...", companyVpCode);
                legalEntity = new LegalEntity();
                await _legalEntitiesRepository.InsertAsync(legalEntity);
            }

            return legalEntity;
        }

        private async Task AssignMasterClient(LegalEntity legalEntity, Company company)
        {
            var masterClient = await _masterClientRepository.FindFirstOrDefaultByConditionAsync(
                client => client.Code == company.MasterClientCode);

            if (masterClient == null)
            {
                masterClient = new MasterClient()
                {
                    Code = company.MasterClientCode,
                    Name = company.MasterClientCode
                };
                await _masterClientRepository.InsertAsync(masterClient);

                await AssignUsersToMasterClient(masterClient);
            }

            legalEntity.MasterClient = masterClient;
        }

        private async Task AssignJurisdiction(LegalEntity legalEntity, Guid jurisdictionId)
        {
            legalEntity.JurisdictionId ??= jurisdictionId;
        }

        private async Task AssignUsersToMasterClient(MasterClient masterClient)
        {
            var users = await _userRepository.SearchAsync();

            foreach (var user in users)
            {
                if (masterClient.MasterClientUsers.All(x => x.UserId != user.Id))
                {
                    masterClient.MasterClientUsers.Add(new MasterClientUser { UserId = user.Id });
                }
            }
        }
    }
}
