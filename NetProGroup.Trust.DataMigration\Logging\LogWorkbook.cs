﻿using ClosedXML.Excel;

namespace NetProGroup.Trust.DataMigration.Logging
{
    /// <summary>
    /// Represents an Excel workbook for logging data migration operations and results.
    /// </summary>
    public class LogWorkbook : IDisposable
    {
        private const int ColumnOffset = 3;
        private readonly XLWorkbook _xlWorkbook;
        private bool _disposed = false;

        /// <summary>
        /// Constructor to recreate the XLWorkbook from a memorystream.
        /// </summary>
        /// <param name="memoryStream">Memory stream to read the workbook from.</param>
        private LogWorkbook(MemoryStream memoryStream)
        {
            _xlWorkbook = new XLWorkbook(memoryStream);
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="LogWorkbook"/> class with a new empty workbook.
        /// </summary>
        public LogWorkbook()
        {
            _xlWorkbook = new XLWorkbook();
        }

        /// <summary>
        /// Disposes the LogWorkbook, releasing any resources it holds.
        /// </summary>
        public void Dispose()
        {
            // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Creates a new LogWorkbook instance from a Base64 encoded string.
        /// </summary>
        /// <param name="data">The Base64 encoded string containing the workbook data.</param>
        /// <returns>A new <see cref="LogWorkbook"/> instance created from the provided data.</returns>
        public static LogWorkbook FromBase64(string data)
        {
            var bytes = Convert.FromBase64String(data);

            // Create a MemoryStream from the byte array
            var ms = new MemoryStream(bytes);
            ms.Position = 0;

            return new LogWorkbook(ms);
        }

        /// <summary>
        /// Adds a migrated entity record to the workbook with the specified details.
        /// </summary>
        /// <param name="entityTypeName">The name of the entity type being migrated.</param>
        /// <param name="entityIdentifier">The identifier of the entity being migrated.</param>
        /// <param name="success">A value indicating whether the migration was successful.</param>
        public void AddMigratedEntity(string entityTypeName, dynamic entityIdentifier, bool success)
        {
            IXLWorksheet workSheet = GetWorksheet(entityTypeName, entityIdentifier);

            var row = workSheet.LastRowUsed().RowNumber() + 1;

            var dateCell = workSheet.Cell(row, 1);
            dateCell.Value = DateTime.UtcNow;
            dateCell.Style.DateFormat.Format = "yyyy-MM-dd HH:mm:ss"; // TODO use constant
            var successCell = workSheet.Cell(row, 2);
            successCell.Value = success;

            var properties = entityIdentifier.GetType().GetProperties();
            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var propertyValue = property.GetValue(entityIdentifier);
                workSheet.Cell(row, i + ColumnOffset).Value = propertyValue ?? "<null>";
            }
        }

        /// <summary>
        /// Converts the workbook to a memory stream for storage or transmission.
        /// </summary>
        /// <returns>A <see cref="MemoryStream"/> containing the workbook data.</returns>
        public MemoryStream AsMemoryStream()
        {
            using var ms = new MemoryStream();
            if (_xlWorkbook.Worksheets.Count == 0)
            {
                _xlWorkbook.Worksheets.Add("TBD");
            }
            _xlWorkbook.SaveAs(ms);
            ms.Position = 0;

            return ms;
        }

        /// <summary>
        /// Converts the workbook to a Base64 encoded string for storage or transmission.
        /// </summary>
        /// <returns>A Base64 encoded string representation of the workbook.</returns>
        public string AsBase64()
        {
            using (var ms = new MemoryStream())
            {
                // THis is throwing an error on the second save
                _xlWorkbook.SaveAs(ms);
                ms.Position = 0;

                var msBytes = ms.ToArray();
                return Convert.ToBase64String(msBytes);
            }
        }

        /// <summary>
        /// Releases the unmanaged resources used by the LogWorkbook and optionally releases the managed resources.
        /// </summary>
        /// <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _xlWorkbook.Dispose();
                }

                _disposed = true;
            }
        }

        private void SetHeaders(IXLWorksheet worksheet, dynamic record)
        {
            var properties = record.GetType().GetProperties();
            var dateHeaderCell = worksheet.Cell(1, 1);
            dateHeaderCell.Value = "Timestamp";
            var successHeaderCell = worksheet.Cell(1, 2);
            successHeaderCell.Value = "Success";

            for (int i = 0; i < properties.Length; i++)
            {
                worksheet.Cell(1, i + ColumnOffset).Value = properties[i].Name;
            }
        }

        private IXLWorksheet GetWorksheet(string entityTypeName, dynamic record)
        {
            var worksheetName = $"{entityTypeName}";

            if (!_xlWorkbook.Worksheets.TryGetWorksheet(worksheetName, out var worksheet))
            {
                worksheet = _xlWorkbook.Worksheets.Add(worksheetName);
            }
            SetHeaders(worksheet, @record);

            return worksheet;
        }
    }
}
