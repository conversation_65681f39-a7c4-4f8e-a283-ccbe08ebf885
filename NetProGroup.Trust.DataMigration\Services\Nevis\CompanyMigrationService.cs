// <copyright file="CompanyMigrationService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.ActivityLogs.Services;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Repository;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Trust.Domain.Shared.Enums;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.Domain.DataMigrations;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DataMigration.Models.Nevis;

namespace NetProGroup.Trust.DataMigration.Services.Nevis
{
    /// <summary>
    /// Service for migrating company data.
    /// </summary>
    public class CompanyMigrationService
    {
        private static readonly TimeSpan JobLockRefreshMargin = TimeSpan.FromSeconds(30);
        private readonly ILogger<CompanyMigrationService> _logger;
        private readonly TrustDbContext _sqlDbContext;
        private readonly ISettingsManager _settingsManager;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IActivityLogManager _activityLogManager;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;
        private readonly ILockManager _lockManager;
        private readonly IBulkOperationProvider _bulkOperationProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="CompanyMigrationService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="sqlDbContext">Instance of the TrustDbContext.</param>
        /// <param name="settingsManager">Instance of the SettingsManager.</param>
        /// <param name="jurisdictionsRepository">Instance of the JurisdictionsRepository.</param>
        /// <param name="activityLogManager">Instance of the ActivityLogManager.</param>
        /// <param name="legalEntitiesRepository">Instance of the LegalEntitiesRepository.</param>
        /// <param name="lockManager">Instance of the lock manager.</param>
        /// <param name="bulkOperationProvider">Provider for bulk operations.</param>
        public CompanyMigrationService(
            ILogger<CompanyMigrationService> logger,
            TrustDbContext sqlDbContext,
            ISettingsManager settingsManager,
            IJurisdictionsRepository jurisdictionsRepository,
            IActivityLogManager activityLogManager,
            ILegalEntitiesRepository legalEntitiesRepository,
            ILockManager lockManager,
            IBulkOperationProvider bulkOperationProvider)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _sqlDbContext = sqlDbContext ?? throw new ArgumentNullException(nameof(sqlDbContext));
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
            _jurisdictionsRepository = jurisdictionsRepository ?? throw new ArgumentNullException(nameof(jurisdictionsRepository));
            _activityLogManager = activityLogManager ?? throw new ArgumentNullException(nameof(activityLogManager));
            _legalEntitiesRepository = legalEntitiesRepository;
            _lockManager = lockManager;
            _bulkOperationProvider = Check.NotNull(bulkOperationProvider, nameof(bulkOperationProvider));
        }

        /// <summary>
        /// Handles a single company migration.
        /// </summary>
        /// <param name="company">The company to process.</param>
        /// <param name="migrationStartedByUser">The user who started the migration.</param>
        /// <param name="region">The region from which the data is being migrated.</param>
        /// <param name="jurisdiction">The jurisdiction.</param>
        /// <param name="defaultFeeSetting">The default fee settings for the jurisdiction.</param>
        /// <param name="jurisdictionModules">The modules available for the jurisdiction.</param>
        /// <returns>A tuple indicating success and any errors encountered.</returns>
        public async Task<(bool Success, List<string> Errors)> HandleCompanyAsync(Company company, ApplicationUser migrationStartedByUser, string region, Jurisdiction jurisdiction, FeeSettingsDTO defaultFeeSetting, List<ModuleDTO> jurisdictionModules)
        {
            ArgumentNullException.ThrowIfNull(company, nameof(company));
            ArgumentNullException.ThrowIfNull(defaultFeeSetting, nameof(defaultFeeSetting));
            ArgumentNullException.ThrowIfNull(migrationStartedByUser, nameof(migrationStartedByUser));

            var errors = new List<string>();
            var success = false;

            var companyVpCode = company.VpCode;

            // Check for existing LegalEntity
            var existingLegalEntity = await _sqlDbContext.Set<LegalEntity>()
                                                     .Include(le => le.LegalEntityModules)
                                                     .SingleOrDefaultAsync(le => le.Code == companyVpCode);

            if (existingLegalEntity != null)
            {
                _logger.LogInformation("LegalEntity with VP Code {Code} found. Updating...", companyVpCode);

                await UpdateLegalEntity(existingLegalEntity, company, defaultFeeSetting, migrationStartedByUser, region, jurisdictionModules);
                success = true;
            }
            else
            {
                _logger.LogInformation("LegalEntity with VP Code {Code} does not exist. Skipping...", companyVpCode);
                errors.Add($"LegalEntity with VP Code {companyVpCode} does not exist.");
            }

            return (success, errors);
        }

        /// <summary>
        /// Performs cleanup operations on companies after migration is complete.
        /// </summary>
        /// <param name="migrationRecord">The migration record containing migration details.</param>
        /// <param name="jobLock">The job lock to ensure exclusive access during cleanup.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous cleanup operation.</returns>
        public async Task CleanupCompaniesAsync(Domain.DataMigrations.DataMigration migrationRecord, LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(migrationRecord, nameof(migrationRecord));
            ArgumentNullException.ThrowIfNull(jobLock, nameof(jobLock));

            jobLock = await RefreshLockIfNeeded(jobLock);

            var companiesToDelete = await _sqlDbContext.Set<LegalEntity>()
                .Where(le =>
                    le.JurisdictionId == migrationRecord.JurisdictionId &&
                    (le.EntityStatus == LegalEntityStatusNames.Closed ||
                     le.EntityStatus == LegalEntityStatusNames.MarkedForDeletion) &&
                    !le.Submissions.Any()
                )
                .Include(le => le.BeneficialOwners)
                .Include(le => le.Directors)
                .ToArrayAsync();

            var companyIds = companiesToDelete.Select(le => le.Id).ToList();
            var companyCodes = companiesToDelete.Select(le => le.Code).ToList();

            var directorsHistory = await _sqlDbContext.Set<DirectorHistory>()
                                                      .Where(dir => companyIds.Contains(dir.LegalEntityId)).ToArrayAsync();

            var beneficialOwnersHistory = await _sqlDbContext.Set<BeneficialOwnerHistory>()
                                                      .Where(bo => companyIds.Contains(bo.LegalEntityId)).ToArrayAsync();

            var companyHistory = await _sqlDbContext.Set<LegalEntityHistory>()
                                                      .Where(le => companyCodes.Contains(le.Code)).ToArrayAsync();

            await RefreshLockIfNeeded(jobLock);
            await using var transaction = await _jurisdictionsRepository.DbContext.Database.BeginTransactionAsync();
            try
            {
                var syncExcludedCompanies = companiesToDelete
                                            .Select(le => new SyncExcludedLegalEntity(le.Code, le.LegacyCode, migrationRecord.Id))
                                            .ToArray();

                _logger.LogInformation("Inserting {Count} companies to SyncExcludedLegalEntity", syncExcludedCompanies.Length);
                await _bulkOperationProvider.BulkInsertAsync(syncExcludedCompanies, _sqlDbContext);
                _logger.LogInformation("Inserted {Count} companies to SyncExcludedLegalEntity", syncExcludedCompanies.Length);

                var beneficialOwners = companiesToDelete.SelectMany(le => le.BeneficialOwners).ToArray();
                _logger.LogInformation("Deleting {Count} beneficial owners", beneficialOwners.Length);
                await _bulkOperationProvider.BulkDeleteAsync(beneficialOwners, _sqlDbContext);
                _logger.LogInformation("Deleted {Count} beneficial owners", beneficialOwners.Length);

                _logger.LogInformation("Deleting {Count} beneficial owners history", beneficialOwnersHistory.Length);
                await _bulkOperationProvider.BulkDeleteAsync(beneficialOwnersHistory, _sqlDbContext);
                _logger.LogInformation("Deleted {Count} beneficial owners history", beneficialOwnersHistory.Length);

                var directors = companiesToDelete.SelectMany(le => le.Directors).ToArray();
                _logger.LogInformation("Deleting {Count} directors", directors.Length);
                await _bulkOperationProvider.BulkDeleteAsync(directors, _sqlDbContext);
                _logger.LogInformation("Deleted {Count} directors", directors.Length);

                _logger.LogInformation("Deleting {Count} directors history", directorsHistory.Length);
                await _bulkOperationProvider.BulkDeleteAsync(directorsHistory, _sqlDbContext);
                _logger.LogInformation("Deleted {Count} directors history", directorsHistory.Length);

                _logger.LogInformation("Deleting {Count} companies", companiesToDelete.Length);
                await _bulkOperationProvider.BulkDeleteAsync(companiesToDelete, _sqlDbContext);
                _logger.LogInformation("Deleted {Count} companies", companiesToDelete.Length);

                _logger.LogInformation("Deleting {Count} company history", companyHistory.Length);
                await _bulkOperationProvider.BulkDeleteAsync(companyHistory, _sqlDbContext);
                _logger.LogInformation("Deleted {Count} company history", companyHistory.Length);

                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error saving data (Tx rolled back)");
                throw;
            }
        }

        /// <summary>
        /// Updates an existing LegalEntity with company data.
        /// </summary>
        /// <param name="legalEntity">The existing LegalEntity to update.</param>
        /// <param name="company">The company data to update with.</param>
        /// <param name="defaultSetting">The default fee setting for the jurisdiction.</param>
        /// <param name="migrationStartedByUser">The user who started the migration.</param>
        /// <param name="region">The region from which the data is being migrated.</param>
        /// <param name="jurisdictionModules"></param>
        private async Task UpdateLegalEntity(LegalEntity legalEntity,
            Company company,
            FeeSettingsDTO defaultSetting,
            ApplicationUser migrationStartedByUser,
            string region,
            List<ModuleDTO> jurisdictionModules)
        {
            var changedInThisMigration = false;

            if (company.IsDeleted && legalEntity.IsActive)
            {
                _logger.LogDebug("Company {CompanyCode} is deleted. Deactivating LegalEntity {LegalEntityId}", company.Code, legalEntity.Id);
                legalEntity.SetInactive(company.DeletedAt);
                changedInThisMigration = true;
            }

            switch (legalEntity.OnboardingStatus)
            {
                // This means status in VP is Active/Closing, see LegalEntitiesDataManager.SyncLegalEntities
                case OnboardingStatus.Onboarding:
                    {
                        _logger.LogDebug(
                            "Setting onboarding status for legal entity '{Company}' from {CurrentOnboardingStatus} to Approved.",
                            company, legalEntity.OnboardingStatus.ToString());

                        legalEntity.ApproveOnboarding();
                        changedInThisMigration = true;
                        break;
                    }
                // This means it was closed during the initial sync. If it's VP status is "MarkedForDeletion", we ignore it; if it is "Closed" in VP
                // and it has submissions, we set it to Approved but not active.
                // If it has no submissions, we do not approve it, it will keep it's Unknown onboarding status and will be deleted in the PostMigrationCleanup/
                case OnboardingStatus.Unknown when legalEntity.EntityStatus is LegalEntityStatusNames.Closed:
                    {
                        var hasSubmissions = await _sqlDbContext.Set<Submission>()
                                                                .AnyAsync(submission =>
                                                                    submission.LegalEntityId == legalEntity.Id);

                        if (hasSubmissions)
                        {
                            _logger.LogDebug("Legal entity '{Company}' has submissions but is closed in VP. Setting to Approved but not setting to active.", company);

                            // Set approved but do not set to active
                            legalEntity.ApproveOnboarding();
                            legalEntity.SetInactive();
                            changedInThisMigration = true;
                        }
                        else
                        {
                            _logger.LogDebug("Legal entity '{Company}' is closed in VP and has no submissions. Not approving.", company);
                        }

                        break;
                    }
            }

            if (EnableModules(legalEntity, jurisdictionModules))
            {
                changedInThisMigration = true;
            }

            await _legalEntitiesRepository.UpdateAsync(legalEntity);

            if (company.Amount != defaultSetting.STRSubmissionFee || company.HasLatePaymentException)
            {
                var companySetting = new FeeSettingsDTO()
                {
                    STRSubmissionFee = company.Amount,
                    STRSubmissionFeeInvoiceText = null,
                    STRSubmissionLatePaymentFeeExempt = company.HasLatePaymentException
                };

                await _settingsManager.SaveSettingsForCompanyAsync(companySetting, legalEntity.Id);
                _logger.LogDebug("Saved custom fee setting for company {CompanyId}", legalEntity.Id);

                changedInThisMigration = true;
            }
            else
            {
                _logger.LogTrace("Company {CompanyId} uses default fee setting. No custom setting saved.", legalEntity.Id);
            }

            if (changedInThisMigration)
            {
                await _activityLogManager.AddActivityLogAsync(
                    migrationStartedByUser.Id,
                    legalEntity,
                    shortDescription: "LegalEntity migrated",
                    text:
                    $"Settings for LegalEntity '{legalEntity.Name}' migrated from {region} by user '{migrationStartedByUser.DisplayName}' at {DateTime.UtcNow:yyyy-MM-dd HH:mm}",
                    activityType: ActivityLogActivityTypes.LegalEntityMigrated,
                    saveChanges: true);
            }
        }

        private bool EnableModules(LegalEntity legalEntity, List<ModuleDTO> jurisdictionModules)
        {
            var changed = false;

            foreach (var jurisdictionModule in jurisdictionModules)
            {
                if (legalEntity.LegalEntityModules.All(m => m.ModuleId != jurisdictionModule.Id))
                {
                    var legalEntityModule = new LegalEntityModule(legalEntity.Id, jurisdictionModule.Id)
                    {
                        IsApproved = true,
                        IsEnabled = true
                    };
                    legalEntity.LegalEntityModules.Add(legalEntityModule);
                    changed = true;

                    _logger.LogDebug("Adding module {Module} to LegalEntity {LegalEntity}", jurisdictionModule.Key, legalEntity.Code);
                }
                else
                {
                    var legalEntityModule = legalEntity.LegalEntityModules.Single(m => m.ModuleId == jurisdictionModule.Id);
                    if (legalEntityModule.IsEnabled != true)
                    {
                        legalEntityModule.IsEnabled = true;
                        changed = true;

                        _logger.LogDebug("Enabling module {Module} for LegalEntity {LegalEntity}", jurisdictionModule.Key, legalEntity.Code);
                    }
                    if (legalEntityModule.IsApproved != true)
                    {
                        legalEntityModule.IsApproved = true;
                        changed = true;

                        _logger.LogDebug("Approving module {Module} for LegalEntity {LegalEntity}", jurisdictionModule.Key, legalEntity.Code);
                    }
                }
            }

            return changed;
        }

        private async Task<LockDTO> RefreshLockIfNeeded(LockDTO jobLock)
        {
            if (jobLock.ExpiresAt - DateTime.UtcNow < JobLockRefreshMargin && jobLock.Id.HasValue)
            {
                _logger.LogInformation("Lock is about to expire, refreshing lock");
                jobLock = await _lockManager.RefreshLockAsync(jobLock.Id.Value);
            }

            return jobLock;
        }
    }
}