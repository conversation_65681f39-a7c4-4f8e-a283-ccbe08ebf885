using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.Domain.Currencies;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Framework.Extensions;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.DataMigration.Models.Nevis;

namespace NetProGroup.Trust.DataMigration.Services.Nevis
{
    /// <summary>
    /// Service for managing invoice-related operations during data migration.
    /// </summary>
    public class InvoiceMigrationService
    {
        private const string UsdCurrencyCode = "USD";
        private const string TridentTrustCompanyName = "Trident Trust Company (Nevis)";
        private const string MorningStarHoldingsName = "Morning Star Holdings Limited";
        private const string LateFilingFeeDescription = "Late filing fee";

        private readonly ICurrenciesRepository _currencyRepository;
        private readonly DataMigrationAppSettings _dataMigrationAppSettings;
        private readonly ILogger<InvoiceMigrationService> _logger;
        private Currency _cachedUsdCurrency;

        /// <summary>
        /// Initializes a new instance of the <see cref="InvoiceMigrationService"/> class.
        /// </summary>
        /// <param name="currencyRepository">The currency repository.</param>
        /// <param name="appSettings">The application settings.</param>
        /// <param name="logger">The logger.</param>
        public InvoiceMigrationService(
            ICurrenciesRepository currencyRepository,
            IOptions<DataMigrationAppSettings> appSettings,
            ILogger<InvoiceMigrationService> logger)
        {
            _currencyRepository = currencyRepository ?? throw new ArgumentNullException(nameof(currencyRepository));
            _dataMigrationAppSettings = appSettings?.Value ?? throw new ArgumentNullException(nameof(appSettings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates an invoice if needed based on the entry data.
        /// </summary>
        /// <param name="entry">The entry data.</param>
        /// <param name="submission">The submission associated with the invoice.</param>
        /// <param name="legalEntity">The legal entity associated with the invoice.</param>
        /// <param name="financialYear">The financial year for the invoice.</param>
        /// <returns>The created invoice, or null if no invoice was needed.</returns>
        public async Task<Invoice> CreateInvoiceIfNeededAsync(Entry entry, Submission submission, LegalEntity legalEntity, int financialYear)
        {
            ArgumentNullException.ThrowIfNull(entry, nameof(entry));
            ArgumentNullException.ThrowIfNull(submission, nameof(submission));
            try
            {
                if (financialYear >= 2024)
                {
                    _logger.LogWarning("Not creating invoice for entry. Company: {CompanyCode}, Year: {PeriodYear}. Financial year is 2024 or later.", entry.Company, entry.PeriodYear);
                    return null;
                }

                if (!entry.InvoiceNumber.IsNullOrWhiteSpace())
                {
                    _logger.LogTrace("Creating invoice for entry. Company: {CompanyCode}, Year: {PeriodYear}", entry.Company, entry.PeriodYear);

                    var currency = await GetUsdCurrencyAsync();
                    var invoice = CreateOrUpdateInvoice(submission, legalEntity, entry, financialYear, currency);

                    AddInvoiceLines(invoice, entry, currency);

                    invoice.Amount = invoice.InvoiceLines.Sum(line => line.Amount);
                    invoice.ExportedAt = entry.InvoiceExportDate;

                    _logger.LogDebug("Created invoice {InvoiceNumber} for company {CompanyCode}, year {PeriodYear}", invoice.InvoiceNr, entry.Company, entry.PeriodYear);
                    return invoice;
                }

                _logger.LogTrace("No invoice needed for entry. Company: {CompanyCode}, Year: {PeriodYear}", entry.Company, entry.PeriodYear);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice for entry. Company: {CompanyCode}, Year: {PeriodYear}", entry.Company, entry.PeriodYear);
                throw;
            }
        }

        private async Task<Currency> GetUsdCurrencyAsync()
        {
            if (_cachedUsdCurrency != null)
            {
                return _cachedUsdCurrency;
            }

            _cachedUsdCurrency = await _currencyRepository.FindFirstOrDefaultByConditionAsync(c => c.Code == UsdCurrencyCode);
            if (_cachedUsdCurrency == null)
            {
                _logger.LogError("Currency not found. Code: {CurrencyCode}", UsdCurrencyCode);
                throw new InvalidOperationException($"Currency with code {UsdCurrencyCode} not found");
            }
            return _cachedUsdCurrency;
        }

        private Invoice CreateOrUpdateInvoice(Submission submission, LegalEntity legalEntity, Entry entry, int financialYear, Currency currency)
        {
            var invoice = submission.Invoice ??= new Invoice();

            invoice.Status = entry.Payment != null ? InvoiceStatus.Paid : InvoiceStatus.Pending;
            invoice.Currency = currency;
            invoice.Date = entry.SubmittedAt.Value;
            invoice.FinancialYear = financialYear;
            invoice.InvoiceNr = entry.InvoiceNumber;
            invoice.LegalEntity = legalEntity;
            invoice.Layout = UseNewBranding(entry)
                ? LayoutConsts.TridentTrust
                : LayoutConsts.MorningStar;

            return invoice;
        }

        private void AddInvoiceLines(Invoice invoice, Entry entry, Currency currency)
        {
            var companyName = UseNewBranding(entry)
                ? TridentTrustCompanyName
                : MorningStarHoldingsName;

            invoice.InvoiceLines.Clear();
            invoice.InvoiceLines.Add(new InvoiceLine
            {
                Amount = entry.CompanyData.Amount,
                Currency = currency,
                Description = $"Fee for submission of the CIT-101 Simplified Tax Return (STR) for {entry.PeriodYear} Tax Year via the {companyName} Portal",
                Sequence = 1,
                ArticleNr = InvoiceLineFee.RegularFee
            });

            if (entry.LatePaymentFees != null)
            {
                _logger.LogDebug("Adding late payment fee for invoice {InvoiceNumber}", invoice.InvoiceNr);
                invoice.InvoiceLines.Add(new InvoiceLine
                {
                    Amount = entry.LatePaymentFees.Value,
                    Currency = currency,
                    Description = LateFilingFeeDescription,
                    Sequence = 2,
                    ArticleNr = InvoiceLineFee.LateFee
                });
            }
        }

        /// <summary>
        /// Determines whether to use the new branding for the invoice layout.
        /// 
        /// In the old portal the layout of the submission is based on the use_new_branding property, but the layout of the invoice was based on the created date.
        /// So we are using the created date to determine the layout of the invoice.
        ///
        /// See https://dev.azure.com/netprogroup/Trident%20-%20Substance/_git/TNEV%20-%20Client%20Portal?path=/controllers/pdfController.js for the original implementation.
        /// </summary>
        /// <param name="entry"></param>
        /// <returns>Whether to use the new branding (Trident) instead of the old (Morningstar)</returns>
        private bool UseNewBranding(Entry entry)
        {
            return entry.CreatedAt.Date > _dataMigrationAppSettings.UseNewBrandingLimitDate;
        }
    }
}
