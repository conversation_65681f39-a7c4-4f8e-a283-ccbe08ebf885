﻿// <copyright file="FormDocumentAttribute.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Tools;

namespace NetProGroup.Trust.Domain.Forms
{
    /// <summary>
    /// Represents an attribute entity for a FormDocument in the database.
    /// </summary>
    public class FormDocumentAttribute : StampedEntity<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentAttribute"/> class with the specified parameters.
        /// </summary>
        /// <param name="id">The unique identifier for the attribute.</param>
        /// <param name="key">The key to reference the attribute.</param>
        /// <param name="name">The name of the setting (informational purposes).</param>
        public FormDocumentAttribute(Guid id, string key, string name)
        {
            Check.NotDefaultOrNull<Guid>(id, nameof(id));
            Check.NotNullOrWhiteSpace(key, nameof(key));
            Check.NotNullOrWhiteSpace(name, nameof(name));

            Id = id;
            Key = key;
            Name = name;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentAttribute"/> class with the specified parameters.
        /// </summary>
        /// <param name="key">The key to reference the attribute.</param>
        /// <param name="name">The name of the setting (informational purposes).</param>
        public FormDocumentAttribute(string key, string name)
        {
            Check.NotNullOrWhiteSpace(key, nameof(key));
            Check.NotNullOrWhiteSpace(name, nameof(name));

            Key = key;
            Name = name;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FormDocumentAttribute"/> class.
        /// Private constructor to restrict instantiation.
        /// </summary>
        private FormDocumentAttribute()
        {
        }

        /// <summary>
        /// Gets or sets the key of the setting so it can be referenced textual.
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets the name of the setting for informational purposes.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the string representation of the value of the setting.
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the id of the FormDocument.
        /// </summary>
        public Guid FormDocumentId { get; set; }

        /// <summary>
        /// Gets or sets the FormDocument.
        /// </summary>
        public virtual FormDocument FormDocument { get; set; }
    }
}
