// <copyright file="Jurisdiction.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.EFAuditing;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Provider;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Domain.Jurisdictions
{
    /// <summary>
    /// Represents a Jurisdiction entity in the database.
    /// </summary>
    public class Jurisdiction : StampedEntity<Guid>, IAuditableEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Jurisdiction"/> class.
        /// </summary>
        public Jurisdiction()
            : base()
        {
            MasterClients = new HashSet<MasterClient>();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Jurisdiction"/> class.
        /// </summary>
        /// <param name="id">Id of the entity to get.</param>
        public Jurisdiction(Guid id)
           : base(id)
        {
            MasterClients = new HashSet<MasterClient>();
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Jurisdiction"/> class.
        /// </summary>
        /// <param name="id">Id of the entity to get.</param>
        /// <param name="code">Code of the jurisdiction.</param>
        /// <param name="name">Name of the jurisdiction.</param>
        public Jurisdiction(Guid id, string code, string name)
           : base(id)
        {
            MasterClients = new HashSet<MasterClient>();

            Code = code;
            Name = name;
        }

        /// <summary>
        /// Gets or sets the name of the jurisdiction.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the code of the jurisdiction.
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Gets the collection with MasterClients.
        /// </summary>
        /// <value>A collection of <see cref="MasterClient"/>.</value>
        public virtual ICollection<MasterClient> MasterClients { get; }

        /// <summary>
        /// Gets the collection of tax rates for the jurisdiction.
        /// </summary>
        public virtual ICollection<JurisdictionTaxRate> TaxRates { get; set; } = new HashSet<JurisdictionTaxRate>();

        /// <summary>
        /// Gets the collection with JurisdictionModules.
        /// </summary>
        /// <value>A collection of <see cref="JurisdictionModule"/>.</value>
        public virtual ICollection<JurisdictionModule> JurisdictionModules { get; }

        /// <summary>
        /// Gets or sets the collection of payment providers associated with this entity.
        /// This represents the available payment providers that can process the transaction.
        /// </summary>
        public ICollection<PaymentProvider> PaymentProviders { get; set; }

        /// <summary>
        /// Gets or sets the payments associated with this jurisdiction.
        /// </summary>
        public ICollection<Payment> Payments { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the initial synchronization has been completed.
        /// </summary>
        public bool InitialSyncCompleted { get; set; }

        /// <summary>
        /// Gets the list of years for the annual fees for the given jurisdiction.
        /// </summary>
        /// <returns>A list of years.</returns>
        public List<int> GetAnnualFeeYears()
        {
            var result = new List<int>();

            var endYear = DateTime.Today.Year;

            var startYear = Code switch
            {
                JurisdictionCodes.Nevis => 2024,
                _ => 2019
            };

            for (var year = startYear; year <= endYear; year++)
            {
                result.Add(year);
            }

            return result;
        }
    }
}
