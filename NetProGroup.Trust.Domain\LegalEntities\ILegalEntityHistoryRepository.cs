﻿// <copyright file="ILegalEntityHistoryRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.LegalEntities
{
    /// <summary>
    /// Interface for the LegalEntityHistory repository.
    /// </summary>
    public interface ILegalEntityHistoryRepository : IRepository<LegalEntityHistory, Guid>, IRepositoryService
    {
        /// <summary>
        /// Lists all most recent companies for the given jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of the most recent legal entity histories for the specified jurisdiction.</returns>
        Task<IList<LegalEntityHistory>> ListCurrentCompaniesByJurisdictionIdAsync(Guid jurisdictionId);

        /// <summary>
        /// Gets the most recent version of a company identified by the jurisdictionId and code.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction.</param>
        /// <param name="code">The code of the company.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the most recent legal entity history for the specified company.</returns>
        Task<LegalEntityHistory> GetCurrentCompanyByEntityCodeAsync(Guid jurisdictionId, string code);
    }
}
