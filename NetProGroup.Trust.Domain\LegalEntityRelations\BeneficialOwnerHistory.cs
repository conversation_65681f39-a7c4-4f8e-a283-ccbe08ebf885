﻿// <copyright file="BeneficialOwnerHistory.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Domain.LegalEntityRelations;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Domain.LegalEntities
{
    /// <summary>
    /// Represents a BeneficialOwner entity in the database.
    /// </summary>
    public class BeneficialOwnerHistory : StampedEntity<Guid>, IMetaData
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BeneficialOwnerHistory"/> class.
        /// </summary>
        public BeneficialOwnerHistory()
            : base()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="BeneficialOwnerHistory"/> class.
        /// </summary>
        /// <param name="id">Id of the entity to get.</param>
        public BeneficialOwnerHistory(Guid id)
           : base(id)
        {
        }

        /// <summary>
        /// Gets or sets the id of the legal entity that this relation belongs to.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the Legal Entity that this relation belongs to.
        /// </summary>
        public virtual LegalEntity LegalEntity { get; set; }

        /// <summary>
        /// Gets or sets the unique external relation id of the BO.
        /// </summary>
        public string ExternalUniqueId { get; set; }

        /// <summary>
        /// Gets or sets the code of the BO.
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the companynumber.
        /// </summary>
        public string CompanyNumber { get; set; }

        /// <summary>
        /// Gets or sets the name of the BO.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the FormerName of the BO.
        /// </summary>
        public string FormerName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the relation is an indiviual (as opposed to 'Company').
        /// </summary>
        /// <remarks>
        /// Also known as 'FileType'.
        /// </remarks>
        public bool IsIndividual { get; set; }

        /// <summary>
        /// Gets or sets the FileType (indicates whether it is an individual or company).
        /// </summary>
        public string FileType { get; set; }

        /// <summary>
        /// Gets or sets the officer type code.
        /// </summary>
        public string OfficerTypeCode { get; set; }

        /// <summary>
        /// Gets or sets the officer type name.
        /// </summary>
        public string OfficerTypeName { get; set; }

        /// <summary>
        /// Gets or sets the DateOfBirth of the BO.
        /// </summary>
        /// <remarks>
        /// For individual BO.
        /// </remarks>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the place of birth of the BO.
        /// </summary>
        /// <remarks>
        /// For individual BO.
        /// </remarks>
        public string PlaceOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the country of birth of the BO.
        /// </summary>
        /// <remarks>
        /// For individual BO.
        /// </remarks>
        public string CountryOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the country code of birth of the BO.
        /// </summary>
        /// <remarks>
        /// For individual BO.
        /// </remarks>
        public string CountryOfBirthCode { get; set; }

        /// <summary>
        /// Gets or sets the nationality of the BO.
        /// </summary>
        /// <remarks>
        /// For individual BO.
        /// </remarks>
        public string Nationality { get; set; }

        /// <summary>
        /// Gets or sets the TIN (Tax Identification Number) of the BO.
        /// </summary>
        /// <remarks>
        /// For all BOs.
        /// </remarks>
        public string TIN { get; set; }

        /// <summary>
        /// Gets or sets the Incorporation Number of the BO.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public string IncorporationNr { get; set; }

        /// <summary>
        /// Gets or sets the date of incorporation of the BO.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public DateTime? IncorporationDate { get; set; }

        /// <summary>
        /// Gets or sets the residential address of the BO.
        /// </summary>
        /// <remarks>
        /// For individual BO.
        /// </remarks>
        public string ResidentialAddress { get; set; }

        /// <summary>
        /// Gets or sets the address of the BO.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public string Address { get; set; }

        /// <summary>
        /// Gets or sets the serviceaddress of the BO.
        /// </summary>
        public string ServiceAddress { get; set; }

        /// <summary>
        /// Gets or sets the country of formation of the BO.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public string Country { get; set; }

        /// <summary>
        /// Gets or sets the name of regulator of the BO.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public string NameOfRegulator { get; set; }

        /// <summary>
        /// Gets or sets the jurisdiction of regulator of the BO.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public string JurisdictionOfRegulator { get; set; }

        /// <summary>
        /// Gets or sets the sovereign state of the BO.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public string SovereignState { get; set; }

        /// <summary>
        /// Gets or sets the stockexchange of the BO.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public string StockExchangeName { get; set; }

        /// <summary>
        /// Gets or sets the stockcode of the BO.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public string StockExchangeCode { get; set; }

        /// <summary>
        /// Gets or sets the appointmentdate of the BO (from).
        /// </summary>
        /// <remarks>
        /// For all Directors.
        /// </remarks>
        public DateTime? AppointmentDate { get; set; }

        /// <summary>
        /// Gets or sets the cessationdate of the BO (To).
        /// </summary>
        /// <remarks>
        /// For all Directors.
        /// </remarks>
        public DateTime? CessationDate { get; set; }

        /// <summary>
        /// Gets or sets the BeneficialOwnerHistory.
        /// </summary>
        public Guid? BeneficialOwnerId { get; set; }

        /// <summary>
        /// Gets or sets the BeneficialOwner.
        /// </summary>
        public virtual BeneficialOwner BeneficialOwner { get; set; }

        /// <summary>
        /// Gets or sets the status of the relation.
        /// </summary>
        public LegalEntityRelationStatus Status { get; set; }

        /// <summary>
        /// Gets or sets when the data was received.
        /// </summary>
        public DateTime? ReceivedAt { get; set; }

        /// <summary>
        /// Gets or sets when the data was received.
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// Gets or sets the id of the user that confirmed the data.
        /// </summary>
        public Guid? ConfirmedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the user that confirmed the data.
        /// </summary>
        public ApplicationUser ConfirmedByUser { get; set; }

        /// <summary>
        /// Gets or sets when an update of the data is requested.
        /// </summary>
        public DateTime? UpdateRequestedAt { get; set; }

        /// <summary>
        /// Gets or sets the id of the user that created the update request.
        /// </summary>
        public Guid? UpdateRequestedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the user that created the update request.
        /// </summary>
        public ApplicationUser UpdateRequestedByUser { get; set; }

        /// <summary>
        /// Gets or sets the type of update request for the relation.
        /// </summary>
        public LegalEntityRelationUpdateRequestType? UpdateRequestType { get; set; }

        /// <summary>
        /// Gets or sets the comments for the update request.
        /// </summary>
        public string UpdateRequestComments { get; set; }
    }
}
