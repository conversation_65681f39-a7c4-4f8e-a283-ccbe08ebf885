﻿// <copyright file="DirectorHistory.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Domain.LegalEntityRelations;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Domain.LegalEntities
{
    /// <summary>
    /// Represents a DirectorHistory entity in the database.
    /// </summary>
    public class DirectorHistory : StampedEntity<Guid>, IMetaData
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DirectorHistory"/> class.
        /// </summary>
        public DirectorHistory()
            : base()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DirectorHistory"/> class.
        /// </summary>
        /// <param name="id">Id of the entity to get.</param>
        public DirectorHistory(Guid id)
           : base(id)
        {
        }

        /// <summary>
        /// Gets or sets the id of the legal entity that this relation belongs to.
        /// </summary>
        public Guid LegalEntityId { get; set; }

        /// <summary>
        /// Gets or sets the Legal Entity that this relation belongs to.
        /// </summary>
        public virtual LegalEntity LegalEntity { get; set; }

        /// <summary>
        /// Gets or sets the Id of the Director (the PCP director entity).
        /// </summary>
        public Guid? DirectorId { get; set; }

        /// <summary>
        /// Gets or sets the unique external relation id of the Director.
        /// </summary>
        public string ExternalUniqueId { get; set; }

        /// <summary>
        /// Gets or sets the Director that this relation belongs to.
        /// </summary>
        public virtual Director Director { get; set; }

        /// <summary>
        /// Gets or sets the code of the Director.
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the companynumber.
        /// </summary>
        public string CompanyNumber { get; set; }

        /// <summary>
        /// Gets or sets the name of the Director.
        /// </summary>
        /// <remarks>
        /// For all Directors.
        /// </remarks>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the former name of the Director.
        /// </summary>
        public string FormerName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the relation is an individual.
        /// </summary>
        public bool IsIndividual { get; set; }

        /// <summary>
        /// Gets or sets the FileType (indicates whether it is an individual or company).
        /// </summary>
        public string FileType { get; set; }

        /// <summary>
        /// Gets or sets the type of relation.
        /// </summary>
        public string RelationType { get; set; }

        /// <summary>
        /// Gets or sets the name of the type of officer.
        /// </summary>
        public string OfficerTypeName { get; set; }

        /// <summary>
        /// Gets or sets the appointmentdate of the Director (from).
        /// </summary>
        /// <remarks>
        /// For all Directors.
        /// </remarks>
        public DateTime? AppointmentDate { get; set; }

        /// <summary>
        /// Gets or sets the cessationdate of the Director (To).
        /// </summary>
        /// <remarks>
        /// For all Directors.
        /// </remarks>
        public DateTime? CessationDate { get; set; }

        /// <summary>
        /// Gets or sets the DateOfBirth of the Director.
        /// </summary>
        /// <remarks>
        /// For individual Director.
        /// </remarks>
        public DateTime? DateOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the place of birth of the Director.
        /// </summary>
        /// <remarks>
        /// For individual Director.
        /// </remarks>
        public string PlaceOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the country of birth of the Director.
        /// </summary>
        /// <remarks>
        /// For individual Director.
        /// </remarks>
        public string CountryOfBirth { get; set; }

        /// <summary>
        /// Gets or sets the country code of birth of the Director.
        /// </summary>
        /// <remarks>
        /// For individual Director.
        /// </remarks>
        public string CountryOfBirthCode { get; set; }

        /// <summary>
        /// Gets or sets the nationality of the Director.
        /// </summary>
        /// <remarks>
        /// For individual Director.
        /// </remarks>
        public string Nationality { get; set; }

        /// <summary>
        /// Gets or sets the residential address of the Director.
        /// </summary>
        /// <remarks>
        /// For individual BO.
        /// </remarks>
        public string ResidentialAddress { get; set; }

        /// <summary>
        /// Gets or sets the address of the Director.
        /// </summary>
        /// <remarks>
        /// For corporate BO.
        /// </remarks>
        public string Address { get; set; }

        /// <summary>
        /// Gets or sets the serviceaddress of the Director.
        /// </summary>
        public string ServiceAddress { get; set; }

        /// <summary>
        /// Gets or sets the TIN (Tax Identification Number) of the Director.
        /// </summary>
        /// <remarks>
        /// For all directors.
        /// </remarks>
        public string TIN { get; set; }

        /// <summary>
        /// Gets or sets the Incorporation Number of the Director.
        /// </summary>
        /// <remarks>
        /// For corporate Director.
        /// </remarks>
        public string IncorporationNr { get; set; }

        /// <summary>
        /// Gets or sets the date of incorporation of the Director.
        /// </summary>
        /// <remarks>
        /// For corporate Director.
        /// </remarks>
        public DateTime? IncorporationDate { get; set; }

        /// <summary>
        /// Gets or sets the place of incorporation.
        /// </summary>
        /// <remarks>
        /// For corporate Director.
        /// </remarks>
        public string IncorporationPlace { get; set; }

        /// <summary>
        /// Gets or sets the country of incorporation.
        /// </summary>
        /// <remarks>
        /// For corporate Director.
        /// </remarks>
        public string IncorporationCountry { get; set; }

        /// <summary>
        /// Gets or sets the country code of incorporation.
        /// </summary>
        /// <remarks>
        /// For corporate Director.
        /// </remarks>
        public string IncorporationCountryCode { get; set; }

        /// <summary>
        /// Gets or sets the country of incorporation or birth of the Director.
        /// </summary>
        /// <remarks>
        /// For corporate Director.
        /// </remarks>
        public string Country { get; set; }

        /// <summary>
        /// Gets or sets the code of the director if this director is an alternate.
        /// </summary>
        public string DirectorIsAlternateToCode { get; set; }

        /// <summary>
        /// Gets or sets the name of the director if this director is an alternate.
        /// </summary>
        public string DirectorIsAlternateToName { get; set; }

        /// <summary>
        /// Gets or sets DirectorCapacity.
        /// </summary>
        public string DirectorCapacity { get; set; }

        /// <summary>
        /// Gets or sets DirectorID.
        /// </summary>
        public string VPDirectorID { get; set; }

        /// <summary>
        /// Gets or sets LicenseeEntityCode.
        /// </summary>
        public string LicenseeEntityCode { get; set; }

        /// <summary>
        /// Gets or sets LicenseeEntityName.
        /// </summary>
        public string LicenseeEntityName { get; set; }

        /// <summary>
        /// Gets or sets the status of the relation.
        /// </summary>
        public LegalEntityRelationStatus Status { get; set; }

        /// <summary>
        /// Gets or sets when the data was received.
        /// </summary>
        public DateTime? ReceivedAt { get; set; }

        /// <summary>
        /// Gets or sets when the data was received.
        /// </summary>
        public DateTime? ConfirmedAt { get; set; }

        /// <summary>
        /// Gets or sets the id of the user that confirmed the data.
        /// </summary>
        public Guid? ConfirmedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the user that confirmed the data.
        /// </summary>
        public ApplicationUser ConfirmedByUser { get; set; }

        /// <summary>
        /// Gets or sets when an update of the data is requested.
        /// </summary>
        public DateTime? UpdateRequestedAt { get; set; }

        /// <summary>
        /// Gets or sets the id of the user that created the update request.
        /// </summary>
        public Guid? UpdateRequestedByUserId { get; set; }

        /// <summary>
        /// Gets or sets the user that created the update request.
        /// </summary>
        public ApplicationUser UpdateRequestedByUser { get; set; }

        /// <summary>
        /// Gets or sets the type of update request for the relation.
        /// </summary>
        public LegalEntityRelationUpdateRequestType? UpdateRequestType { get; set; }

        /// <summary>
        /// Gets or sets the comments for the update request.
        /// </summary>
        public string UpdateRequestComments { get; set; }
    }
}
