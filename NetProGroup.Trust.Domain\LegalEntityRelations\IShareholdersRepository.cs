﻿// <copyright file="IShareholdersRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.LegalEntities
{
    /// <summary>
    /// Interface for the Shareholder repository.
    /// </summary>
    public interface IShareholdersRepository : IRepository<Shareholder, Guid>, IRepositoryService;
}
