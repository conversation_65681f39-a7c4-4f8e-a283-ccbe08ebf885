﻿// <copyright file="IMasterClientUsersRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.MasterClients
{
    /// <summary>
    /// Interface for the MasterClientUser repository.
    /// </summary>
    public interface IMasterClientUsersRepository : IRepository<MasterClientUser, Guid>, IRepositoryService;
}
