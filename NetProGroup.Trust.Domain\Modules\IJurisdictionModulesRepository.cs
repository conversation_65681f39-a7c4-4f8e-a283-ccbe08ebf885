﻿// <copyright file="IJurisdictionModulesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.Modules
{
    /// <summary>
    /// Interface for the JurisdictionModules repository.
    /// </summary>
    public interface IJurisdictionModulesRepository : IRepository<JurisdictionModule, Guid>, IRepositoryService;
}
