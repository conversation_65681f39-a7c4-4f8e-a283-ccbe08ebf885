﻿// <copyright file="IModulesRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.Modules
{
    /// <summary>
    /// Interface for the Modules repository.
    /// </summary>
    public interface IModulesRepository : IRepository<Module, Guid>, IRepositoryService;
}
