// <copyright file="Module.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Framework.Services.EFAuditing;
using NetProGroup.Framework.Tools;

namespace NetProGroup.Trust.Domain.Modules
{
    /// <summary>
    /// Represents a Module entity in the database.
    /// </summary>
    public class Module : StampedEntity<Guid>, IAuditableEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Module"/> class.
        /// Private constructor to restrict instantiation.
        /// </summary>
        private Module()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Module"/> class with the specified parameters.
        /// </summary>
        /// <param name="id">The unique identifier for the module.</param>
        /// <param name="key">The key to reference the module.</param>
        /// <param name="name">The name of the module.</param>
        public Module(Guid id, string key, string name)
        {
            Check.NotDefaultOrNull<Guid>(id, nameof(id));
            Check.NotNullOrWhiteSpace(key, nameof(key));
            Check.NotNullOrWhiteSpace(name, nameof(name));

            Id = id;
            Key = key;
            Name = name;

            IsActive = true;
        }

        /// <summary>
        /// Gets or sets the name of the module.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the key of the module so it can be referenced textual.
        /// </summary>
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the module is active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets the collection with JurisdictionModules.
        /// </summary>
        /// <value>A collection of <see cref="JurisdictionModule"/>.</value>
        public virtual ICollection<JurisdictionModule> JurisdictionModules { get; } = new List<JurisdictionModule>();

        /// <summary>
        /// Gets the collection with LegalEntityModules.
        /// </summary>
        /// <value>A collection of <see cref="LegalEntityModule"/>.</value>
        public virtual ICollection<LegalEntityModule> LegalEntityModules { get; } = new List<LegalEntityModule>();
    }
}
