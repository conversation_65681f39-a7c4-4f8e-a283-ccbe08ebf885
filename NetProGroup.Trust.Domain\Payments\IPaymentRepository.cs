// <copyright file="IPaymentRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.Payments.Invoices
{
    /// <summary>
    /// Payment repository interface.
    /// </summary>
    public interface IPaymentRepository : IRepository<Payment, Guid>, IRepositoryService
    {
        /// <summary>
        /// Gets the DbContext of the repository.
        /// </summary>
        DbContext DbContext { get; }

        /// <summary>
        /// Gets the active payment for the invoices.
        /// </summary>
        /// <param name="invoiceIds">The invoice IDs.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public Task<Payment> GetActivePaymentForInvoices(IEnumerable<Guid> invoiceIds);

        /// <summary>
        /// Soft-deletes a payment.
        /// </summary>
        /// <param name="payment">The payment to soft-delete.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public Task<Payment> SoftDeletePayment(Payment payment);
    }
}