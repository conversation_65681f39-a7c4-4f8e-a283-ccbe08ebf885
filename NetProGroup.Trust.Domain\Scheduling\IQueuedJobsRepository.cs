﻿// <copyright file="IQueuedJobsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.Scheduling
{
    /// <summary>
    /// Interface for the QueuedJob repository.
    /// </summary>
    public interface IQueuedJobsRepository : IRepository<QueuedJob, Guid>, IRepositoryService;
}
