﻿// <copyright file="IScheduledJobsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.Scheduling
{
    /// <summary>
    /// Interface for the ScheduledJob repository.
    /// </summary>
    public interface IScheduledJobsRepository : IRepository<ScheduledJob, Guid>, IRepositoryService;
}
