﻿// <copyright file="ISettingsRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.EF.Repository.Interfaces;

namespace NetProGroup.Trust.Domain.Settings
{
    /// <summary>
    /// Interface for the settings repository.
    /// </summary>
    public interface ISettingsRepository : IRepository<Setting, Guid>, IRepositoryService;
}
