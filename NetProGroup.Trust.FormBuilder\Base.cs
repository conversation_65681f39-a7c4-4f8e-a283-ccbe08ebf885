// <copyright file="Base.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms
{
    /// <summary>
    /// Represents the base class for form elements.
    /// </summary>
    public abstract class Base
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Base"/> class.
        /// </summary>
        /// <param name="id">The unique identifier for the form element.</param>
        protected Base(string id)
        {
            Id = id;
        }

        /// <summary>
        /// Gets or sets the identification of the form element.
        /// </summary>
        [JsonPropertyOrder(10)]
        public string Id { get; set; }
    }
}
