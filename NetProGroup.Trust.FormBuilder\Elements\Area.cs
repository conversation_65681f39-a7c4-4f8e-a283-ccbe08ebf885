﻿// <copyright file="Area.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Forms.Extensions;
using NetProGroup.Trust.Forms.Types;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms.Elements
{
    /// <summary>
    /// Represents an area with other fields that can be visible/hidden or enabled/disabled base on values/selections in other fields.
    /// </summary>
    public class Area : ElementBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Area"/> class.
        /// </summary>
        /// <param name="id">The unique identifier for the area.</param>
        public Area(string id)
           : base(id)
        {
        }

        /// <summary>
        /// Gets or sets the full list of elements in this area.
        /// </summary>
        [JsonPropertyOrder(100)]
        public List<ElementBase> Elements { get; set; } = new List<ElementBase>();

        /// <summary>
        /// Gets or sets the label for this area (use as caption).
        /// </summary>
        [JsonPropertyOrder(110)]
        public string Label { get; set; }

        /// <summary>
        /// Gets or sets the hint for this area.
        /// </summary>
        [JsonPropertyOrder(111)]
        public string Hint { get; set; }

        /// <summary>
        /// Adds a field to the area.
        /// </summary>
        /// <param name="id">Id of the field.</param>
        /// <param name="type">Type of the field.</param>
        /// <param name="label">The label for the field.</param>
        /// <returns>The created field.</returns>
        public Field AddField(string id, FieldType type, string label)
        {
            return Elements.AddField(id, type, label);
        }
    }
}
