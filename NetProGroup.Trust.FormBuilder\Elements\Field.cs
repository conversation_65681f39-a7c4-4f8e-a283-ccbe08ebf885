﻿// <copyright file="Field.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Forms.MetaData;
using NetProGroup.Trust.Forms.Types;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms.Elements
{
    /// <summary>
    /// Represents a field to display to the user. This can be a i.e. a text field, checkbox, radio button etc.
    /// </summary>
    public class Field
        : ElementBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Field"/> class.
        /// </summary>
        public Field() : this("", FieldType.Text)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Field"/> class.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="type">The field type.</param>
        public Field(string id, FieldType type) : base(id)
        {
            FieldType = type;
        }

        /// <summary>
        /// Gets or sets the type of field.
        /// </summary>
        [JsonPropertyOrder(100)]
        public FieldType FieldType { get; set; }

        /// <summary>
        /// Gets or sets the label for this field.
        /// </summary>
        [JsonPropertyOrder(110)]
        public string Label { get; set; }

        /// <summary>
        /// Gets or sets the hint for this field.
        /// </summary>
        [JsonPropertyOrder(111)]
        public string Hint { get; set; }

        /// <summary>
        /// Gets or sets the key of this field which is used to store the value in the database.
        /// </summary>
        [JsonPropertyOrder(120)]
        public string Key { get; set; }

        /// <summary>
        /// Gets or sets the value. For a template, this is the default value.
        /// Formatting is base on XmlConvert.
        /// </summary>
        /// <remarks>
        /// In case of a multiselect field, the value is comma separated.
        /// </remarks>
        [JsonPropertyOrder(130)]
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the type of value. Defaults to String.
        /// </summary>
        [JsonPropertyOrder(140)]
        public Types.ValueType ValueType { get; set; } = Types.ValueType.String;

        /// <summary>
        /// Gets or sets the name of the column in the corresponding DataRow in a DataSet.
        /// </summary>
        /// <remarks>
        /// Normally used when in a Dialog to read existing data from a DataRow or to write data to a DataRow.
        /// </remarks>
        [JsonPropertyOrder(150)]
        public string ColumnName { get; set; }

        /// <summary>
        /// Gets or sets the id of the variable to write the value in.
        /// </summary>
        /// <remarks>
        /// Normally used when in a Dialog to read or write data to a non-field.
        /// </remarks>
        [JsonPropertyOrder(160)]
        public string Variable { get; set; }

        /// <summary>
        /// Gets or sets the conditions for this field.
        /// </summary>
        [JsonPropertyOrder(200)]
        public List<Condition> Conditions { get; set; }

        /// <summary>
        /// Gets or sets the validation rules for this field.
        /// </summary>
        [JsonPropertyOrder(210)]
        public Dictionary<string, object> ValidationRules { get; set; }

        /// <summary>
        /// Gets or sets the messages for this field when limits or conditions do not meet.
        /// </summary>
        [JsonPropertyOrder(220)]
        public Dictionary<string, object> ErrorMessages { get; set; }

        /// <summary>
        /// Gets or sets the options for a select field.
        /// </summary>
        [JsonPropertyOrder(230)]
        public List<SelectOption> SelectOptions { get; set; }

        /// <summary>
        /// Gets or sets the id of the dataset to use for the select options.
        /// </summary>
        [JsonPropertyOrder(235)]
        public string SelectDataSet { get; set; }

        /// <summary>
        /// Gets or sets the columnname to read the value from the datarow in a dataset.
        /// </summary>
        [JsonPropertyOrder(236)]
        public string SelectValueColumnName { get; set; }

        /// <summary>
        /// Gets or sets the columnname to read the display value from the datarow in a dataset.
        /// </summary>
        [JsonPropertyOrder(237)]
        public string SelectDisplayColumnName { get; set; }

        /// <summary>
        /// Gets or sets the metadata to add to the field.
        /// </summary>
        [JsonPropertyOrder(240)]
        public Dictionary<string, object> MetaData { get; set; }

        /// <summary>
        /// Creates a Text field to add to a page or area.
        /// </summary>
        /// <param name="id">The id of the text to craete.</param>
        /// <param name="label">The text label to assign to the label.</param>
        /// <param name="key">The field key.</param>
        /// <returns>The created field.</returns>
        public static Field CreateText(string id, string label, string key)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.Text, ValueType = Types.ValueType.String };

            return result;
        }

        /// <summary>
        /// Creates a TextArea field to add to a page or area.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="label">The field label.</param>
        /// <param name="key">The field key.</param>
        /// <returns>The created field.</returns>
        public static Field CreateTextArea(string id, string label, string key)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.TextArea, ValueType = Types.ValueType.String };

            return result;
        }

        /// <summary>
        /// Creates a boolean field to add to a page or area.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="label">The field label.</param>
        /// <param name="key">The field key.</param>
        /// <returns>The created field.</returns>
        public static Field CreateBoolean(string id, string label, string key)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.Checkbox, ValueType = Types.ValueType.Boolean };

            return result;
        }

        /// <summary>
        /// Creates an integer field to add to a page or area.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="label">The field label.</param>
        /// <param name="key">The field key.</param>
        /// <returns>The created field.</returns>
        public static Field CreateInteger(string id, string label, string key)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.Text, ValueType = Types.ValueType.Integer };

            return result;
        }

        /// <summary>
        /// Creates a float field to add to a page or area.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="label">The field label.</param>
        /// <param name="key">The field key.</param>
        /// <param name="decimals">No of decimals to show.</param>
        /// <returns>The created field.</returns>
        public static Field CreateFloat(string id, string label, string key, int decimals = 2)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.Text, ValueType = Types.ValueType.Float };
            result.AddMetaData("decimals", decimals);

            return result;
        }

        /// <summary>
        /// Creates a datetime field to add to a page or area.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="label">The field label.</param>
        /// <param name="key">The field key.</param>
        /// <returns>The created field.</returns>
        public static Field CreateDateTime(string id, string label, string key)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.DateTimeSelector, ValueType = Types.ValueType.DateTime };

            return result;
        }

        /// <summary>
        /// Creates a date field to add to a page or area.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="label">The field label.</param>
        /// <param name="key">The field key.</param>
        /// <returns>The created field.</returns>
        public static Field CreateDate(string id, string label, string key)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.DateSelector, ValueType = Types.ValueType.Date };

            return result;
        }

        /// <summary>
        /// Creates a time field to add to a page or area.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="label">The field label.</param>
        /// <param name="key">The field key.</param>
        /// <returns>The created field.</returns>
        public static Field CreateTime(string id, string label, string key)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.TimeSelector, ValueType = Types.ValueType.Time };

            return result;
        }

        /// <summary>
        /// Creates a selection field to add to a page or area.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="label">The field label.</param>
        /// <param name="key">The field key.</param>
        /// <param name="options">The select field options.</param>
        /// <returns>The created field.</returns>
        public static Field CreateSelectList(string id, string label, string key, IReadOnlyCollection<SelectOption> options = null)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.SelectList, ValueType = Types.ValueType.Select };

            if (options != null)
            {
                result.SelectOptions = options.ToList();
            }

            return result;
        }

        /// <summary>
        /// Creates a selection field to add to a page or area.
        /// </summary>
        /// <param name="id">The field id.</param>
        /// <param name="label">The field label.</param>
        /// <param name="key">The field key.</param>
        /// <param name="options">The select field options.</param>
        /// <returns>The created field.</returns>
        public static Field CreateMultiSelectList(string id, string label, string key, IReadOnlyCollection<SelectOption> options = null)
        {
            var result = new Field { Id = id, Label = label, Key = key, FieldType = FieldType.MultiSelectList, ValueType = Types.ValueType.Select };

            if (options != null)
            {
                result.SelectOptions = options.ToList();
            }

            return result;
        }

        /// <summary>
        /// Adds a condition to the field.
        /// </summary>
        /// <param name="condition">The condition to add.</param>
        /// <returns>The added condition.</returns>
        public Condition AddCondition(Condition condition)
        {
            if (Conditions == null)
            {
                Conditions = new List<Condition>();
            }

            Conditions.Add(condition);
            return condition;
        }

        /// <summary>
        /// Adds a validation rule to the field.
        /// </summary>
        /// <param name="name">Name of the rule (max, min, max-length etc).</param>
        /// <param name="value">Value for the validation.</param>
        /// <returns>The field instance for chaining.</returns>
        public Field AddValidationRule(string name, object value)
        {
            if (ValidationRules == null)
            {
                ValidationRules = new Dictionary<string, object>();
            }

            ValidationRules[name] = value;
            return this;
        }

        /// <summary>
        /// Adds an error message to the field.
        /// </summary>
        /// <param name="name">Name of the error (max, min, max-length etc).</param>
        /// <param name="value">The message to display.</param>
        /// <returns>The field instance for chaining.</returns>
        public Field AddErrorMessage(string name, object value)
        {
            if (ErrorMessages == null)
            {
                ErrorMessages = new Dictionary<string, object>();
            }

            ErrorMessages[name] = value;
            return this;
        }

        /// <summary>
        /// Adds a selection option field.
        /// </summary>
        /// <param name="value">The field value.</param>
        /// <param name="label">The field label.</param>
        /// <param name="selected">Field selected or not.</param>
        /// <returns>The current field.</returns>
        public Field AddSelectOption(string value, string label, bool selected = false)
        {
            AddSelectOption(new SelectOption { Value = value, Label = label, Selected = selected });
            return this;
        }

        /// <summary>
        /// Adds a selection option field.
        /// </summary>
        /// <param name="selectOption">The select field details.</param>
        /// <returns>The current field.</returns>
        public Field AddSelectOption(SelectOption selectOption)
        {
            if (SelectOptions == null)
            {
                SelectOptions = new List<SelectOption>();
            }

            SelectOptions.Add(selectOption);

            return this;
        }

        /// <summary>
        /// Sets a medata data value for the current field.
        /// </summary>
        /// <param name="name">Metadata name.</param>
        /// <param name="value">Metadata value.</param>
        /// <returns>The current field.</returns>
        public Field AddMetaData(string name, object value)
        {
            MetaData[name] = value;
            return this;
        }
    }
}
