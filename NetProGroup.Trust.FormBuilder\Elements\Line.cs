﻿// <copyright file="Line.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Forms.Elements
{
    /// <summary>
    /// A line to add to a paragraph.
    /// </summary>
    public class Line
    {
        /// <summary>
        /// Gets or sets the text for the line.
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// Gets or sets the class to add to the line.
        /// </summary>
        public string Class { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to add a linebreak after the line. Defaults to true.
        /// </summary>
        public bool LineBreak { get; set; } = true;
    }
}
