// <copyright file="Paragraph.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms.Elements
{
    /// <summary>
    /// Represents a paragraph for a page or area.
    /// </summary>
    public class Paragraph : ElementBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Paragraph"/> class.
        /// </summary>
        /// <param name="id">The unique identifier for the paragraph.</param>
        public Paragraph(string id)
           : base(id)
        {
        }

        /// <summary>
        /// Gets or sets the lines of text for the paragraph.
        /// </summary>
        [JsonPropertyOrder(100)]
        public List<Line> Lines { get; set; } = new List<Line>();

        /// <summary>
        /// Adds text as a line with a linebreak.
        /// </summary>
        /// <param name="text">The text to add.</param>
        /// <returns>The added line.</returns>
        public Line AddLine(string text)
        {
            return AddLine(new Line { Text = text, LineBreak = true });
        }

        /// <summary>
        /// Adds text as a line and a class specifier. Adds a linebreak.
        /// </summary>
        /// <param name="text">The text to add.</param>
        /// <param name="class">The class to add.</param>
        /// <returns>The added line.</returns>
        public Line AddLine(string text, string @class)
        {
            return AddLine(new Line { Text = text, Class = @class, LineBreak = true });
        }

        /// <summary>
        /// Adds text as a line and a class specifier and whether to add a linebreak.
        /// </summary>
        /// <param name="text">The text to add.</param>
        /// <param name="class">The class to add.</param>
        /// <param name="linebreak">Whether to add a linebreak.</param>
        /// <returns>The added line.</returns>
        public Line AddLine(string text, string @class, bool linebreak)
        {
            return AddLine(new Line { Text = text, Class = @class, LineBreak = linebreak });
        }

        /// <summary>
        /// Adds a line of text.
        /// </summary>
        /// <param name="line">The line to add to the paragraph.</param>
        /// <returns>The added line.</returns>
        public Line AddLine(Line line)
        {
            Lines.Add(line);

            return line;
        }
    }
}
