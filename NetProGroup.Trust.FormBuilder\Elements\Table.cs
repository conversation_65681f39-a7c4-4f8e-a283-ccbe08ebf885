// <copyright file="Table.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms.Elements
{
    /// <summary>
    /// Represents a table to display DataRows from a Dataset.
    /// </summary>
    public class Table : ElementBase
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Table"/> class.
        /// </summary>
        /// <param name="id">The unique identifier for the table.</param>
        public Table(string id)
           : base(id)
        {
        }

        /// <summary>
        /// Gets or sets the id of the DataSet to read the data (rows) from.
        /// </summary>
        [JsonPropertyOrder(100)]
        public string DataSet { get; set; }

        /// <summary>
        /// Gets or sets the collection of cells in each row of the table.
        /// </summary>
        /// <remarks>
        /// A cell can hold 1 or multiple display elements.
        /// </remarks>
        [JsonPropertyOrder(200)]
        public List<TableCell> Cells { get; set; } = new List<TableCell>();

        /// <summary>
        /// Gets or sets the metadata to add to the table.
        /// </summary>
        [JsonPropertyOrder(240)]
        public Dictionary<string, object> MetaData { get; set; }

        /// <summary>
        /// Adds a cell to the table.
        /// </summary>
        /// <param name="cell">The cell to add to the table.</param>
        /// <returns>The added cell.</returns>
        public TableCell AddCell(TableCell cell)
        {
            Cells.Add(cell);
            return cell;
        }

        /// <summary>
        /// Adds a new item to the MetaData.
        /// </summary>
        /// <param name="name">The name of the metadata item.</param>
        /// <param name="value">The value of the metadata item.</param>
        /// <returns>The current table instance for method chaining.</returns>
        public Table AddMetaData(string name, object value)
        {
            MetaData[name] = value;
            return this;
        }
    }
}
