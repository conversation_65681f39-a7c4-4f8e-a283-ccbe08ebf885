// <copyright file="TableCell.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms.Elements
{
    /// <summary>
    /// Represents a cell in a table.
    /// </summary>
    public class TableCell : Base
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="TableCell"/> class.
        /// </summary>
        /// <param name="id">The unique identifier for the table cell.</param>
        public TableCell(string id)
           : base(id)
        {
        }

        /// <summary>
        /// Gets or sets the class to use for the cell.
        /// </summary>
        [JsonPropertyOrder(100)]
        public string Class { get; set; }

        /// <summary>
        /// Gets or sets the collection of cell texts in the cell.
        /// </summary>
        /// <remarks>
        /// A cell can hold 1 or multiple text elements.
        /// </remarks>
        [JsonPropertyOrder(110)]
        public List<TableCellText> Texts { get; set; } = new List<TableCellText>();

        /// <summary>
        /// Adds a CellText to the table.
        /// </summary>
        /// <param name="cellText">The cell text to add to the table cell.</param>
        /// <returns>The added cell text.</returns>
        public TableCellText AddText(TableCellText cellText)
        {
            Texts.Add(cellText);
            return cellText;
        }
    }
}
