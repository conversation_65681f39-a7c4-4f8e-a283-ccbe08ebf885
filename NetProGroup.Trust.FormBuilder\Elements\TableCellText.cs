﻿// <copyright file="TableCellText.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms.Elements
{
    /// <summary>
    /// Represents a piece of text in a cell in a table.
    /// </summary>
    public class TableCellText : Base
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="TableCellText"/> class.
        /// </summary>
        /// <param name="id">The unique identifier for the table cell text.</param>
        public TableCellText(string id)
           : base(id)
        {
        }

        /// <summary>
        /// Gets or sets the optional fixed text.
        /// </summary>
        [JsonPropertyOrder(100)]
        public string Text { get; set; }

        /// <summary>
        /// Gets or sets the name of the column of the DataRow (from TableCell) in a DataSet.
        /// </summary>
        [JsonPropertyOrder(110)]
        public string ColumnName { get; set; }

        /// <summary>
        /// Gets or sets the class to add to the text.
        /// </summary>
        [JsonPropertyOrder(120)]
        public string Class { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to add a linebreak after the text. Defaults to true.
        /// </summary>
        [JsonPropertyOrder(130)]
        public bool LineBreak { get; set; } = true;
    }
}
