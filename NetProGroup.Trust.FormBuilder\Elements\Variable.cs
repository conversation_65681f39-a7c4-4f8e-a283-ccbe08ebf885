﻿// <copyright file="Variable.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms.Elements
{
    /// <summary>
    /// Represents a variable in a form.
    /// </summary>
    public class Variable : Base
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Variable"/> class.
        /// </summary>
        /// <param name="id">The unique identifier for the variable.</param>
        public Variable(string id)
           : base(id)
        {
        }

        /// <summary>
        /// Gets or sets the value of the variable.
        /// </summary>
        [JsonPropertyOrder(100)]
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the type of value. Defaults to String.
        /// </summary>
        [JsonPropertyOrder(110)]
        public Types.ValueType ValueType { get; set; } = Types.ValueType.String;

        /// <summary>
        /// Gets or sets the key of this variable which is used to store the value in the database.
        /// </summary>
        [JsonPropertyOrder(120)]
        public string Key { get; set; }
    }
}
