﻿// <copyright file="FormBuilder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Forms.Forms;
using System.Text.Json;

namespace NetProGroup.Trust.Forms
{
    /// <summary>
    /// Represents the top level of a document holding a polymorphic form.
    /// </summary>
    public class FormBuilder
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="FormBuilder"/> class.
        /// </summary>
        public FormBuilder()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FormBuilder"/> class.
        /// </summary>
        /// <param name="form">The base form.</param>
        public FormBuilder(FormBase form)
        {
            Form = form;
        }

        /// <summary>
        /// Gets or sets the form.
        /// </summary>
        public FormBase Form { get; set; }

        /// <summary>
        /// Deserializes the json to an instance of Document.
        /// </summary>
        /// <param name="json"><PERSON><PERSON><PERSON> to parse.</param>
        /// <returns>The parsed object.</returns>
        public static FormBuilder FromJson(string json)
        {
            if (string.IsNullOrEmpty(json))
            {
                return null;
            }

            return JsonSerializer.Deserialize<FormBuilder>(json, FormBase.GetJsonSerializerOptions());
        }

        /// <summary>
        /// Serializes the Document to a Json string.
        /// </summary>
        /// <returns>This as JSON.</returns>
        public string ToJson()
        {
            return JsonSerializer.Serialize(this, FormBase.GetJsonSerializerOptions());
        }
    }
}
