﻿// <copyright file="ExtendedForm.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Forms.Elements;
using System.Text.Json.Serialization;
using System.Text.Json;
using NetProGroup.Trust.Forms.Types;

namespace NetProGroup.Trust.Forms.Forms
{
    /// <summary>
    /// Represents a extended form definition.
    /// </summary>
    public class ExtendedForm : FormBase
    {
        private static JsonSerializerOptions _jsonSerializerOptions;
        private Page _currentPage;

        /// <summary>
        /// Initializes a new instance of the <see cref="ExtendedForm"/> class.
        /// </summary>
        public ExtendedForm() : base("")
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ExtendedForm"/> class.
        /// </summary>
        /// <param name="id"></param>
        public ExtendedForm(string id)
            : base(id)
        {
            Pages.Add(new Page($"page_{Pages.Count + 1}"));
            _currentPage = Pages.Last();

            if (_jsonSerializerOptions == null)
            {
                _jsonSerializerOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DictionaryKeyPolicy = JsonNamingPolicy.CamelCase,
                    Converters =
                    {
                        new JsonStringEnumConverter(JsonNamingPolicy.CamelCase)
                    }
                };
            }
        }

        /// <summary>
        /// Gets or sets the pages for this form.
        /// </summary>
        public List<Page> Pages { get; set; } = new List<Page>();

        /// <summary>
        /// Gets the current page.
        /// </summary>
        [JsonIgnore]
        public Page CurrentPage => _currentPage;

        /// <summary>
        /// Adds a new page and makes it the current one.
        /// </summary>
        /// <returns></returns>
        public Page AddPage()
        {
            Pages.Add(new Page($"page_{Pages.Count + 1}"));
            _currentPage = Pages.Last();
            return _currentPage;
        }

        /// <summary>
        /// Adds a new field to the current page.
        /// </summary>
        /// <param name="id">The unique identifier for the field.</param>
        /// <param name="type">The type of the field.</param>
        /// <param name="label">The label for the field.</param>
        /// <returns>The created field.</returns>
        public Field AddField(string id, FieldType type, string label)
        {
            var field = new Field(id, type);
            _currentPage.AddElement(field);
            return field;
        }

        /// <summary>
        /// Adds a new area to the current page.
        /// </summary>
        /// <param name="id">The unique identifier for the area.</param>
        /// <returns>The created area.</returns>
        public Area AddArea(string id)
        {
            var area = new Area(id);
            _currentPage.AddElement(area);
            return area;
        }
    }
}
