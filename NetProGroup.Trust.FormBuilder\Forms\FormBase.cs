﻿// <copyright file="FormBase.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Forms.MetaData;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms.Forms
{
    /// <summary>
    /// Represents the polymorphic base for forms.
    /// </summary>
    /// <remarks>
    /// Use the JsonDerivedType attributed to enable polymorphism by adding a $type tag to the form in the Json.
    /// </remarks>
    [JsonDerivedType(typeof(StandardForm), typeDiscriminator: "standard")]
    [JsonDerivedType(typeof(ExtendedForm), typeDiscriminator: "extended")]
    [JsonDerivedType(typeof(KeyValueForm), typeDiscriminator: "keyvalue")]
    public class FormBase : Base
    {
        private static JsonSerializerOptions _jsonSerializerOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="FormBase"/> class.
        /// </summary>
        /// <param name="id">The form ID.</param>
        public FormBase(string id) : base(id)
        {
        }

        /// <summary>
        /// Gets or sets who created this form.
        /// </summary>
        [JsonPropertyOrder(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the date this form was created.
        /// </summary>
        [JsonPropertyOrder(110)]
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the name of the form.
        /// </summary>
        [JsonPropertyOrder(120)]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets a description of the form.
        /// </summary>
        [JsonPropertyOrder(130)]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the version of the form.
        /// </summary>
        [JsonPropertyOrder(140)]
        public string Version { get; set; }

        /// <summary>
        /// Serializes the form to a Json string.
        /// </summary>
        /// <returns>The current object as JSON.</returns>
        public virtual string ToJson()
        {
            return new FormBuilder { Form = this }.ToJson();
        }

        /// <summary>
        /// Gets a list with all values from the fields.
        /// </summary>
        /// <returns>A list of the form values.</returns>
        public virtual IList<FieldValue> GetFormValues()
        {
            var result = new List<FieldValue>();

            return result;
        }

        /// <summary>
        /// Validates the fields and returns a list with the errors.
        /// </summary>
        /// <returns>A list of validated fields.</returns>
        public virtual IList<string> ValidateFields()
        {
            var result = new List<string>();

            return result;
        }

        /// <summary>
        /// Deserializes the json to an instance of Form.
        /// </summary>
        /// <param name="json">Input JSON to parse.</param>
        /// <returns>A new object of type TResult.</returns>
        /// <typeparam name="TResult">Target model.</typeparam>
        protected static TResult FromJson<TResult>(string json) where TResult : FormBase
        {
            return JsonSerializer.Deserialize<TResult>(json, GetJsonSerializerOptions());
        }

        /// <summary>
        /// Gets the options for Json serialization.
        /// </summary>
        /// <returns>The JSON serializer options for type <see cref="FormBase"/>.</returns>
        internal static JsonSerializerOptions GetJsonSerializerOptions()
        {
            if (_jsonSerializerOptions == null)
            {
                _jsonSerializerOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DictionaryKeyPolicy = JsonNamingPolicy.CamelCase,
                    Converters =
                    {
                        new JsonStringEnumConverter(JsonNamingPolicy.CamelCase)
                    }
                };
            }

            return _jsonSerializerOptions;
        }
    }
}
