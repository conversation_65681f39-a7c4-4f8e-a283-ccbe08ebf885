// <copyright file="KeyValueForm.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json.Serialization;
using System.Text.Json;
using NetProGroup.Trust.Forms.MetaData;

namespace NetProGroup.Trust.Forms.Forms
{
    /// <summary>
    /// Represents a very simple form definition with only KeyValue pairs to support the hardcoded forms in the application.
    /// As soon as there is a formbuilder, we can continue with the actual form definitions.
    /// </summary>
    public class KeyValueForm : FormBase
    {
        private static JsonSerializerOptions _jsonSerializerOptions;
        private Dictionary<string, string> _keyValuePairs;

        /// <summary>
        /// Initializes a new instance of the <see cref="KeyValueForm"/> class.
        /// </summary>
        public KeyValueForm() : base("") { }

        /// <summary>
        /// Initializes a new instance of the <see cref="KeyValueForm"/> class.
        /// </summary>
        /// <param name="id">The unique identifier for the form.</param>
        public KeyValueForm(string id)
            : base(id)
        {
            _keyValuePairs = new Dictionary<string, string>();

            if (_jsonSerializerOptions == null)
            {
                _jsonSerializerOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DictionaryKeyPolicy = JsonNamingPolicy.CamelCase,
                    Converters =
                    {
                        new JsonStringEnumConverter(JsonNamingPolicy.CamelCase)
                    }
                };
            }
        }

        /// <summary>
        /// Gets or sets the dataset, being a dictionary with KeyValue pairs.
        /// </summary>
        public Dictionary<string, string> DataSet { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Sets a string value on the form.
        /// </summary>
        /// <param name="key">The key of the value.</param>
        /// <param name="value">The value.</param>
        public void SetFormValue(string key, string value)
        {
            DataSet[key] = value;
        }

        /// <summary>
        /// Sets a boolean value on the form.
        /// </summary>
        /// <param name="key">The key of the value.</param>
        /// <param name="value">The boolean value.</param>
        public void SetFormValue(string key, bool value)
        {
            DataSet[key] = value.ToString().ToLowerInvariant();
        }

        /// <summary>
        /// Sets a datetime value on the form.
        /// </summary>
        /// <param name="key">The key of the value.</param>
        /// <param name="value">The datetime value.</param>
        public void SetFormValue(string key, DateTime value)
        {
            DataSet[key] = value.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// Sets a datetime value on the form.
        /// </summary>
        /// <param name="key">The key of the value.</param>
        /// <param name="value">The datetime value.</param>
        public void SetFormValue(string key, DateTime? value)
        {
            if (!value.HasValue)
            {
                DataSet.Remove(key);
            }
            else
            {
                DataSet[key] = value.Value.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// Gets a list with all values from the fields.
        /// </summary>
        /// <returns></returns>
        public override IList<FieldValue> GetFormValues()
        {
            var result = base.GetFormValues();

            foreach (var key in DataSet.Keys)
            {
                var fieldValue = new FieldValue { Key = key, Value = DataSet[key] };
                result.Add(fieldValue);
            }

            return result;
        }
    }
}
