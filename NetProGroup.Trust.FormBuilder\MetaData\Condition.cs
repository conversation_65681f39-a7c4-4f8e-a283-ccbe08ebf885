﻿// <copyright file="Condition.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Trust.Forms.Types;
using System.Text.Json.Serialization;

namespace NetProGroup.Trust.Forms.MetaData
{
    /// <summary>
    /// Represents a condition for a field or area.
    /// </summary>
    public class Condition
    {
        /// <summary>
        /// Gets or sets the type of condition.
        /// </summary>
        [JsonPropertyName("condition")]
        public ConditionType ConditionType { get; set; }

        /// <summary>
        /// Gets or sets how to interpret the comparisons.
        /// </summary>
        [JsonPropertyName("match")]
        public Types.MatchType MatchType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the condition is met when the comparison matches the match type.
        /// </summary>
        /// <remarks>
        /// i.e. for a 'Required' condition this is true or false.
        /// </remarks>
        public bool Value { get; set; }

        /// <summary>
        /// Gets or sets the conditions for this condition.
        /// </summary>
        public List<Comparison> Comparisons { get; set; } = new List<Comparison>();

        /// <summary>
        /// Adds a comparison to the condition.
        /// </summary>
        /// <param name="comparison">The comparison to add to the condition.</param>
        /// <returns>The added comparison.</returns>
        public Comparison AddComparison(Comparison comparison)
        {
            Comparisons.Add(comparison);
            return comparison;
        }
    }
}
