// <copyright file="SelectOption.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Forms.MetaData
{
    /// <summary>
    /// Represents an option for a select field.
    /// </summary>
    public class SelectOption
    {
        /// <summary>
        /// Gets or sets the value of the select option.
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the label of the select option.
        /// </summary>
        public string Label { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this option is selected.
        /// </summary>
        public bool Selected { get; set; }
    }
}
