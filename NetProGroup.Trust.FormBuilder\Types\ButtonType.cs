// <copyright file="ButtonType.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Forms.Types
{
    /// <summary>
    /// Represents the different types of buttons that can be used in forms.
    /// </summary>
    public enum ButtonType
    {
        /// <summary>
        /// But<PERSON> starts a dialog.
        /// </summary>
        Dialog,

        /// <summary>
        /// Button is a Submit action.
        /// </summary>
        Submit,

        /// <summary>
        /// Button is a Cancel action.
        /// </summary>
        Cancel,

        /// <summary>
        /// Button is a Back action.
        /// </summary>
        Back,

        /// <summary>
        /// Button is a Next action.
        /// </summary>
        Next,
    }
}
