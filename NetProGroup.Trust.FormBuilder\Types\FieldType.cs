﻿// <copyright file="FieldType.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Forms.Types
{
    /// <summary>
    /// Represents the different types of fields that can be used in forms.
    /// </summary>
    public enum FieldType
    {
        /// <summary>
        /// Field is a text field.
        /// </summary>
        Text,

        /// <summary>
        /// Field is a text-area field.
        /// </summary>
        TextArea,

        /// <summary>
        /// Field is a checkbox.
        /// </summary>
        Checkbox,

        /// <summary>
        /// Field is a radio button.
        /// </summary>
        Radiobutton,

        /// <summary>
        /// Field is a select list with a single selection.
        /// </summary>
        SelectList,

        /// <summary>
        /// Field is a select list with multiple selections.
        /// </summary>
        MultiSelectList,

        /// <summary>
        /// Field is a selector for a date.
        /// </summary>
        DateSelector,

        /// <summary>
        /// Field is a selector for a time.
        /// </summary>
        TimeSelector,

        /// <summary>
        /// Field is a selector for a date and time.
        /// </summary>
        DateTimeSelector,
    }
}
