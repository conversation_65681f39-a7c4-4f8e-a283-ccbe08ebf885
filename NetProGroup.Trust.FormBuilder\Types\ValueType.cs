﻿// <copyright file="ValueType.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Forms.Types
{
    /// <summary>
    /// Represents the different types of values that can be stored in form fields.
    /// </summary>
    public enum ValueType
    {
        /// <summary>
        /// The value is a string.
        /// </summary>
        String,

        /// <summary>
        /// Value is a boolean.
        /// </summary>
        <PERSON><PERSON><PERSON>,

        /// <summary>
        /// Value is a DateTime.
        /// </summary>
        DateTime,

        /// <summary>
        /// Value is a Date.
        /// </summary>
        Date,

        /// <summary>
        /// Value is a Time.
        /// </summary>
        Time,

        /// <summary>
        /// Value is an integer.
        /// </summary>
        Integer,

        /// <summary>
        /// Value is a floating value.
        /// </summary>
        Float,

        /// <summary>
        /// The value is a selection (1 or multiple).
        /// </summary>
        Select,
    }
}
