﻿// <copyright file="Company.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Import.CanonicalModels
{
    /// <summary>
    /// Represents a Company from an import file.
    /// </summary>
    public class Company
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Company"/> class.
        /// </summary>
        public Company()
        {
        }

        /// <summary>
        /// Gets or sets the unique id of the company.
        /// </summary>
        public string UniqueId { get; set; }

        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the code of the company (aka VG Code). This is unique within a jurisdiction.
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the legacy code where applicable.
        /// </summary>
        public string LegacyCode { get; set; }

        /// <summary>
        /// Gets or sets the type of the company in case of a company entity.
        /// </summary>
        /// <remarks>
        /// BC Company, Limited Partnership, Trust, Fund, Vessel, Foundation
        /// </remarks>
        public string CompanyType { get; set; }

        /// <summary>
        /// Gets or sets the incorporation number.
        /// </summary>
        public string IncorporationNr { get; set; }

        /// <summary>
        /// Gets or sets the incorporation date.
        /// </summary>
        public DateTime IncorporationDate { get; set; }

        /// <summary>
        /// Gets or sets the Jurisdiction of Registration.
        /// </summary>
        public string JurisdictionOfRegistration { get; set; }

        /// <summary>
        /// Gets or sets the referral office.
        /// </summary>
        public string ReferralOffice { get; set; }

        /// <summary>
        /// Gets or sets the service office.
        /// </summary>
        public string ServiceOffice { get; set; }

        /// <summary>
        /// Gets or sets the entity status.
        /// </summary>
        public string EntityStatus { get; set; }

        /// <summary>
        /// Gets or sets the entity sub-status.
        /// </summary>
        public string EntitySubStatus { get; set; }

        /// <summary>
        /// Gets or sets the risk group.
        /// </summary>
        public string RiskGroup { get; set; }

        /// <summary>
        /// Gets or sets the master client code.
        /// </summary>
        public string MasterClientCode { get; set; }
    }
}
