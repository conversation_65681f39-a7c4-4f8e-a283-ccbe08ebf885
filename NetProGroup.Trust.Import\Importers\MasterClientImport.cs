﻿// <copyright file="MasterClientImport.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.DataManager.LegalEntityRelations.MasterClients.RequestResponses;
using NetProGroup.Trust.DataManager.MasterClients;
using NetProGroup.Trust.DataManager.MasterClients.RequestResponses;
using NetProGroup.Trust.Domain.Sync;
using NetProGroup.Trust.Import.Interfaces;

namespace NetProGroup.Trust.Import.Importers
{
    /// <summary>
    /// Basic implementation for importing MasterClient data.
    /// </summary>
    public class MasterClientImport : ImportBase, IMasterClientImport
    {
        private readonly ILogger _logger;
        private readonly IMasterClientsDataManager _masterClientDataManager;
        private readonly ISyncMasterClientRepository _syncMasterClientRepository;
        private readonly ILockManager _lockManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="MasterClientImport"/> class.
        /// </summary>
        public MasterClientImport(
            ILogger<DirectorImport> logger,
            IMasterClientsDataManager masterClientDataManager,
            ISyncMasterClientRepository syncMasterClientRepository,
            ILockManager lockManager)
        {
            _logger = logger;
            _masterClientDataManager = masterClientDataManager;
            _syncMasterClientRepository = syncMasterClientRepository;
            _lockManager = lockManager;
        }

        /// <summary>
        /// Imports the contents of the file.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to import for.</param>
        /// <param name="stream">The stream holding the file.</param>
        /// <returns></returns>
        public virtual async Task<object> ImportFileAsync(Guid jurisdictionId, Stream stream)
        {
            ArgumentNullException.ThrowIfNull(stream, nameof(stream));

            // Build the canonical model from the excel sheet
            var canonicalModels = BuildCanonicalModels(stream, out bool isOldStyle);

            // Create the request to use on the DataManager
            var request = new CreateMasterClientsRequest();

            var masterClients = new Dictionary<string, CreateMasterClient>(StringComparer.OrdinalIgnoreCase);

            foreach (var item in canonicalModels)
            {
                if (!masterClients.TryGetValue(item.MasterClientCode, out CreateMasterClient createMasterClient))
                {
                    createMasterClient = new CreateMasterClient
                    {
                        JurisdictionId = jurisdictionId,
                        MasterClientCode = item.MasterClientCode,

                        DeleteNonExistingEmailAddresses = !isOldStyle
                    };
                    masterClients.Add(item.MasterClientCode, createMasterClient);
                }

                // Add all email address from this 'row' to the 'createMasterClient'
                item.EmailAddresses.ToList().ForEach(x => createMasterClient.EmailAddresses.Add(x));
            }

            masterClients.Values.ToList().ForEach(x => request.MasterClients.Add(x));



            await _masterClientDataManager.ImportMasterClients(request);

            return null;
        }

        /// <summary>
        /// Validates the file and saves the data.
        /// </summary>
        /// <param name="jurisdictionId">Id of the jurisdiction to import for.</param>
        /// <param name="stream">The stream holding the file.</param>
        /// <returns></returns>
        public virtual async Task<object> ValidateFileAsync(Guid jurisdictionId, Stream stream)
        {
            await Task.CompletedTask;
            throw new NotImplementedException();
        }

        /// <inheritdoc/>
        public async Task<SyncResult> SyncViewPointAsync(LockDTO jobLock)
        {
            ArgumentNullException.ThrowIfNull(jobLock, nameof(jobLock));

            await _lockManager.RefreshLockAsync(jobLock.Id.Value);

            if (!await _syncMasterClientRepository.StagingTableExistsAsync())
            {
                return new SyncResult();
            }

            var count = await _syncMasterClientRepository.GetStagingCountAsync();
            var threshold = 1;
            if (count < threshold)
            {
                throw new Framework.Exceptions.APIException($"Sync for MasterClients found only {count} entries in staging; threshold = {threshold}");
            }

            await _lockManager.RefreshLockAsync(jobLock.Id.Value);

            var allMasterClients = await _syncMasterClientRepository.GetAllMasterClientsAsync();

            var allSyncMasterClientUsers = new List<SyncMasterClientUser>();

            foreach (var masterClient in allMasterClients)
            {
                var emailaddresses = GetEmailAddresses(masterClient.UserEmailAddress);

                foreach (var emailAddress in emailaddresses)
                {
                    var syncMasterClientUser = new SyncMasterClientUser
                    {
                        MasterClientCode = masterClient.ClientCode,
                        MasterClientName = masterClient.ClientName,
                        UserEmail = emailAddress
                    };

                    allSyncMasterClientUsers.Add(syncMasterClientUser);
                }
            }

            var request = new SyncMasterClientRequest
            {
                MasterClientUsers = allSyncMasterClientUsers
            };

            await RefreshLockAsync(_lockManager, jobLock);

            var result = new SyncResult
            {
                UpdatedCount = allSyncMasterClientUsers.Count
            };

            await _masterClientDataManager.SyncMasterClientsAsync(request, jobLock, beforeCommitAsync: async (dbContext) =>
            {
                await _lockManager.RefreshLockAsync(jobLock.Id.Value);

                _logger.LogInformation("Copy data to history...");
                await _syncMasterClientRepository.SaveLastStateAsync();
                _logger.LogInformation("Copied to history");
            });

            return result;
        }

        /// <summary>
        /// Parses the excel sheet and return a list of canonical MasterClients.
        /// </summary>
        /// <param name="stream"></param>
        /// <param name="isOldStyleExcel">Returns whether the excel is an older style with only 2 email addresse.</param>
        /// <returns></returns>
        protected virtual ICollection<CanonicalModels.MasterClient> BuildCanonicalModels(Stream stream, out bool isOldStyleExcel)
        {
            var result = new List<CanonicalModels.MasterClient>();

            // Create an instance of Fast Excel with the stream as the input.
            using (FastExcel.FastExcel fastExcel = new FastExcel.FastExcel(null, stream, true, true))
            {
                var sheet = fastExcel.Worksheets[0];
                sheet.Read();

                isOldStyleExcel = GetColumnExists(sheet, "Email Option 1");

                // Get all rows after the header row
                var rows = sheet.Rows.Skip(1).ToArray();

                foreach (var row in rows)
                {
                    var code = GetCellValueAsString(sheet, row, "MC Code", false);

                    // Older format
                    var email1 = GetCellValueAsString(sheet, row, "Email Option 1", true);
                    var email2 = GetCellValueAsString(sheet, row, "Email Option 2", true);

                    // New format
                    var email = GetCellValueAsString(sheet, row, "Email", true);

                    if (!string.IsNullOrEmpty(code))
                    {
                        var masterClient = new CanonicalModels.MasterClient { MasterClientCode = code };

                        masterClient.EmailAddresses.Add(email1);

                        if (!string.IsNullOrEmpty(email2))
                        {
                            masterClient.EmailAddresses.Add(email2);
                        }

                        if (!string.IsNullOrEmpty(email))
                        {
                            masterClient.EmailAddresses.Add(email);
                        }

                        result.Add(masterClient);
                    }
                }
            }

            return result;
        }

        private static List<string> GetEmailAddresses(string emailAddress)
        {
            var result = new List<string>();

            if (!string.IsNullOrEmpty(emailAddress) && !emailAddress.Contains(',', StringComparison.OrdinalIgnoreCase))
            {
                return emailAddress.Split(';', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList();
            }

            return result;
        }
    }
}
