using System.Net;
using System.Xml.Serialization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Payments.Processor;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.CXPayStatus;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Input;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Services;
using NetProGroup.Trust.Payment.Provider.Status;

namespace NetProGroup.Trust.Payment.Provider.CXPay;

/// <summary>
/// Implementation for CXPay
/// </summary>
public class CxPaymentService : ICxPaymentService
{
    private readonly ILogger _logger;
    private readonly IPaymentTransactionRepository _paymentTransactionRepository;
    private readonly IDateTimeProvider _dateTimeProvider;
    public HttpWebRequest? httpRequest;

    /// <summary>
    /// Initializes a new instance of the <see cref="CxPaymentService"/> class.
    /// </summary>
    /// <param name="paymentTransactionRepository">The payment transaction repository.</param>
    /// <param name="logger">The logger.</param>
    /// <param name="dateTimeProvider">The datetime provider.</param>
    public CxPaymentService(IPaymentTransactionRepository paymentTransactionRepository,
                            ILogger<CxPaymentService> logger, IDateTimeProvider dateTimeProvider)
    {
        _logger = logger;
        _dateTimeProvider = dateTimeProvider;
        _paymentTransactionRepository = paymentTransactionRepository;
    }

    /// <summary>
    /// Step1: Process the initial request given the payment request object
    /// </summary>
    /// <param name="request">PAyment request object that must contains the required data to start a payment attempt with CX-Pay</param>
    /// <returns></returns>
    /// <exception cref="ApplicationException"></exception> <summary>
    /// 
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public PaymentProcessorResponse ProcessRequest(PaymentRequest request)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));
        CxPayResponse result = new CxPayResponse();

        try
        {
            CxPaymentSale cxPaymentSale = new CxPaymentSale()
            {
                apiKey = request.ApiKey,
                redirectUrl = request.RedirectUrl,
                amount = request.Amount,
                //currency = request.Currency,
                orderId = request.OrderId,
                merchantReceiptEmail = request.Email,
                customerReceipt = true,
                billing = new CxPaymentBilling()
                {
                    company = request.Company,
                    email = request.Email
                }
            };

            string xmlBody = "";

            using (StringWriter textWriter = new StringWriter())
            {

                XmlSerializer x = new XmlSerializer(cxPaymentSale.GetType());
                x.Serialize(textWriter, cxPaymentSale);
                xmlBody = textWriter.ToString();
            }

            xmlBody = xmlBody.Replace(Environment.NewLine, string.Empty).Replace("utf-16", "UTF-8");

            httpRequest = (HttpWebRequest)WebRequest.Create(request.PaymentGatewaytUrl);
            httpRequest.Method = "POST";
            httpRequest.ContentType = "text/xml";
            httpRequest.Accept = "text/xml";
            var httpStream = httpRequest.GetRequestStream();
            using (var streamWriter = new StreamWriter(httpStream))
            {
                streamWriter.Write(xmlBody);
            }

            var httpResponse = (HttpWebResponse)httpRequest.GetResponse();
            var responseResult = "";
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                responseResult = streamReader.ReadToEnd();
            }
            httpStream.Close();
            httpResponse.Close();

            XmlSerializer serializer = new XmlSerializer(typeof(CxPayResponse));
            using (TextReader reader = new StringReader(responseResult))
            {
                result = (CxPayResponse)serializer.Deserialize(reader)!;
            }
        }
        catch (Exception ex)
        {
            throw new ApplicationException("Error encountered in processing a CX pay request with error message: " + ex.Message);
        }

        try
        {
            #region Update transaction                            

            var transaction = _paymentTransactionRepository.FindFirstOrDefaultByCondition(t => t.Id == Guid.Parse(request.OrderId));
            transaction.Result = result.Result.ToString();
            transaction.ResultCode = result.ResultCode.ToString();
            transaction.ResultMessage = result.ResultText;
            transaction.TransactionId = result.TransactionId;

            switch (result.Result)
            {
                case 2:
                    transaction.Status = CxTransactionStatus.DECLINED.ToString();
                    transaction.IsFinished = true;
                    break;
                case 3:
                    transaction.Status = CxTransactionStatus.ERROR.ToString();
                    transaction.IsFinished = true;
                    break;
            }

            #endregion

            var transactionResult = _paymentTransactionRepository.Update(transaction);

            PaymentProcessorResponse response = new PaymentProcessorResponse();

            response.TransactionId = result.TransactionId;
            response.CallBackUrl = result.FormUrl;
            response.ResultCode = result.Result;
            response.ResultNumber = result.ResultCode;
            response.ResultMessage = result.ResultText;
            response.Transaction = transactionResult;

            return response;
        }
        catch (Exception ex)
        {

            throw new ApplicationException("Error encountered in handling the transaction with id :" + request.OrderId + " an message: " + ex.Message);
        }

    }


    /// <summary>
    /// Step2: Process the initial request given the payment request object
    /// </summary>
    /// <param name="request">Payment request object that must contains the required data to start a payment attempt with CX-Pay</param>
    /// <param name="completionRedirectUrl">The url to redirect the browser to after CXPay processed the submit of the payment</param>
    /// <param name="transactionId">The id of the transaction in the database</param>
    /// <returns>The received CxPayResponse</returns>
    public CxPayResponse ProcessRequest(StartPaymentRequest request, string completionRedirectUrl, Guid transactionId)
    {
        ArgumentNullException.ThrowIfNull(request);

        if (string.IsNullOrEmpty(completionRedirectUrl))
        {
            throw new ArgumentNullException(nameof(completionRedirectUrl));
        }

        CxPayResponse result = new CxPayResponse();

        try
        {
            // Create a sale obejct to pass on to CxPay
            var sale = MapToSale(request, completionRedirectUrl);

            // Post to CXPay
            result = PostData<CxPayResponse>(sale, request.Flow.PaymentGatewayUrl);
        }
        catch (Exception ex)
        {
            throw new ApplicationException("Error encountered in processing a CX pay request with error message: " + ex.Message);
        }

        try
        {
            // Update the transaction with the result                           
            UpdateTransaction(result, transactionId);

            return result;
        }
        catch (Exception ex)
        {
            throw new ApplicationException("Error encountered in handling the transaction with id '" + transactionId.ToString() + "' and message: " + ex.Message);
        }

    }

    /// <summary>
    /// Step 3: Completes the transaction once step 2 is successfully executed (html form)
    /// </summary>
    /// <param name="request">Request object that contains the token generated by CXPay, the transaction id and the type</param>
    /// <returns></returns>
    public PaymentProcessorResponse CompleteRequest(PaymentComplete request)
    {
        ArgumentNullException.ThrowIfNull(request, nameof(request));

        PaymentProcessorResponse response = new PaymentProcessorResponse();

        var transaction = _paymentTransactionRepository.GetById(Guid.Parse(request.IdTransaction), o =>
            o.Include(x => x.Payment)
                .Include(p => p.PaymentProvider)
        );

        if (transaction == null)
        {
            throw new ApplicationException("No transaction found with transaction id: " + request.IdTransaction);
        }

        _logger.LogInformation($"CompleteRequest for transaction '{request.IdTransaction}'");

        if (transaction.IsFinished)
        {
            response.TransactionId = transaction.TransactionId;
            response.ResultCode = Convert.ToInt16(transaction.ResultCode);
            response.ResultNumber = Convert.ToInt16(transaction.Result);
            response.ResultMessage = transaction.ResultMessage;
            response.Transaction = transaction;

            return response;
        }

        CxPayCompletePayment data = new CxPayCompletePayment()
        {
            apiKey = transaction.PaymentProvider.ApiKey,
            tokenId = request.Token
        };

        string xmlBody = "";

        using (StringWriter textWriter = new StringWriter())
        {

            XmlSerializer x = new XmlSerializer(data.GetType());
            x.Serialize(textWriter, data);
            xmlBody = textWriter.ToString();
        }

        xmlBody = xmlBody.Replace(Environment.NewLine, string.Empty).Replace("utf-16", "UTF-8");

        httpRequest = (HttpWebRequest)WebRequest.Create(request.ApiGatewayUrl);
        httpRequest.Method = "POST";
        httpRequest.ContentType = "text/xml";
        httpRequest.Accept = "text/xml";
        var httpStream = httpRequest.GetRequestStream();
        using (var streamWriter = new StreamWriter(httpStream))
        {
            streamWriter.Write(xmlBody);
        }

        var httpResponse = (HttpWebResponse)httpRequest.GetResponse();
        var responseResult = "";
        using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
        {
            responseResult = streamReader.ReadToEnd();
        }

        httpStream.Close();
        httpResponse.Close();

        CxPayResponse result = new CxPayResponse();
        XmlSerializer serializer = new XmlSerializer(typeof(CxPayResponse));
        using (TextReader reader = new StringReader(responseResult))
        {
            result = (CxPayResponse)serializer.Deserialize(reader)!;
        }

        response.TransactionId = result.TransactionId;
        response.CallBackUrl = result.FormUrl;
        response.ResultCode = result.Result;
        response.ResultNumber = result.ResultCode;
        response.ResultMessage = result.ResultText;

        _logger.LogInformation($"Result of CompleteRequest for transaction '{request.IdTransaction}' is {result.Result}");

        switch (result.Result)
        {
            case 1:
                transaction.Status = TransactionStatus.COMPLETED;
                transaction.IsFinished = true;
                transaction.Result = result.Result.ToString();
                transaction.ResultCode = result.ResultCode.ToString();
                transaction.ResultMessage = result.ResultText;
                transaction.PaidAt = _dateTimeProvider.UtcNow;
                transaction.FirstName = result.CxPaymentBilling.firstName;
                transaction.PhoneNumber = result.CxPaymentBilling.phone;
                transaction.Email = result.CxPaymentBilling.email;
                transaction.CardDigits = result.CxPaymentBilling.CardDigits;
                transaction.LastName = result.CxPaymentBilling.lastName;
                transaction.Address = result.CxPaymentBilling.address;
                transaction.City = result.CxPaymentBilling.city;
                transaction.State = result.CxPaymentBilling.state;
                transaction.ZipCode = result.CxPaymentBilling.postal;
                transaction.Company = result.CxPaymentBilling.company;
                break;
            case 2:
                transaction.Status = TransactionStatus.FAILED;
                transaction.IsFinished = true;
                transaction.Result = result.Result.ToString();
                transaction.ResultCode = result.ResultCode.ToString();
                transaction.ResultMessage = result.ResultText;
                transaction.FirstName = result.CxPaymentBilling.firstName;
                transaction.PhoneNumber = result.CxPaymentBilling.phone;
                transaction.Email = result.CxPaymentBilling.email;
                transaction.CardDigits = result.CxPaymentBilling.CardDigits;
                transaction.LastName = result.CxPaymentBilling.lastName;
                transaction.Address = result.CxPaymentBilling.address;
                transaction.City = result.CxPaymentBilling.city;
                transaction.State = result.CxPaymentBilling.state;
                transaction.ZipCode = result.CxPaymentBilling.postal;
                transaction.Company = result.CxPaymentBilling.company;


                break;
            case 3:
                transaction.Status = TransactionStatus.ERROR;
                transaction.IsFinished = true;
                transaction.Result = result.Result.ToString();
                transaction.ResultCode = result.ResultCode.ToString();
                transaction.ResultMessage = result.ResultText;
                break;
        }

        var transactionResult = _paymentTransactionRepository.Update(transaction);
        response.Transaction = transactionResult;

        return response;
    }

    /// <summary>
    /// Step 3: Completes the transaction once step 2 is successfully executed (html form)
    /// </summary>
    /// <param name="request">Request object that contains the token generated by CXPay, the transaction id and the type</param>
    /// <returns></returns>
    public CxPayResponse CompletePayment(string apiurl, string tokenId, string apiKey)
    {
        if (string.IsNullOrEmpty(tokenId))
        {
            throw new ArgumentNullException(nameof(tokenId));
        }

        // Setup the request for CXPay
        CxPayCompletePayment data = new CxPayCompletePayment()
        {
            apiKey = apiKey,
            tokenId = tokenId
        };

        var result = PostData<CxPayResponse>(data, apiurl);

        return result;
    }

    #region Utils

    /// <summary>
    /// Creates a Sale object for the request
    /// </summary>
    /// <param name="request"></param>
    /// <param name="completionRedirectUrl"></param>
    /// <returns></returns>
    private CxPaymentSale MapToSale(StartPaymentRequest request, string completionRedirectUrl)
    {
        // Create a sale obejct to pass on to CxPay
        CxPaymentSale cxPaymentSale = new CxPaymentSale()
        {
            apiKey = request.PaymentProvider.ApiKey,
            redirectUrl = completionRedirectUrl,
            amount = request.Order.Amount,
            orderDescription = request.Order.Description,
            orderId = request.Order.OrderId,

            merchantReceiptEmail = request.Merchant?.Email!,

            customerReceipt = true,
            billing = new CxPaymentBilling()
            {
                company = request.CardHolder.CompanyName,
                email = request.CardHolder.Email,
                phone = request.CardHolder.PhoneNumber,
                lastName = request.CardHolder.LastName,
                firstName = request.CardHolder.FirstName,
            }
        };

        return cxPaymentSale;
    }


    /// <summary>
    /// Updates the transaction with the response from posting the sale.
    /// </summary>
    /// <param name="CxPayResponse"></param>
    /// <param name="transactionId"></param>
    private void UpdateTransaction(CxPayResponse CxPayResponse, Guid transactionId)
    {
        // Update transaction                            

        var transaction = _paymentTransactionRepository.GetById(transactionId);
        transaction.Result = CxPayResponse.Result.ToString();
        transaction.ResultCode = CxPayResponse.ResultCode.ToString();
        transaction.ResultMessage = CxPayResponse.ResultText;
        transaction.TransactionId = CxPayResponse.TransactionId;

        switch (CxPayResponse.Result)
        {
            case 2:
                transaction.Status = CxTransactionStatus.DECLINED.ToString();
                transaction.IsFinished = true;
                break;
            case 3:
                transaction.Status = CxTransactionStatus.ERROR.ToString();
                transaction.IsFinished = true;
                break;
        }

        _paymentTransactionRepository.Update(transaction);
        _paymentTransactionRepository.SaveChanges();
    }

    private T PostData<T>(object data, string apiEndpoint)
    {
        string xmlBody = "";

        using (StringWriter textWriter = new StringWriter())
        {
            XmlSerializer x = new XmlSerializer(data.GetType());
            x.Serialize(textWriter, data);
            xmlBody = textWriter.ToString();
        }

        xmlBody = xmlBody.Replace(Environment.NewLine, string.Empty).Replace("utf-16", "UTF-8");

        try
        {
            httpRequest = (HttpWebRequest)WebRequest.Create(apiEndpoint);
            httpRequest.Method = "POST";
            httpRequest.ContentType = "text/xml";
            httpRequest.Accept = "text/xml";
            var httpStream = httpRequest.GetRequestStream();
            using (var streamWriter = new StreamWriter(httpStream))
            {
                streamWriter.Write(xmlBody);
            }

            var httpResponse = (HttpWebResponse)httpRequest.GetResponse();
            var responseResult = "";
            using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
            {
                responseResult = streamReader.ReadToEnd();
            }

            httpStream.Close();
            httpResponse.Close();

            XmlSerializer serializer = new XmlSerializer(typeof(T));
            using (TextReader reader = new StringReader(responseResult))
            {
                return (T)serializer.Deserialize(reader)!;
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }

        return default;
    }

    #endregion
}


