using System.Xml.Serialization;

namespace NetProGroup.Trust.Payment.Provider.CXPay.Contracts.CXPayStatus
{
    [XmlRoot("response")]
    public class CxPayResponse
    {
        [XmlElement("result")]
        public int Result { get; set; }

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        [XmlElement("result-text")]
        public string ResultText { get; set; }

        [XmlElement("transaction-id")]
        public string TransactionId { get; set; }

        [XmlElement("form-url")]
        public string FormUrl { get; set; }

        public CxPaymentBilling CxPaymentBilling { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

        [XmlElement("result-code")]
        public int ResultCode { get; set; }
    }
}