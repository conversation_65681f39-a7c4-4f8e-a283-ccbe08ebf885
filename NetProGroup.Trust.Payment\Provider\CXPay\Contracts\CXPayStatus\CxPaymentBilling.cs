using System.Xml.Serialization;

namespace NetProGroup.Trust.Payment.Provider.CXPay.Contracts.CXPayStatus
{
    public class CxPaymentBilling
    {
        public string company { get; set; }
        public string email { get; set; }

        [XmlElement("first-name")]
        public string firstName { get; set; }

        [XmlElement("last-name")]
        public string lastName { get; set; }

        [XmlElement("address1")]
        public string address { get; set; }

        [XmlElement("city")]
        public string city { get; set; }

        [XmlElement("state")]
        public string state { get; set; }

        [XmlElement("postal")]
        public string postal { get; set; }

        [XmlElement("country")]
        public string country { get; set; }

        [XmlElement("phone")]
        public string phone { get; set; }

        [XmlElement("cc-number")]
        public string CardDigits { get; set; }
    }
}