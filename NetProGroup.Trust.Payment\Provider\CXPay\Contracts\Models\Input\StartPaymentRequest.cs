using System.ComponentModel.DataAnnotations;
using NetProGroup.Trust.Domain.Payments.Provider;

namespace NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Input
{
    /// <summary>
    /// Represents a payment request to start a payment.
    /// </summary>
    public class StartPaymentRequest
    {
        /// <summary>
        /// Gets or sets the order information.
        /// </summary>
        public Guid PaymentId { get; set; }

        /// <summary>
        /// Gets or sets the order information.
        /// </summary>
        public OrderInfo Order { get; set; }

        /// <summary>
        /// Gets or sets the flow information (urls to use etc).
        /// </summary>
        public FlowInfo Flow { get; set; }

        /// <summary>
        /// Gets or sets the cardholder information.
        /// </summary>
        public CardholderInfo CardHolder { get; set; }

        /// <summary>
        /// Gets or sets the merchant information.
        /// </summary>
        public MerchantInfo Merchant { get; set; }

        /// <summary>
        /// Gets or sets the list of invoice IDs associated with the transaction.
        /// </summary>
        /// <value>
        /// A list of <see cref="Guid"/> representing the unique identifiers of the invoices.
        /// </value>
        public List<Guid> InvoiceIds { get; set; }

        /// <summary>
        /// Gets or sets the payment provider.
        /// </summary>
        public PaymentProvider PaymentProvider { get; set; }


        /// <summary>
        /// Parameters for the flow of the payment process.
        /// </summary>
        public class FlowInfo
        {
            /// <summary>
            /// Gets or sets the url for the provider to redirect back to the application after the user submitted the form to the provider.
            /// </summary>
            /// <remarks>
            /// If the url does not start with https://, it is assumed to be a path relative to the CallBackUrl configured at the consumer.
            /// </remarks>
            public string PaymentGatewayUrl { get; set; }

            /// <summary>
            /// Gets or sets the url for the provider to redirect back to the application after the user submitted the form to the provider.
            /// </summary>
            /// <remarks>
            /// If the url does not start with https://, it is assumed to be a path relative to the CallBackUrl configured at the consumer.
            /// </remarks>
            public string PaymentRedirectUrl { get; set; }

            /// <summary>
            /// Gets or sets the url to navigate to when the user cancels the payment.
            /// </summary>
            /// <remarks>
            /// If the url does not start with https://, it is assumed to be a path relative to the CancelUrl configured at the consumer.
            /// </remarks>
            public string CancelUrl { get; set; }

            /// <summary>
            /// Gets or sets the name of the template to use for the resulting html.
            /// </summary>
            public string TemplateName { get; set; }
        }

        /// <summary>
        /// Parameters for the order.
        /// </summary>
        public class OrderInfo
        {
            /// <summary>
            /// Gets or sets the amount to pay.
            /// </summary>
            [Required(ErrorMessage = "The amount is required")]
            public decimal Amount { get; set; }

            /// <summary>
            /// Gets or sets the currency of the amoutn to pay.
            /// </summary>
            [Required(ErrorMessage = "The currency is required")]
            public string Currency { get; set; }


            /// <summary>
            /// Gets or sets the optional description for the order.
            /// </summary>
            public string Description { get; set; }

            /// <summary>
            /// Gets or sets the optional id for the order.
            /// If none provided, the transactionId will be used.
            /// </summary>
            public string OrderId { get; set; }

            // <summary>
            /// Gets or sets the legal entity ID.
            /// </summary>
            /// <value>The unique identifier for the legal entity involved in the payment.</value>
            public Guid LegalEntityId { get; set; }

            /// <summary>
            /// Gets or sets the currency ID.
            /// </summary>
            /// <value>The unique identifier for the currency type used in the transaction.</value>
            public Guid CurrencyId { get; set; }
        }
    }

    /// <summary>
    /// Parameters for the customer (the user that makes the payment).
    /// </summary>
    public class CardholderInfo
    {
        /// <summary>
        /// Gets or sets the name of the cardholders company.
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// Gets or sets the first name of the cardholder (optional).
        /// </summary>
        public string FirstName { get; set; }

        /// <summary>
        /// Gets or sets the last name of the cardholder (optional).
        /// </summary>
        public string LastName { get; set; }

        /// <summary>
        /// Gets or sets the email address of the cardholder.
        /// </summary>
        [EmailAddress]
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets the phonenumber of the cardholder.
        /// </summary>
        public string PhoneNumber { get; set; }
    }

    /// <summary>
    /// Parameters for the merchant.
    /// </summary>
    public class MerchantInfo
    {
        /// <summary>
        /// Gets or sets the email address of the merchant to receive a Transaction Receipt.
        /// </summary>
        [EmailAddress]
        public string Email { get; set; }
    }
}