using System.ComponentModel.DataAnnotations;

namespace NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Output
{
    public class CxPayTransactionDTO
    {
        public Guid Id { get; set; }

        public Guid PaymentId { get; set; }

        [Required(ErrorMessage = "The transaction amount is required")]
        public decimal Amount { get; set; }

        public string Result { get; set; }
        public string ResultCode { get; set; }
        public string ResultMessage { get; set; }
        public string TransactionId { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }

        [Required(ErrorMessage = "The status is required")]
        public string Status { get; set; }

        public string CardDigits { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string State { get; set; }

        [Required(ErrorMessage = "The reference id is required")]
        public Guid ReferenceId { get; set; }

        public string ZipCode { get; set; }
        public string Company { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? PaidAt { get; set; }
        public bool IsFinished { get; set; }
        public Guid ProviderId { get; set; }
        public Guid LegalEntityId { get; set; }
        public Guid CurrencyId { get; set; }
        public List<Guid> InvoiceIds { get; set; }
    }
}