namespace NetProGroup.Trust.Application.Contracts.Payments.Processor
{
    public class PaymentProcessorResponse
    {
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public string TransactionId { get; set; }
        public string CallBackUrl { get; set; }
        public string ResultMessage { get; set; }
        public Object Transaction { get; set; }
        public string htmlContent { get; set; }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
        public int ResultCode { get; set; }
        public int ResultNumber { get; set; }
    }
}