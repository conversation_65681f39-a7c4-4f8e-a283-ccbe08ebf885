using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.Payments.Models.PaymentsAPI.V2;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Output;
using StartPaymentRequest = NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Input.StartPaymentRequest;

namespace NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Services
{
    /// <summary>
    /// Interface for the Transactions in CX payments.
    /// </summary>
    public interface ICxPayTransactionHandler : IScopedService
    {
        /// <summary>
        /// Starts a payment attempt and generate a response depending of the payment provider.
        /// </summary>
        /// <param name="consumer">The consumer that is starting the payment</param>
        /// <param name="request">Request holding the payment data</param>
        /// <returns>A PaymentResponse object</returns>
        Task<StartPaymentResponse> StartPaymentAsync(StartPaymentRequest request);

        /// <summary>
        /// Submits a payment to the provider.
        /// </summary>
        /// <param name="request">Request holding the submission data</param>
        /// <returns>A CompletionResponse object</returns>
        Task<CxCompletionResponse> SubmitPaymentAsync(SubmitPaymentRequest request);

        /// <summary>
        /// Completes the payment identified by the transactionId, using the provided token.
        /// </summary>
        /// <param name="transactionId">The id that identifies the transaction in the database</param>
        /// <param name="token">The token provided by the payment provider</param>
        /// <returns>A CompletionResponse object</returns>
        Task<CxCompletionResponse> CompletePaymentAsync(Guid transactionId, string token);

        /// <summary>
        /// Updates some parts of a payment transaction.
        /// </summary>
        /// <param name="request" />
        /// <returns></returns>
        Task<PaymentTransactionModel> UpdateTransactionAsync(UpdatePaymentRequest request);

        /// <summary>
        /// Gets a specific transaction.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<PaymentTransactionModel> GetPaymentTransactionAsync(Guid id);

        /// <summary>
        /// Get the transactions for a specific referenceid.
        /// </summary>
        /// <param name="referenceId"></param>
        /// <returns></returns>
        Task<IEnumerable<PaymentTransactionModel>> GetPaymentTransactionByIdAsync(Guid referenceId);
    }
}