using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Payments.Models.PaymentsAPI.V2;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Payments.Provider;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.CXPayStatus;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Enums;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Output;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Services;
using NetProGroup.Trust.Payment.Provider.Status;
using NetProGroup.Trust.Payment.Provider.Validator;
using StartPaymentRequest = NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Input.StartPaymentRequest;

namespace NetProGroup.Trust.Payment.Provider.CXPay;

public class CxPayTransactionHandler : ICxPayTransactionHandler
{
    private readonly ILogger _logger;
    private readonly IPaymentRepository _paymentRepository;
    private readonly IInvoiceRepository _invoiceRepository;
    private readonly ISubmissionsRepository _submissionsRepository;
    private readonly IPaymentTransactionRepository _paymentTransactionRepository;
    private readonly IMapper _mapper;
    private readonly ICxPaymentService _cxPayService;
    private readonly IDateTimeProvider _dateTimeProvider;
    private readonly ITransactionValidator _transactionValidator;

    /// <summary>
    /// PaymentController constructor
    /// </summary>
    /// <param name="mapper"></param>
    /// <param name="logger"></param>
    /// <param name="paymentRepository"></param>
    /// <param name="invoiceRepository"></param>
    /// <param name="submissionsRepository"></param>
    /// <param name="paymentTransactionRepository"></param>
    /// <param name="cxPayService"></param>
    /// <param name="dateTimeProvider"></param>
    /// <param name="transactionValidator"></param>
    public CxPayTransactionHandler(
        IMapper mapper,
        ILogger<CxPayTransactionHandler> logger,
        IPaymentRepository paymentRepository,
        IInvoiceRepository invoiceRepository,
        ISubmissionsRepository submissionsRepository,
        IPaymentTransactionRepository paymentTransactionRepository,
        ICxPaymentService cxPayService,
        IDateTimeProvider dateTimeProvider,
        ITransactionValidator transactionValidator)
    {
        _logger = logger;
        _paymentRepository = paymentRepository;
        _invoiceRepository = invoiceRepository;
        _submissionsRepository = submissionsRepository;
        _mapper = mapper;
        _cxPayService = cxPayService;
        _dateTimeProvider = dateTimeProvider;
        _transactionValidator = transactionValidator;
        _paymentTransactionRepository = paymentTransactionRepository;
    }

    /// <summary>
    /// Starts a payment attempt and generate a response depending of the payment provider.
    /// </summary>
    /// <param name="request">Request holding the payment data</param>
    /// <exception cref="ArgumentException"></exception>
    /// <returns></returns>
    public async Task<StartPaymentResponse> StartPaymentAsync(StartPaymentRequest request)
    {
        var payment = await _paymentRepository.GetByIdAsync(request.PaymentId,
            q => q.Include(p => p.PaymentTransactions)
                .Include(p => p.PaymentInvoices));

        await _transactionValidator.ValidateTransactionAsync(payment);

        var response = new StartPaymentResponse();
        var paymentProvider = GetPaymentProvider(payment);
        // Prepare and create the transaction in the database
        CxPayTransactionDTO payTransaction = new CxPayTransactionDTO()
        {
            Status = TransactionStatus.IN_PROGRESS,
            PaymentId = request.PaymentId,
            ProviderId = paymentProvider.Id
        };
        var transactionFromDb = StartTransaction(payTransaction);

        // Call the correct payment provider
        request.PaymentProvider = paymentProvider;
        response = StartCXPay(request, transactionFromDb);
        return await Task.FromResult(response);
    }

    /// <summary>
    /// Completes the payment identified by the transactionId, using the provided token.
    /// </summary>
    /// <param name="transactionId">The id that identifies the transaction in the database</param>
    /// <param name="tokenId">The token provided by the payment provider</param>
    /// <returns>A CompletionResponse object</returns>
    public async Task<CxCompletionResponse> CompletePaymentAsync(Guid transactionId, string tokenId)
    {
        _logger.LogInformation("Completing payment for transaction '{0}' with token '{1}'", transactionId, tokenId);

        if (transactionId == Guid.Empty)
            throw new ArgumentException("TransactionId must be a valid Guid", nameof(transactionId));

        if (string.IsNullOrEmpty(tokenId))
            throw new ArgumentNullException(nameof(tokenId));

        var response = new CxCompletionResponse();

        // Get the transaction that we're completing
        var transactionPayment = await _paymentTransactionRepository.GetQueryable()
            .Include(x => x.Payment)
            .Include(p => p.PaymentProvider)
            .FirstOrDefaultAsync(x => x.Id == transactionId);

        if (transactionPayment == null)
        {
            throw new NotFoundException(ApplicationErrors.PAYMENT_TRANSACTION_NOT_FOUND.ToErrorCode(), $"No transaction found with transaction id '{transactionId}'");
            throw new ApplicationException($"No transaction found with transaction id '{transactionId}'");
        }

        // Get the provider
        var providerFromDb = transactionPayment.PaymentProvider;

        if (providerFromDb == null)
        {
            throw new ApplicationException($"ProviderId for transaction with transaction id '{transactionId}' not found");
        }

        _logger.LogInformation($"CompleteRequest for transaction '{transactionId}'");

        // Is the transaction already finished?
        if (transactionPayment.IsFinished)
        {
            response.TransactionId = transactionPayment.Id;
            response.ProviderTransactionId = transactionPayment.TransactionId;

            response.Result = int.Parse(transactionPayment.Result);
            response.ResultText = transactionPayment.ResultMessage;
            response.ResultNumber = int.Parse(transactionPayment.ResultCode);

            response.PaymentTransaction = await GetPaymentTransactionAsync(response.TransactionId);

            return response;
        }

        // Call the correct payment provider

        CompleteCXPay(tokenId, transactionPayment, providerFromDb);

        // Map result to the response
        response.TransactionId = transactionPayment.Id;
        response.ProviderTransactionId = transactionPayment.TransactionId;

        response.Result = int.Parse(transactionPayment.Result);
        response.ResultText = transactionPayment.ResultMessage;
        response.ResultNumber = int.Parse(transactionPayment.ResultCode);

        // Store the transaction in the database
        await _paymentTransactionRepository.SaveChangesAsync();

        response.PaymentTransaction = await GetPaymentTransactionAsync(response.TransactionId);

        return response;
    }

    /// <summary>
    /// Completes the payment identified by the transactionId, using the provided token.
    /// </summary>
    /// <param name="transactionId">The id that identifies the transaction in the database</param>
    /// <param name="tokenId">The token provided by the payment provider</param>
    /// <returns>A CompletionResponse object</returns>
    public async Task<CxCompletionResponse> SubmitPaymentAsync(SubmitPaymentRequest request)
    {
        _logger.LogInformation("Submitting payment for transaction '{0}'", request.TransactionId);

        ArgumentNullException.ThrowIfNull(request);

        if (request.Cardholder == null)
            throw new ArgumentNullException(nameof(request.Cardholder));

        if (request.Billing == null)
            throw new ArgumentNullException(nameof(request.Billing));

        var response = new CxCompletionResponse();

        // Get the transaction that we're completing
        var transactionFromDb = await _paymentTransactionRepository.GetQueryable().Include(p => p.Payment)
            .FirstOrDefaultAsync(x => x.Id == request.TransactionId);

        if (transactionFromDb == null)
        {
            throw new ApplicationException($"No transaction found with transaction id '{request.TransactionId}'");
        }

        // Populate the form variable
        var formVariables = new List<KeyValuePair<string, string>>
            {
                new("inputTotalAmount", Domain.Shared.Utilities.Formatter.FormatAmount(transactionFromDb.Payment.Amount)),
                new("billing-first-name", request.Cardholder.FirstName),
                new("billing-last-name", request.Cardholder.LastName),
                new("billing-phone", request.Cardholder.Phone),
                new("billing-cc-number", request.Billing.CardNumber),
                new("billing-cvv", request.Billing.CVVNumber),
                new("billing-cc-exp", request.Billing.ExpirationMonth.ToString("00") + request.Billing.ExpirationYear.ToString("00")),
                new("billing-address1", request.Billing.Address),
                new("billing-city", request.Billing.City),
                new("billing-state", request.Billing.State),
                new("billing-postal", request.Billing.PostalCode),
                new("billing-country", request.Billing.CountryCode)
            };


        var formContent = new FormUrlEncodedContent(formVariables);

        // submit the form, this should return a redirect response
        HttpClientHandler handler = new HttpClientHandler();
        handler.AllowAutoRedirect = false;

        HttpClient client = new HttpClient(handler);
        var submisisonResponse = await client.PostAsync(request.SubmissionUrl, formContent);

        // Get the url that th euser is normally redircted to
        var redirectUri = submisisonResponse.Headers.Location;

        // Get the the parameters from the url
        var queryParameters = GetQueryStringParameters(EnsureValidUri(redirectUri!.ToString()));

        Guid transactionId = Guid.Empty;
        string tokenId = "";

        if (queryParameters.ContainsKey("transactionid"))
        {
            transactionId = Guid.Parse(queryParameters["transactionid"].ToString());
        }
        if (queryParameters.ContainsKey("token-id"))
        {
            tokenId = queryParameters["token-id"].ToString();
        }

        // Complete the payment
        response = await CompletePaymentAsync(transactionId, tokenId);

        return response;
    }


    /// <summary>
    /// Updates a transaction with some information.
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    public async Task<PaymentTransactionModel> UpdateTransactionAsync(UpdatePaymentRequest request)
    {
        _logger.LogInformation("Updating transaction '{0}' with VoucherReceiptNumber '{1}'", request.TransactionId, request.VoucherReceiptNumber);

        ArgumentNullException.ThrowIfNull(request);

        if (request.TransactionId == Guid.Empty)
            throw new ArgumentException(nameof(request.TransactionId));

        // Get the transaction that we're updating
        var transactionFromDb = await _paymentTransactionRepository.GetByIdAsync(request.TransactionId);

        if (transactionFromDb == null)
            throw new NotFoundException($"Transaction '{request.TransactionId}' not found");


        await _paymentTransactionRepository.SaveChangesAsync();

        return await GetPaymentTransactionAsync(request.TransactionId);
    }

    /// <summary>
    /// Gets a specific transaction
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<PaymentTransactionModel> GetPaymentTransactionAsync(Guid id)
    {
        if (id == Guid.Empty)
            throw new ArgumentException(nameof(id));

        // Get the transaction that we're looking for
        var transactionFromDb = await _paymentTransactionRepository.GetByIdAsync(id);

        if (transactionFromDb == null)
            throw new NotFoundException($"Transaction '{id}' not found");

        return await Task.FromResult(MapToPaymentTransactionModel(transactionFromDb));
    }

    /// <summary>
    /// Gets transactions for a sepcific referenceid. Can have multiple transactions when there are new attempts.
    /// </summary>
    /// <param name="referenceId"></param>
    /// <returns></returns>
    public async Task<IEnumerable<PaymentTransactionModel>> GetPaymentTransactionByIdAsync(Guid Id)
    {
        if (Id == Guid.Empty)
            throw new ArgumentException(nameof(Id));

        // Get the transactions that we're looking for
        var transactionsFromDb = await _paymentTransactionRepository.FindByConditionAsync(t => t.Id == Id);

        var result = new List<PaymentTransactionModel>();

        transactionsFromDb.ToList().ForEach(t => result.Add(MapToPaymentTransactionModel(t)));

        return await Task.FromResult(result);
    }

    public static Uri EnsureValidUri(string uriString)
    {
        // Remove any duplicate paths if present
        if (uriString.Contains("/form/payments.com/form"))
        {
            uriString = uriString.Replace("/form/payments.com/form", "/form");
        }

        // Check if the URI starts with a protocol
        if (!uriString.StartsWith("http://", StringComparison.OrdinalIgnoreCase) &&
            !uriString.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
        {
            // Add https:// as the default protocol
            uriString = "https://" + uriString;
        }

        // Create and validate the URI
        if (Uri.TryCreate(uriString, UriKind.Absolute, out Uri? resultUri))
        {
            return resultUri;
        }

        throw new UriFormatException($"Unable to create a valid URI from: {uriString}");
    }

    private PaymentProvider GetPaymentProvider(Domain.Payments.Payment payment)
    {
        var paymentInvoice = payment.PaymentInvoices.FirstOrDefault();
        return _invoiceRepository.GetPaymentProviderSettingsForCompany(paymentInvoice?.InvoiceId ?? Guid.Empty);
    }


    private CxPayTransactionDTO StartTransaction(CxPayTransactionDTO payTransaction)
    {
        // Check if the transaction already exists and is in progress
        var transactionCheck = _paymentTransactionRepository.GetQueryable()
            .Include(p => p.Payment)
            .FirstOrDefault(t => t.Id == payTransaction.Id && t.Status == CxTransactionStatus.IN_PROGRESS.ToString());

        if (transactionCheck == null)
        {
            // Create a new Payment instance
            var paymentTransaction = new PaymentTransaction
            {
                PaymentId = payTransaction.PaymentId,
                Result = payTransaction.Result,
                ResultCode = payTransaction.ResultCode,
                ResultMessage = payTransaction.ResultMessage,
                TransactionId = payTransaction.TransactionId,
                Status = payTransaction.Status,
                CardDigits = payTransaction.CardDigits,
                ProcessCreatedAt = _dateTimeProvider.UtcNow,
                PaidAt = payTransaction.PaidAt,
                IsFinished = payTransaction.IsFinished,
                FirstName = payTransaction.FirstName,
                LastName = payTransaction.LastName,
                Address = payTransaction.Address,
                City = payTransaction.City,
                State = payTransaction.State,
                ZipCode = payTransaction.ZipCode,
                Company = payTransaction.Company,
                PhoneNumber = payTransaction.PhoneNumber,
                Email = payTransaction.Email,
                PaymentProviderId = payTransaction.ProviderId
            };
            // Insert the payment (along with the nested transaction) into the database
            _paymentTransactionRepository.Insert(paymentTransaction);
            _paymentTransactionRepository.SaveChanges();

            return _mapper.Map<CxPayTransactionDTO>(paymentTransaction);
        }

        // Update existing transaction
        _logger.LogInformation("Reusing existing transaction '{1}' ", payTransaction.Id);

        transactionCheck.Payment.Amount = payTransaction.Amount;
        transactionCheck.Payment.Status = PaymentStatus.InProgress;
        transactionCheck.Result = payTransaction.Result;
        transactionCheck.ResultCode = payTransaction.ResultCode;
        transactionCheck.ResultMessage = payTransaction.ResultMessage;
        transactionCheck.TransactionId = payTransaction.TransactionId;
        transactionCheck.ProcessCreatedAt = _dateTimeProvider.UtcNow;
        transactionCheck.PaidAt = payTransaction.PaidAt;
        transactionCheck.IsFinished = payTransaction.IsFinished;

        // Update in the database
        _paymentTransactionRepository.Update(transactionCheck);
        _paymentTransactionRepository.SaveChanges();

        return _mapper.Map<CxPayTransactionDTO>(transactionCheck);
    }

    #region Utils

    /// <summary>
    /// Maps a transaction from the database to a PaymentTransactionModel.
    /// </summary>
    /// <param name="transaction"></param>
    /// <returns></returns>
    private PaymentTransactionModel MapToPaymentTransactionModel(PaymentTransaction transaction)
    {
        var result = new PaymentTransactionModel
        {
            TransactionId = transaction.Id,
            ProviderTransactionId = transaction.TransactionId,
            Amount = transaction.Payment.Amount,
            Result = int.Parse(transaction.Result),
            ResultNumber = int.Parse(transaction.ResultCode),
            ResultText = transaction.ResultMessage,
            Status = transaction.Status,
            PaidAt = transaction.PaidAt,
            IsCompleted = transaction.IsFinished,
            Cardholder = new CardholderModel
            {
                FirstName = transaction.FirstName,
                LastName = transaction.LastName,
                CompanyName = transaction.Company,
                CardDigits = transaction.CardDigits
            }
        };

        if (transaction.PaymentProvider != null)
        {
            result.Provider = new ProviderModel
            {
                Id = transaction.PaymentProvider.Id,
                Name = transaction.PaymentProvider.Name
            };
        }

        return result;
    }

    /// <summary>
    /// From 'Tavis.UriTemplates.UriTemplateExtensions'
    /// </summary>
    /// <param name="target"></param>
    /// <returns></returns>
    public Dictionary<string, string> GetQueryStringParameters(Uri uri)
    {
        if (uri == null)
            throw new ArgumentNullException(nameof(uri));

        var parameters = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

        // If there's no query string, return empty dictionary
        if (string.IsNullOrEmpty(uri.Query))
            return parameters;

        // Remove the leading '?' if present
        string query = uri.Query.TrimStart('?');

        // Split the query string by '&' and process each parameter
        foreach (string pair in query.Split('&'))
        {
            // Skip empty pairs
            if (string.IsNullOrEmpty(pair))
                continue;

            int index = pair.IndexOf('=');
            if (index < 0)
            {
                // Handle parameters without values
                string key = Uri.UnescapeDataString(pair);
                parameters[key] = string.Empty;
            }
            else
            {
                string key = Uri.UnescapeDataString(pair.Substring(0, index));
                string value = Uri.UnescapeDataString(pair.Substring(index + 1));

                // If key already exists, latest value wins
                parameters[key] = value;
            }
        }

        return parameters;
    }

    #endregion

    #region Provider specific

    /// <summary>
    /// Starts a payment using CX Pay
    /// </summary>
    /// <param name="request"></param>
    /// <param name="consumer"></param>
    /// <param name="payTransaction"></param>
    /// <returns></returns>
    private StartPaymentResponse StartCXPay(StartPaymentRequest request,
                                       CxPayTransactionDTO payTransaction)
    {
        StartPaymentResponse result = new StartPaymentResponse();


        // If no orderid was given then use the id of the transaction in the database
        if (string.IsNullOrEmpty(request.Order.OrderId))
        {
            request.Order.OrderId = payTransaction.Id.ToString();
        }


        // Prepare urls for callbacks and cancellation

        var callbackBaseUrl = request.Flow.PaymentRedirectUrl;
        if (!callbackBaseUrl.EndsWith("/"))
        {
            callbackBaseUrl += "/";
        }

        // The default payment endpoint is set to /PaymentResult
        string redirectUrl = $"{callbackBaseUrl}PaymentResult?transactionId={payTransaction.Id}";

        // Did the consumer specify a url for the completion of the payment?
        if (!string.IsNullOrEmpty(request.Flow.PaymentRedirectUrl))
        {
            // Full url or relative?
            var paymentRedirectUrl = request.Flow.PaymentRedirectUrl.ToLowerInvariant();
            if (Uri.TryCreate(paymentRedirectUrl, UriKind.Absolute, out var uri) && (uri.Scheme == Uri.UriSchemeHttp || uri.Scheme == Uri.UriSchemeHttps))
            {
                redirectUrl = $"{paymentRedirectUrl}?transactionId={payTransaction.Id}";
            }
            else
            {
                redirectUrl = $"{callbackBaseUrl}{request.Flow.PaymentRedirectUrl}?transactionId={payTransaction.Id}";
            }
        }

        // The cancellation url
        var cancelBaseUrl = request.Flow.CancelUrl;
        if (string.IsNullOrEmpty(cancelBaseUrl))
        {
            cancelBaseUrl = callbackBaseUrl;
        }
        if (!cancelBaseUrl.EndsWith("/"))
        {
            cancelBaseUrl += "/";
        }

        string cancelUrl = $"{cancelBaseUrl}{request.Flow.CancelUrl}";
        if (!string.IsNullOrEmpty(request.Flow.CancelUrl) && request.Flow.CancelUrl.ToLower().StartsWith("https://"))
        {
            cancelUrl = request.Flow.CancelUrl;
        }
        var transactionPayment = _paymentTransactionRepository.GetQueryable()
            .Include(x => x.Payment)
            .Include(p => p.PaymentProvider)
            .FirstOrDefault(x => x.Id == payTransaction.Id);
        var providerFromDb = transactionPayment?.PaymentProvider;

        if (providerFromDb == null)
        {
            throw new ApplicationException($"ProviderId for transaction with transaction id '{payTransaction.Id}' not found");
        }

        request.Flow.PaymentGatewayUrl = providerFromDb.BaseUrl;
        request.Order.Amount = transactionPayment!.Payment.Amount;

        // Perform Step 1
        var cxPayResponse = _cxPayService.ProcessRequest(request, redirectUrl, payTransaction.Id);

        result.TransactionId = payTransaction.Id;
        result.CallBackUrl = cxPayResponse.FormUrl;
        result.ProviderTransactionId = cxPayResponse.TransactionId;
        result.ResultText = cxPayResponse.ResultText;
        result.ResultNumber = cxPayResponse.ResultCode;
        result.Result = cxPayResponse.Result;
        return result;
    }

    /// <summary>
    /// Completes a payment using CX Pay
    /// </summary>
    /// <param name="tokenId"></param>
    /// <param name="transaction"></param>
    private void CompleteCXPay(string tokenId, PaymentTransaction transaction, PaymentProvider provider)
    {
        // Perform Step 3
        var cxPayResponse = _cxPayService.CompletePayment(provider.BaseUrl, tokenId, provider.ApiKey);

        transaction.TransactionId = cxPayResponse.TransactionId;
        transaction.Result = cxPayResponse.Result.ToString();
        transaction.ResultCode = cxPayResponse.ResultCode.ToString();
        transaction.ResultMessage = cxPayResponse.ResultText;
        transaction.IsFinished = true;

        _logger.LogInformation($"Result of CompleteRequest for transaction '{transaction.Id}' is {cxPayResponse.ResultText} ({cxPayResponse.Result}/{cxPayResponse.ResultCode})");

        switch (cxPayResponse.Result)
        {
            case 1:
                transaction.Status = CxPaymentStatus.APPROVED.ToString();
                transaction.IsFinished = true;
                transaction.PaidAt = _dateTimeProvider.UtcNow;
                transaction.Payment.PaidAt = _dateTimeProvider.UtcNow;
                transaction.Payment.Status = PaymentStatus.Completed;

                if (cxPayResponse.CxPaymentBilling != null)
                {
                    transaction.FirstName = cxPayResponse.CxPaymentBilling.firstName;
                    transaction.PhoneNumber = cxPayResponse.CxPaymentBilling.phone;
                    transaction.Email = cxPayResponse.CxPaymentBilling.email;
                    transaction.CardDigits = cxPayResponse.CxPaymentBilling.CardDigits;
                    transaction.LastName = cxPayResponse.CxPaymentBilling.lastName;
                    transaction.Address = cxPayResponse.CxPaymentBilling.address;
                    transaction.City = cxPayResponse.CxPaymentBilling.city;
                    transaction.State = cxPayResponse.CxPaymentBilling.state;
                    transaction.ZipCode = cxPayResponse.CxPaymentBilling.postal;
                    transaction.Company = cxPayResponse.CxPaymentBilling.company;
                }
                _submissionsRepository.UpdateSubmissionPaymentStatus(transaction.Id);
                break;
            case 2:
                transaction.Status = CxPaymentStatus.DECLINED.ToString();
                transaction.Payment.Status = PaymentStatus.Decline;
                if (cxPayResponse.CxPaymentBilling != null)
                {
                    transaction.FirstName = cxPayResponse.CxPaymentBilling.firstName;
                    transaction.PhoneNumber = cxPayResponse.CxPaymentBilling.phone;
                    transaction.Email = cxPayResponse.CxPaymentBilling.email;
                    transaction.CardDigits = cxPayResponse.CxPaymentBilling.CardDigits;
                    transaction.LastName = cxPayResponse.CxPaymentBilling.lastName;
                    transaction.Address = cxPayResponse.CxPaymentBilling.address;
                    transaction.City = cxPayResponse.CxPaymentBilling.city;
                    transaction.State = cxPayResponse.CxPaymentBilling.state;
                    transaction.ZipCode = cxPayResponse.CxPaymentBilling.postal;
                    transaction.Company = cxPayResponse.CxPaymentBilling.company;
                }

                break;
            case 3:
                // Completed before?
                if (cxPayResponse.ResultCode == 300 && cxPayResponse.ResultText.Equals("Transaction previously completed", StringComparison.OrdinalIgnoreCase))
                {
                    //transaction.Status = Shared.Enum.TransactionStatus.APPROVED.ToString();
                }
                else
                {
                    transaction.Status = CxPaymentStatus.ERROR.ToString();
                    transaction.Payment.Status = PaymentStatus.Failed;
                }

                break;
        }
    }

    #endregion
}