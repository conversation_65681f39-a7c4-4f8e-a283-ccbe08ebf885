using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Common;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Payment.Provider.Status;

namespace NetProGroup.Trust.Payment.Provider.Validator;

public class TransactionValidator : ITransactionValidator
{
    private readonly IPaymentRepository _paymentRepository;
    private readonly IDateTimeProvider _dateTimeProvider;

    /// <summary>
    /// Initializes a new instance of the <see cref="TransactionValidator"/> class.
    /// </summary>
    /// <param name="paymentRepository">The payment repository.</param>
    /// <param name="dateTimeProvider">The datetime provider.</param>
    public TransactionValidator(IPaymentRepository paymentRepository, IDateTimeProvider dateTimeProvider)
    {
        _paymentRepository = paymentRepository;
        _dateTimeProvider = dateTimeProvider;
    }

    /// <summary>
    /// This method validates transaction is eligible to start a new payment.
    /// </summary>
    /// <param name="paymentId">THe GUID of the payment to validate.</param>
    /// <returns>The <see cref="Task"/> representing the asynchronous operation.</returns>
    /// <exception cref="NotImplementedException">NUllReferenceException.</exception>
    public async Task ValidateTransactionAsync(Domain.Payments.Payment payment)
    {
        if (payment is null)
        {
            throw new ConstraintException(ApplicationErrors.PAYMENT_TRANSACTION_NOT_FOUND.ToErrorCode(), "Payment transaction not found");
        }

        if (payment.Status == PaymentStatus.Completed)
        {
            throw new ConstraintException(ApplicationErrors.PAYMENT_TRANSACTION_COMPLETED.ToErrorCode(), "Payment transaction is completed");
        }
        if (IsDeletablePayment(payment))
        {
            await _paymentRepository.SoftDeletePayment(payment);
        }

        if (payment.ExpirationDate <= _dateTimeProvider.UtcNow)
        {

            throw new ConstraintException(ApplicationErrors.PAYMENT_TRANSACTION_HAS_EXPIRED.ToErrorCode(), "transaction has expired");
        }

        if (payment.PaymentTransactions.Any(t => t.Status == TransactionStatus.IN_PROGRESS))
        {
            throw new ConstraintException(ApplicationErrors.PAYMENT_TRANSACTION_IN_PROGRESS.ToErrorCode(), "Payment transaction is in progress");
        }

        if (payment.PaymentTransactions.Any(t => t.Status == TransactionStatus.COMPLETED))
        {
            throw new ConstraintException(ApplicationErrors.PAYMENT_TRANSACTION_COMPLETED.ToErrorCode(), "Payment transaction is Completed");
        }
    }

    private bool IsDeletablePayment(Domain.Payments.Payment payment)
    {
        bool isInValidPayment = !payment.IsDeleted && payment.ExpirationDate <= _dateTimeProvider.UtcNow;
        bool isTerminalStatus = payment.Status is PaymentStatus.InProgress or
            PaymentStatus.Completed or PaymentStatus.Pending;
        return isInValidPayment || !isTerminalStatus;
    }
}