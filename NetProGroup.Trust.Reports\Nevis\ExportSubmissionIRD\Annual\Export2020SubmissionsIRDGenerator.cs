using ClosedXML.Excel;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual
{
    /// <summary>
    /// Interface for exporting 2020 STR submissions.
    /// </summary>
    public interface IExport2020SubmissionsIRDGenerator : IExportYearSubmissionsIRDGenerator, IScopedService;

    /// <summary>
    /// Manager for exporting 2020 submissions.
    /// </summary>
    public class Export2020SubmissionsIRDGenerator : BaseExportSubmissionsIRDGenerator, IExport2020SubmissionsIRDGenerator
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Export2020SubmissionsIRDGenerator"/> class.
        /// </summary>
        /// <param name="logger">the logger.</param>
        /// <param name="submissionsManager">The submissions manager.</param>
        /// <param name="templateProvider">template provider.</param>
        public Export2020SubmissionsIRDGenerator(ILogger<BaseExportSubmissionsIRDGenerator> logger,
            ISubmissionReportsDataManager submissionsManager,
            IReportTemplateProvider templateProvider) : base(logger, templateProvider, submissionsManager)
        {
        }

        /// <inheritdoc />
        protected override int Year { get => 2020; }

        /// <inheritdoc />
        protected override string ExportModule { get => ModuleKeyConsts.SimplifiedTaxReturn; }

        /// <inheritdoc />
        protected override string ExportJurisdiction { get => JurisdictionCodes.Nevis; }

        /// <inheritdoc/>
        public override async Task<ReportDownloadResponseDTO> ExportAsync(ExportSubmissionDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var template = await GetTemplateContentAsync();

            // Modify the file in memory with ClosedXML
            using var workbook = new XLWorkbook(template);
            var submissions = await GetSubmissions(request);

            ModifyWorkbook(workbook, submissions);

            // Save modified workbook to a new FileStream stream
            var modifiedStream = new MemoryStream();
            workbook.SaveAs(modifiedStream);
            return CreateResponse(modifiedStream);
        }

        private void ModifyWorkbook(XLWorkbook workbook, List<Submission> submissions)
        {
            ModifySubmissionTabTemplate(workbook, submissions);
            ModifySchedule1TabTemplate(workbook, submissions);
        }

        private void ModifySubmissionTabTemplate(XLWorkbook workbook, List<Submission> submissions)
        {
            ProcessSubmissionsTab(workbook, submissions, form =>
            {
                var values = new List<XLCellValue>();
                var haveIncomeGenerated = bool.Parse(GetValueOrDefault(form, FormKeys.CorporateAccountingAssessableIncomeGenerated, "false"));
                var incorporatedBefore2019 = bool.Parse(GetValueOrDefault(form, FormKeys.TaxResidentIncorporatedBefore2019, "false"));
                var nonTaxResident = bool.Parse(GetValueOrDefault(form, FormKeys.TaxResidentNonTaxResident, "false"));
                var isPartOfMneGroup = Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateMNEIsPartOfMNEGroup, "false"));
                var requiresCbeReport = Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateMNERequiresCbCReport, "false"));

                // Question No 1 (Yes/No)
                values.Add(incorporatedBefore2019 ? "True" : "False");

                // Question No 1.1 (Yes/No)
                values.Add(incorporatedBefore2019
                    ? ""
                    : nonTaxResident
                        ? "True"
                        : "False");

                // Question No 1.2 (Yes/No)
                values.Add(nonTaxResident
                    ? ""
                    : GetCountryOrNoCountry(GetValueOrDefault(form, FormKeys.TaxResidentResidentCountry)));

                // Question No 2 (Yes/No)
                values.Add((!incorporatedBefore2019 && !nonTaxResident)
                    ? ""
                    : haveIncomeGenerated
                        ? "True"
                        : "False");

                // Question No 2.1 (Yes/No) 
                values.Add((!incorporatedBefore2019 && !nonTaxResident && !haveIncomeGenerated) ||
                           (incorporatedBefore2019 && !haveIncomeGenerated) ? "" :
                    Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesCondition, "false")) ? "True" :
                    "False");

                // Question No 3 (Yes/No)
                values.Add((!incorporatedBefore2019 && !nonTaxResident)
                    ? ""
                    : isPartOfMneGroup
                        ? "True"
                        : "False");

                // Question No 3.1 (Yes/No)
                values.Add(
                    (!incorporatedBefore2019 && !nonTaxResident)
                        ? ""
                        : isPartOfMneGroup
                            ? requiresCbeReport ? "True" : "False"
                            : "");

                return values;
            });
        }

        private void ModifySchedule1TabTemplate(XLWorkbook workbook, List<Submission> submissions)
        {
            var worksheet = workbook.Worksheet(2);

            int row = 3; // Starting row (assuming the first row is for headers)

            foreach (var submission in submissions)
            {
                var form = submission.FormDocument.FormDocumentRevisions.First().GetFormBuilder().Form as KeyValueForm;
                foreach (var index in GetIndexForAccountingActivities(form!.DataSet))
                {
                    // Column A: Morning_Star_Id
                    worksheet.Cell(row, 1).Value = submission.ReportId;

                    // Column B: CorporateAccountingActivitiesDescription
                    worksheet.Cell(row, 2).Value = GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesDescription(index));

                    // Column C: CorporateAccountingActivities
                    var activitiesArray = GetActivities(form, index);

                    worksheet.Cell(row, 3).Value = string.Join(", ", activitiesArray.Where(s => s != null));

                    // Column D: CorporateAccountingActivitiesIncome
                    worksheet.Cell(row, 4).Value = GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesIncome(index));

                    // Column E: Schedule Type
                    worksheet.Cell(row, 5).Value = "Schedule 1";

                    // Increment the row for the next submission
                    row++;
                }
            }
        }
    }
}
