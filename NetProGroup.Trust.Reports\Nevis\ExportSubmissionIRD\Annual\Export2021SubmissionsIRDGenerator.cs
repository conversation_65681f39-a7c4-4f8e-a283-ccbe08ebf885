using ClosedXML.Excel;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.ReportTemplates;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual
{
    /// <summary>
    /// Interface for exporting 2020 STR submissions.
    /// </summary>
    public interface IExport2021SubmissionsIRDGenerator : IExportYearSubmissionsIRDGenerator, IScopedService;

    /// <summary>
    /// Manager for exporting 2021 submissions.
    /// </summary>
    public class Export2021SubmissionsIRDGenerator : BaseExportSubmissionsIRDGenerator, IExport2021SubmissionsIRDGenerator
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Export2021SubmissionsIRDGenerator"/> class.
        /// </summary>
        /// <param name="logger">the logger.</param>
        /// <param name="submissionsManager">The submissions manager.</param>
        /// <param name="templateProvider">The template provider.</param>
        /// <summary/>
        public Export2021SubmissionsIRDGenerator(ILogger<BaseExportSubmissionsIRDGenerator> logger,
            ISubmissionReportsDataManager submissionsManager,
            IReportTemplateProvider templateProvider) : base(logger, templateProvider, submissionsManager)
        {
        }

        /// <inheritdoc />
        protected override int Year { get => 2021; }

        /// <inheritdoc />
        protected override string ExportModule { get => ModuleKeyConsts.SimplifiedTaxReturn; }

        /// <inheritdoc />
        protected override string ExportJurisdiction { get => JurisdictionCodes.Nevis; }

        /// <inheritdoc/>
        public override async Task<ReportDownloadResponseDTO> ExportAsync(ExportSubmissionDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var template = await GetTemplateContentAsync();

            // Modify the file in memory with ClosedXML
            using var workbook = new XLWorkbook(template);
            var submissions = await GetSubmissions(request);

            ModifyWorkbook(workbook, submissions);

            // Save modified workbook to a new FileStream stream
            var modifiedStream = new MemoryStream();
            workbook.SaveAs(modifiedStream);
            return CreateResponse(modifiedStream);
        }

        private void ModifyWorkbook(XLWorkbook workbook, List<Submission> submissions)
        {
            ModifySubmissionTabTemplate(workbook, submissions);
            ModifySchedule1TabTemplate(workbook, submissions);
        }

        private void ModifySubmissionTabTemplate(XLWorkbook workbook, List<Submission> submissions)
        {
            ProcessSubmissionsTab(workbook, submissions, form =>
            {
                var values = new List<XLCellValue>();
                var haveIncomeGenerated = bool.Parse(GetValueOrDefault(form, FormKeys.CorporateAccountingAssessableIncomeGenerated, "false"));
                var incorporatedBefore2019 = bool.Parse(GetValueOrDefault(form, FormKeys.TaxResidentIncorporatedBefore2019, "false"));
                var nonTaxResident = bool.Parse(GetValueOrDefault(form, FormKeys.TaxResidentNonTaxResident, "false"));
                var isPartOfMneGroup = Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateMNEIsPartOfMNEGroup, "false"));
                var requiresCbeReport = Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateMNERequiresCbCReport, "false"));

                // Question No 1 (Yes/No)
                values.Add(incorporatedBefore2019 ? "True" : "False");

                // Question No 1.1 (Yes/No)
                values.Add(incorporatedBefore2019
                    ? ""
                    : nonTaxResident
                        ? "True"
                        : "False");

                // Question No 1.2 (Yes/No)
                values.Add(nonTaxResident
                    ? ""
                    : GetCountryOrNoCountry(GetValueOrDefault(form, FormKeys.TaxResidentResidentCountry)));

                // Question No 2 (Yes/No)
                values.Add((!incorporatedBefore2019 && !nonTaxResident)
                    ? ""
                    : haveIncomeGenerated
                        ? "True"
                        : "False");

                // Question No 2.1 (Yes/No) 
                values.Add((!incorporatedBefore2019 && !nonTaxResident && !haveIncomeGenerated) ||
                           (incorporatedBefore2019 && !haveIncomeGenerated) ? "" :
                    Boolean.Parse(GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesCondition, "false")) ? "True" :
                    "False");

                // Question No 3 (Yes/No)
                values.Add((!incorporatedBefore2019 && !nonTaxResident)
                    ? ""
                    : isPartOfMneGroup
                        ? "True"
                        : "False");

                // Question No 3.1 (Yes/No)
                values.Add(
                    (!incorporatedBefore2019 && !nonTaxResident)
                        ? ""
                        : isPartOfMneGroup
                            ? requiresCbeReport ? "True" : "False"
                            : "");

                return values;
            });
        }

        private static void ModifySchedule1TabTemplate(XLWorkbook workbook, List<Submission> submissions)
        {
            var worksheet = workbook.Worksheet(2);

            int row = 3; // Starting row (assuming the first row is for headers)

            foreach (var submission in submissions)
            {
                var form = submission.FormDocument.FormDocumentRevisions.FirstOrDefault()?.GetFormBuilder().Form as KeyValueForm;
                foreach (var index in GetIndexForAccountingActivities(form!.DataSet))
                {
                    var activitiesArray = GetActivities(form, index);
                    var values = new List<XLCellValue>();

                    // Morning_Star_Id
                    values.Add(submission.ReportId);

                    // SCHEDULE_COL1
                    values.Add(GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesDescription(index)));

                    // SCHEDULE_COL2
                    values.Add(activitiesArray.Any() ? string.Join(", ", activitiesArray) : "Not Applicable, commenced new activity");

                    // SCHEDULE_COL3
                    values.Add(GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesYearIncome(index)));

                    // SCHEDULE_COL4
                    values.Add(!activitiesArray.Any() ? "New Activity" : "");

                    // SCHEDULE_COL5
                    values.Add(GetValueOrDefault(form, FormKeys.CorporateAccountingActivitiesIncome(index)));

                    // SCHEDULE_TYPE
                    values.Add("Schedule 1");

                    foreach (var (value, column) in values.Select((value, i) => (value, i + 1)))
                    {
                        worksheet.Cell(row, column).Value = value;
                    }

                    // Increment the row for the next submission
                    row++;
                }
            }
        }
    }
}