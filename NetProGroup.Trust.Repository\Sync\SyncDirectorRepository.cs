﻿// <copyright file="SyncDirectorRepository.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Sync;

namespace NetProGroup.Trust.Domain.Repository.Sync
{
    /// <summary>
    /// Repository for SyncBODirector.
    /// </summary>
    public class SyncDirectorRepository : RepositoryBase<TrustDbContext, SyncDirector, string>, ISyncDirectorRepository
    {
        private const string TableName = "Staging_PCP_Directors";

        /// <summary>
        /// Initializes a new instance of the <see cref="SyncDirectorRepository"/> class.
        /// </summary>
        /// <param name="dbContext">The DbContext to use in the repository.</param>
        public SyncDirectorRepository(TrustDbContext dbContext)
            : base(dbContext)
        {
        }

        /// <summary>
        /// Gets the DbContext.
        /// </summary>
        DbContext ISyncDirectorRepository.DbContext => base.DbContext;

        /// <inheritdoc/>
        public async Task<ICollection<SyncDirector>> GetChangedDirectorsAsync()
        {
            var sqlBldr = new StringBuilder();

            var clause = SQLBuilder.GetEntityJurisdictionClause("EntityCode");

            sqlBldr.AppendLine($"IF NOT (EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'dbo' AND  TABLE_NAME = '{TableName}History')) ");
            sqlBldr.AppendLine("BEGIN ");
            sqlBldr.AppendLine($"SELECT convert(varchar(50), newid()) as Id, * FROM [{TableName}] WHERE DirStatus <> 'resigned' AND EntityCode IN {clause}");
            sqlBldr.AppendLine("return ");
            sqlBldr.AppendLine("END");

            sqlBldr.AppendLine("SELECT convert(varchar(50), newid()) as Id, Dir.* ");
            sqlBldr.AppendLine($"FROM (SELECT DISTINCT * FROM dbo.{TableName} WHERE DirStatus <> 'resigned') AS Dir ");
            sqlBldr.AppendLine($"LEFT JOIN (SELECT DISTINCT * FROM dbo.{TableName}History) hist ON Dir.UniqueRelationID = hist.UniqueRelationID AND hist.DirStatus <> 'resigned'");
            sqlBldr.AppendLine("WHERE ( ");

            sqlBldr.AppendLine($"(Dir.EntityCode IN {clause}) AND (");

            SyncHelper.AddFieldComparison(sqlBldr, "Dir", AllFields());

            sqlBldr.AppendLine("))  ");
            sqlBldr.AppendLine("ORDER BY Dir.UniqueRelationID, Dir.ClientUniqueNr");

            var sql = sqlBldr.ToString();

            var result = await DbContext.Set<SyncDirector>().FromSqlRaw(sql).ToListAsync();

            return result;
        }

        /// <inheritdoc/>
        public async Task<ICollection<SyncDirector>> GetDeletedDirectorsAsync()
        {
            var sqlBldr = new StringBuilder();

            var fields = AllFields();

            fields.Remove("UniqueRelationID");
            fields.Remove("DirCode");
            fields.Remove("DirName");

            sqlBldr.AppendLine("SELECT convert(varchar(50), newid()) as Id, ");
            sqlBldr.AppendLine("D.ExternalUniqueId AS [UniqueRelationID], ");
            sqlBldr.AppendLine("D.Name AS [DirName], ");
            sqlBldr.AppendLine("D.Code AS [DirCode], ");

            SyncHelper.AddFieldsAsNull(sqlBldr, fields);

            sqlBldr.AppendLine("FROM dbo.Directors AS D ");
            sqlBldr.AppendLine($"LEFT JOIN dbo.{TableName} SD ON D.ExternalUniqueId = SD.UniqueRelationID ");
            sqlBldr.AppendLine("WHERE SD.DirUniqueNr is null");

            var sql = sqlBldr.ToString();

            var result = await DbContext.Set<SyncDirector>().FromSqlRaw(sql).ToListAsync();

            return result;
        }

        /// <inheritdoc/>
        public async Task<int> GetStagingCountAsync()
        {
            return await SyncHelper.GetStagingCountAsync(DbContext, TableName);
        }

        /// <inheritdoc/>
        public async Task<bool> StagingTableExistsAsync()
        {
            return await SyncHelper.StagingTableExistsAsync(DbContext, TableName);
        }

        /// <inheritdoc/>
        public async Task SaveLastStateAsync()
        {
            var clause = SQLBuilder.GetEntityJurisdictionClause("EntityCode");
            var whereClause = $"WHERE [EntityCode] IN {clause}";
            await SyncHelper.SaveLastStateAsync<SyncDirector>(DbContext, TableName, whereClause);
        }

        private List<string> AllFields()
        {
            return SyncHelper.GetStagingTableFieldsAsList<SyncDirector>(base.DbContext, includeId: false);
        }
    }
}
