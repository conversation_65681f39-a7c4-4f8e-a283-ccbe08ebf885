// <copyright file="LegalEntityOfficerTypes.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Legal entity officer type keys used for mapping.
    /// </summary>
    public static class LegalEntityOfficerTypes
    {
        /// <summary>
        /// Standard director officer type.
        /// </summary>
        public const string Director = "Director";

        /// <summary>
        /// Alternate director officer type.
        /// </summary>
        public const string AlternateDirector = "Alternate Director";

        /// <summary>
        /// Reserve director officer type.
        /// </summary>
        public const string ReserveDirector = "Reserve Director";

        /// <summary>
        /// BVI officer type code VGTP01.
        /// </summary>
        public const string VGTP01 = "VGTP01";

        /// <summary>
        /// BVI officer type code VGTP02.
        /// </summary>
        public const string VGTP02 = "VGTP02";

        /// <summary>
        /// BVI officer type code VGTP03.
        /// </summary>
        public const string VGTP03 = "VGTP03";

        /// <summary>
        /// BVI officer type code VGTP04.
        /// </summary>
        public const string VGTP04 = "VGTP04";

        /// <summary>
        /// BVI officer type code VGTP05.
        /// </summary>
        public const string VGTP05 = "VGTP05";

        /// <summary>
        /// BVI officer type code VGTP06.
        /// </summary>
        public const string VGTP06 = "VGTP06";

        /// <summary>
        /// Nevis officer type code KNTP01.
        /// </summary>
        public const string KNTP01 = "KNTP01";

        /// <summary>
        /// Nevis officer type code KNTP02.
        /// </summary>
        public const string KNTP02 = "KNTP02";

        /// <summary>
        /// Nevis officer type code KNTP03.
        /// </summary>
        public const string KNTP03 = "KNTP03";

        /// <summary>
        /// Nevis officer type code KNTP04.
        /// </summary>
        public const string KNTP04 = "KNTP04";

        /// <summary>
        /// Nevis officer type code KNTP05.
        /// </summary>
        public const string KNTP05 = "KNTP05";

        /// <summary>
        /// Nevis officer type code KNTP06.
        /// </summary>
        public const string KNTP06 = "KNTP06";
    }
}
