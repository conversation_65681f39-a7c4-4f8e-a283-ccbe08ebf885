// <copyright file="UserAttributeKeys.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Consts
{
    /// <summary>
    /// Attribute keys for UserAttributes.
    /// </summary>
    public static class UserAttributeKeys
    {
        /// <summary>
        /// Gets the key for the MFA Method setting.
        /// </summary>
        public const string MFAMethod = "MFAMethod";

        /// <summary>
        /// Gets the key for the MFA Enabled setting.
        /// </summary>
        public const string MFAEnabled = "MFAEnabled";

        /// <summary>
        /// Gets the key for the MFA Secret.
        /// </summary>
        public const string MFASecret = "MFASecret";

        /// <summary>
        /// Gets the key for the MFA Email Code.
        /// </summary>
        public const string MFAEmailCode = "MFAEmailCode";

        /// <summary>
        /// Gets the key for the MFA Email Code expiration.
        /// </summary>
        public const string MFAEmailCodeExpiration = "MFAEmailCodeExpiration";

        /// <summary>
        /// Gets the key for the MFA Email Code.
        /// </summary>
        public const string MFAResetEmailCode = "MFAResetEmailCode";

        /// <summary>
        /// Gets the key for the MFA Email Code expiration.
        /// </summary>
        public const string MFAResetEmailCodeExpiration = "MFAResetEmailCodeExpiration";

        /// <summary>
        /// Gets the key for the date/time of the last sent invitation.
        /// </summary>
#pragma warning disable SA1310 // Field names should not contain underscore
        public const string Invitation_Last_Sent = "Invitation.Last-Sent";
#pragma warning restore SA1310 // Field names should not contain underscore

        /// <summary>
        /// Gets the key for the date/time of the registration of the user.
        /// </summary>
        public const string RegistrationAt = "Registration.At";

        /// <summary>
        /// Gets the key for the Terms and Conditions accepted version.
        /// </summary>
        public const string TermsConditionsVersion = "TermsConditions.AcceptedVersion";
    }
}
