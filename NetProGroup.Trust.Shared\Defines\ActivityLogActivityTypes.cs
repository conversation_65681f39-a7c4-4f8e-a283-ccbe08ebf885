﻿// <copyright file="ActivityLogActivityTypes.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Defines
{
    /// <summary>
    /// Represents the various types for ActivityLog.
    /// </summary>
    public static class ActivityLogActivityTypes
    {
#pragma warning disable CS1591 // Missing XML comment for publicly visible type or member
#pragma warning disable SA1600 // Elements should be documented

        public const string UserAuthenticate = "user.authenticate";
        public const string UserLogout = "user.logout";
        public const string UserAdd = "user.add";
        public const string UserRegister = "user.register";
        public const string UserAddedToMasterClient = "user.added-to-master-client";
        public const string UserRemovedFromMasterClient = "user.removed-from-master-client";

        // Reset of MFA by user requested
        public const string UserMFAResetRequested = "user.mfa.reset.requested";

        // Reset of MFA by user confirmed
        public const string UserMFAResetConfirmed = "user.mfa.reset.confirmed";

        // Reset of MFA by management user
        public const string UserMFAReset = "user.mfa.reset";

        // Terms and Conditions accepted by user
        public const string UserTermsConditionsAccepted = "user.terms-conditions.accepted";

        // Blocked/unblocked status changed
        public const string UserBlocked = "user.blocked";
        public const string UserUnblocked = "user.unblocked";

        public const string MasterClientAdded = "master-client.added";
        public const string MasterClientLegalEntityAdded = "master-client.legal-entity-added";
        public const string MasterClientLegalEntityRemoved = "master-client.legal-entity-removed";
        public const string MasterClientUserAdded = "master-client.user-added";
        public const string MasterClientUserRemoved = "master-client.user-removed";

        public const string BeneficialOwnerDataConfirmed = "beneficial-owner-data.confirmed";
        public const string BeneficialOwnerDataUpdateRequested = "beneficial-owner-data.update-requested";
        public const string BeneficialOwnerAssistanceRequested = "beneficial-owner.assistance-requested";

        public const string DirectorDataConfirmed = "director-data.confirmed";
        public const string DirectorDataUpdateRequested = "director-data.update-requested";
        public const string DirectorAssistanceRequested = "director.assistance-requested";

        public const string ShareholderDataConfirmed = "shareholder-data.confirmed";
        public const string ShareholderDataUpdateRequested = "shareholder-data.update-requested";
        public const string ShareholderAssistanceRequested = "shareholder.assistance-requested";

        public const string CompanyActivated = "company.activated";
        public const string CompanyDeactivated = "company.deactivated";
        public const string CompanyMasterClientChanged = "company.masterclient.changed";
        public const string CompanyModuleEnabledChanged = "company.module.changed";
        public const string CompanyModuleApprovalChanged = "company.module.approvalChanged";
        public const string CompanyApproved = "company.approved";
        public const string CompanyDeclined = "company.declined";
        public const string CompanyOnboardingChanged = "company.onboarding-changed";
        public const string CompanyAnnualFeeStatusUpdated = "company.annual-fee-status-updated";

        public const string UserInvitationSentManually = "userinvitation.sent-manually";
        public const string UserInvitationSent = "userinvitation.sent";

        public const string InvoiceDeleted = "invoice.deleted";
        public const string PaymentDeleted = "payment.deleted";

        public const string SettingsSaved = "settings.saved";
        public const string LatePaymentFeeExemptUpdated = "settings.late-payment-fee-exempt-changed";
        public const string SubmissionFeeUpdated = "settings.submission-fee-changed";

        public const string SubmissionStarted = "submission.started";
        public const string SubmissionSubmitted = "submission.submitted";
        public const string SubmissionSubmitScheduled = "submission.submit-scheduled";
        public const string SubmissionReopened = "submission.reopened";
        public const string SubmissionResubmitted = "submission.resubmitted";
        public const string SubmissionFinancialPeriodChanged = "submission.financial-period-changed";
        public const string SubmissionInformationRequested = "submission.information-requested";
        public const string SubmissionInformationRequestCompleted = "submission.information-request.completed";
        public const string SubmissionInformationRequestCancelled = "submission.information-request.cancelled";
        public const string SubmissionPaidStatusUpdated = "submission.paid-status-updated";
        public const string SubmissionFormContentUpdated = "submission.form-updated";
        public const string RfiCompletedNotificationCreated = "rfi.notified-completion";
        public const string Rfi3DaysOverDueNotificationCreated = "rfi.notified-3-days-overdue";
        public const string RfiCreatedNotificationCreated = "rfi.notified-created";
        public const string RfiDueInOneWeekNotificationCreated = "rfi.notified-due-in-one-week";
        public const string RfiDueInOneDayNotificationCreated = "rfi.notified-due-in-one-day";

        // Used to log the submission deleted event.
        public const string SubmissionDeleted = "submission.deleted";

        public const string SubmissionMigrated = "submission.migrated";
        public const string InvoiceMigrated = "invoice.migrated";
        public const string PaymentMigrated = "payment.migrated";
        public const string LegalEntityMigrated = "legalentity.migrated";
        public const string FormDocumentMigrated = "formdocument.migrated";

        public const string MessageSentToEmployeeForCompany = "message.sent-for-company";

        public const string ModuleForCompanyDeactivated = "module.deactivated-for-company";
        public const string PanamaFeeSettingsSaved = "panama.fee.settings.saved";

#pragma warning restore SA1600 // Elements should be documented
#pragma warning restore CS1591 // Missing XML comment for publicly visible type or member
    }
}
