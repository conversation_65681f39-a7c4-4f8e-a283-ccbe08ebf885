﻿// <copyright file="BODirectorStatus.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Shared.Defines.BODirector
{
    /// <summary>
    /// Status constants for beneficial owner and director records.
    /// </summary>
    public static class BODirectorStatus
    {
        /// <summary>
        /// Initial status when record is first created.
        /// </summary>
        public const string Initial = "INITIAL";

        /// <summary>
        /// Status when record has been refreshed.
        /// </summary>
        public const string Refreshed = "REFRESHED";

        /// <summary>
        /// Status when VP data has been received.
        /// </summary>
        public const string VPDataReceived = "VP DATA RECEIVED";

        /// <summary>
        /// Status when an update request is pending.
        /// </summary>
        public const string PendingUpdateRequest = "PENDING UPDATE REQUEST";

        /// <summary>
        /// Status when record has been confirmed.
        /// </summary>
        public const string Confirmed = "CONFIRMED";
    }
}
