using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Documents.EFModels;
using NetProGroup.Trust.Application.Contracts.Payments.Invoices;
using NetProGroup.Trust.DataManager.Payments.Invoices;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Currencies;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Invoices : TestBase
    {
        private IInvoiceDataManager _invoiceDataManager;
        private IInvoiceRepository _invoiceRepository;
        private ISettingsManager _settingsManager;

        private Guid _jurisdictionId = new Guid("BDEF352D-DEDC-4271-888D-EFA168404CE9");

        [SetUp]
        public void Setup()
        {
            _invoiceDataManager = _server.Services.GetRequiredService<IInvoiceDataManager>();
            _invoiceRepository = _server.Services.GetRequiredService<IInvoiceRepository>();

            _settingsManager = _server.Services.GetRequiredService<ISettingsManager>();
            SetupNevisSTRInvoiceNumbering();
        }

        private void SetupNevisSTRInvoiceNumbering()
        {
            var settings = new Application.Contracts.Settings.InvoiceNumberingSettingsDTO
            {
                InitialNumber = 4000,
                JurisdictionId = _jurisdictionId,
                PrefixFormat = "{yy}{mm}",
                RangeFormat = "{yy}{mm}"
            };

            _settingsManager.SaveInvoiceNumberingSettingsAsync(_jurisdictionId, null, settings).Wait();
        }

        [Test]
        public void GetInvoiceAsync_InvoiceNotFound_ThrowsNotFoundException()
        {
            // Arrange
            var nonExistingInvoiceId = Guid.NewGuid();

            // Act & Assert
            var ex = Assert.ThrowsAsync<NotFoundException>(()
                => _invoiceDataManager.GetInvoiceAsync(nonExistingInvoiceId));
            Assert.That(ex.Message, Is.EqualTo("Invoice not found"));
        }

        [Test]
        public async Task GetInvoiceAsync_InvoiceExists_ReturnsInvoice()
        {
            // Arrange
            var existingInvoice = await CreateAndInsertTestInvoiceAsync();

            // Act
            var invoice = await _invoiceDataManager.GetInvoiceAsync(existingInvoice.Id);

            // Assert
            Assert.That(invoice, Is.Not.Null);
            Assert.That(invoice.Id, Is.EqualTo(existingInvoice.Id));
            invoice.Layout.Should().Be(LayoutConsts.TridentTrust);
        }

        [Test]
        public async Task ListInvoicesAsync_ValidRequest_ReturnsPagedInvoices()
        {
            // Arrange
            var request = new InvoiceListRequestDTO
            {
                PageNumber = 1,
                PageSize = 10,
                SortBy = "Date",
                SortOrder = "asc"
            };

            // Act
            var response = await _invoiceDataManager.ListInvoicesAsync(request);

            // Assert
            Assert.That(response.GetEnumerator(), Is.Not.Null);
            Assert.That(response, Has.Count.GreaterThanOrEqualTo(0));
        }
        [Test]
        public async Task ListInvoicesAsync_InvalidRequest_ThrowsArgumentException()
        {
            // Arrange
            InvoiceListRequestDTO request = null;

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentNullException>(()
                => _invoiceDataManager.ListInvoicesAsync(request));
            Assert.That(ex.Message, Is.EqualTo("Value cannot be null. (Parameter 'request')"));
        }

        [Test]
        public async Task ListInvoicesAsync_ValidRequestWithInsertedInvoices_ReturnsCorrectInvoices()
        {
            // Arrange
            await CreateAndInsertTestInvoiceAsync();
            await CreateAndInsertTestInvoiceAsync();

            var request = new InvoiceListRequestDTO
            {
                PageNumber = 1,
                PageSize = 10,
                SortBy = "Date",
                SortOrder = "asc",
                UserId = ClientUser.Id
            };

            // Act
            var response = await _invoiceDataManager.ListInvoicesAsync(request);

            // Assert
            Assert.That(response.GetEnumerator(), Is.Not.Null);
            Assert.That(response, Has.Count.EqualTo(2)); // Expecting 2 invoices
        }

        private async Task<Invoice> CreateAndInsertTestInvoiceAsync()
        {
            var currency = new Currency(
                id: Guid.NewGuid(),
                name: "US Dollar",
                symbol: "$",
                code: "USD"
            );

            var legalEntity = new LegalEntity(Guid.NewGuid())
            {
                Name = "NetProGroup LLC",
                Code = "NPG123",
                MasterClient = _masterClient
            };

            var document = new Document(Guid.NewGuid())
            {
                Filename = "Invoice_Document.pdf",
                Description = "Invoice related document",
                BlobPath = "path/to/blob",
                FileSize = 1024,
                AddedAt = DateTime.UtcNow
            };

            var invoice = new Invoice(Guid.NewGuid())
            {
                InvoiceNr = "INV-001",
                Date = DateTime.UtcNow,
                Amount = 100m,
                Status = DomainShared.Enums.InvoiceStatus.Pending,
                CurrencyId = currency.Id,
                Currency = currency,
                LegalEntityId = legalEntity.Id,
                LegalEntity = legalEntity,
                DocumentId = document.Id,
                Document = document,
                Layout = LayoutConsts.TridentTrust
            };

            var a = await _invoiceRepository.InsertAsync(invoice);
            await _invoiceRepository.SaveChangesAsync();
            return invoice;
        }


        // 1. Test case to verify that an invoice with lines is returned and includes the lines
        [Test]
        public async Task GetInvoiceAsync_InvoiceWithLines_ReturnsInvoiceWithLines()
        {
            // Arrange
            var invoice = await CreateAndInsertTestInvoiceWithLinesAsync();

            // Act
            var result = await _invoiceDataManager.GetInvoiceAsync(invoice.Id);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.InvoiceLines, Is.Not.Null);
            Assert.That(result.InvoiceLines.Count, Is.GreaterThan(0));
        }

        // 2. Test case to verify that an invoice without lines is returned and contains no lines
        [Test]
        public async Task GetInvoiceAsync_InvoiceWithoutLines_ReturnsInvoiceWithoutLines()
        {
            // Arrange
            var invoice = await CreateAndInsertTestInvoiceWithoutLinesAsync();

            // Act
            var result = await _invoiceDataManager.GetInvoiceAsync(invoice.Id);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.InvoiceLines, Is.Not.Null);
            Assert.That(result.InvoiceLines.Count, Is.EqualTo(0));
        }

        // Helper method to create and insert an invoice with lines
        private async Task<Invoice> CreateAndInsertTestInvoiceWithLinesAsync()
        {
            var currency = new Currency(
                id: Guid.NewGuid(),
                name: "US Dollar",
                symbol: "$",
                code: "USD"
            );

            var legalEntity = new LegalEntity(Guid.NewGuid())
            {
                Name = "NetProGroup LLC",
                Code = "NPG123",
            };

            var document = new Document(Guid.NewGuid())
            {
                Filename = "Invoice_Document.pdf",
                Description = "Invoice related document",
                BlobPath = "path/to/blob",
                FileSize = 1024,
                AddedAt = DateTime.UtcNow
            };
            var line1 = new InvoiceLine(Guid.NewGuid())
            {
                Description = "Test Line 1",
                Amount = 50m,
                Sequence = 1,
                CurrencyId = currency.Id,
                ArticleNr = InvoiceLineFee.RegularFee
            };

            var line2 = new InvoiceLine(Guid.NewGuid())
            {
                Description = "Test Line 2",
                Amount = 50m,
                Sequence = 2,
                CurrencyId = currency.Id,
                ArticleNr = InvoiceLineFee.LateFee
            };
            var invoice = new Invoice(Guid.NewGuid())
            {
                InvoiceNr = "INV-001",
                Date = DateTime.UtcNow,
                Amount = 100m,
                Status = DomainShared.Enums.InvoiceStatus.Pending,
                CurrencyId = currency.Id,
                Currency = currency,
                LegalEntityId = legalEntity.Id,
                LegalEntity = legalEntity,
                DocumentId = document.Id,
                Document = document,
                InvoiceLines = new List<InvoiceLine>()
                {
                    line1,
                    line2
                },
                Layout = LayoutConsts.TridentTrust
            };

            await _invoiceRepository.InsertAsync(invoice);
            await _invoiceRepository.SaveChangesAsync();
            return invoice;
        }

        // Helper method to create and insert an invoice without lines
        private async Task<Invoice> CreateAndInsertTestInvoiceWithoutLinesAsync()
        {
            var currency = new Currency(
                id: Guid.NewGuid(),
                name: "Euro",
                symbol: "€",
                code: "EUR"
            );

            var legalEntity = new LegalEntity(Guid.NewGuid())
            {
                Name = "NetProGroup Europe",
                Code = "NPG124",
            };

            var document = new Document(Guid.NewGuid())
            {
                Filename = "Invoice_Document_EU.pdf",
                Description = "Invoice related document",
                BlobPath = "path/to/blob",
                FileSize = 1024,
                AddedAt = DateTime.UtcNow
            };

            var invoice = new Invoice(Guid.NewGuid())
            {
                InvoiceNr = "INV-002",
                Date = DateTime.UtcNow,
                Amount = 150m,
                Status = DomainShared.Enums.InvoiceStatus.Pending,
                CurrencyId = currency.Id,
                Currency = currency,
                LegalEntityId = legalEntity.Id,
                LegalEntity = legalEntity,
                DocumentId = document.Id,
                Document = document,
                Layout = LayoutConsts.TridentTrust
            };

            await _invoiceRepository.InsertAsync(invoice);
            await _invoiceRepository.SaveChangesAsync();
            return invoice;
        }

    }
}